import 'dart:async';
import 'dart:io' as io;
import 'dart:math' as math;

import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http show ClientException;
import 'package:me_constants/me_constants.dart';
import 'package:me_l10n/me_l10n.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_ui/me_ui.dart';
import 'package:me_utils/me_utils.dart';
import 'package:solana/solana.dart' as solana show HttpException, RpcTimeoutException;
import 'package:url_launcher/url_launcher.dart' as ul show LaunchMode;
import 'package:url_launcher/url_launcher_string.dart' as uls show launchUrlString;

import '/extensions/riverpod_extension.dart';
import '/provider/api.dart' show ApiException;
import '/ui/widgets/dialog/tiny_dialog.dart';
import '/ui/widgets/toast.dart';

export 'package:url_launcher/url_launcher.dart' show LaunchMode;
export 'package:url_launcher/url_launcher_string.dart' show canLaunchUrlString;

Future<bool> launchUrlString(
  String url, {
  ul.LaunchMode mode = ul.LaunchMode.externalApplication,
  String? askToOpenText,
  String? askToOpenCaptionText,
}) async {
  if (askToOpenText case final text?) {
    await postRun(() {});
    final result = await TinyDialog.show(
      text: text,
      captionText: askToOpenCaptionText,
      buttonsBuilder: (context) => TinyDialogButtonGroup(
        confirmText: context.l10nME.openButton,
      ),
    );
    if (result != true) {
      return false;
    }
  }
  return uls.launchUrlString(url, mode: mode);
}

bool isNetworkError(Object? error) {
  if (error is DioException) {
    return switch (error.type) {
      DioExceptionType.badResponse || DioExceptionType.badCertificate => false,
      DioExceptionType.unknown => isNetworkError(error.error),
      _ => true,
    };
  }
  return error is ApiException ||
      error is solana.HttpException ||
      error is solana.RpcTimeoutException ||
      error is http.ClientException ||
      error is io.HttpException ||
      error is io.SocketException ||
      error is io.TlsException ||
      error is TimeoutException ||
      error is NetworkImageLoadException;
}

void showToastForError(Object error) {
  if (error is ApiException) {
    // Network errors only show simple networking toasts.
    showErrorToast(
      '${globalL10nME.exceptionFailed}: ${error.message} (${error.code})',
    );
  } else if (isNetworkError(error)) {
    // Network errors only show simple networking toasts.
    showErrorToast(globalL10nME.networkError);
  } else if (error is MEError) {
    // MEError errors show toasts with error codes.
    showErrorToast(error.toStringShort());
  } else {
    showErrorToast('${globalL10nME.exceptionError}: $error');
  }
}

/// Converts unformatted error/exception to readable format.
String produceReadableError(Object error) {
  return switch (error) {
    DioException() =>
      '${error.response?.realUri ?? error.requestOptions.uri}\n'
          '$error\n${error.response?.data}',
    _ => error.toString(),
  };
}

bool handleExceptions({
  FlutterErrorDetails? details,
  Object? error,
  StackTrace? stackTrace,
  bool log = true,
  bool bot = true,
  String? tag,
  bool tagWithTrace = kDebugMode,
}) {
  // If a Flutter error details presents, record with corresponding method.
  if (details != null) {
    final e = details.exception;
    if (e is NeverCatchError || e is QueueTaskCancelledError || e is RefCanceledException) {
      return true;
    }
    // Treat cancel as a normal behavior.
    if (e is DioException && e.type == DioExceptionType.cancel) {
      return true;
    }

    if (kReleaseMode) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(details);
    } else {
      FlutterError.reportError(details);
    }
    return true;
  }

  if (error is NeverCatchError || error is QueueTaskCancelledError || error is RefCanceledException) {
    return true;
  }

  // Treat cancel as a normal behavior.
  if (error is DioException && error.type == DioExceptionType.cancel) {
    return true;
  }

  if (error == null) {
    return false;
  }

  if (stackTrace == StackTrace.empty) {
    stackTrace = null;
  }

  if (log) {
    LogUtil.e(
      produceReadableError(error),
      stackTrace: stackTrace,
      tag: tag != null ? '💣 $tag' : null,
      tagWithTrace: tagWithTrace,
      level: 2,
      report: bot && !isNetworkError(error) && kReleaseMode,
      enabled: error is! ApiException,
    );
  }
  if (kReleaseMode) {
    FirebaseCrashlytics.instance.recordError(
      error,
      stackTrace,
      fatal: true,
    );
  }
  return true;
}

String udid() {
  const chars = 'qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM1234567890';
  final sb = StringBuffer();
  for (int i = 0; i < 12; ++i) {
    final o = chars[math.Random().nextInt(chars.length)];
    sb.write(o);
  }
  return sb.toString();
}

void copyAndToast(String? text, [String? toast]) {
  if (text == null || text.isEmpty) {
    return;
  }

  copy(text);

  if (toast != null && toast.isNotEmpty) {
    showToast(toast);
    return;
  }

  final context = meNavigatorKey.currentState?.overlay?.context;
  if (context == null) {
    return;
  }

  Card3ToastUtil.showToast(message: ToastMessages.copied);
}
