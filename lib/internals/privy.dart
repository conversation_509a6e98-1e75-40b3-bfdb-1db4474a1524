import 'dart:async';

import 'package:privy_flutter/privy_flutter.dart';

import '/constants/envs.dart' show envApiIdPrivy, envApiIdPrivyClient, isSealed, envIsProd;

export 'package:privy_flutter/privy_flutter.dart' show AuthStateExtension, PrivyUser, Result, Success, Failure;

typedef PrivyAuthStateAuthenticated = Authenticated;
typedef PrivyAuthStateUnauthenticated = Unauthenticated;

final privyConfig = PrivyConfig(
  appId: envApiIdPrivy,
  appClientId: envApiIdPrivyClient,
  logLevel: isSealed ? PrivyLogLevel.info : PrivyLogLevel.verbose,
);
final privyClient = Privy.init(config: privyConfig);

Future<Result<PrivyUser>> privyGuestAuthenticate() async {
  final email = envIsProd ? '<EMAIL>' : '<EMAIL>';
  final code = envIsProd ? '846675' : '187202';

  final result = await privyClient.email.sendCode(email);
  final completer = Completer<Result<PrivyUser>>();
  result.fold(
    onSuccess: (_) {
      try {
        final result = privyClient.email.loginWithCode(code: code, email: email);
        completer.complete(result);
      } catch (e, s) {
        completer.completeError(e, s);
      }
    },
    onFailure: (error) {
      completer.completeError(error);
    },
  );
  return completer.future;
}

extension PrivyUserExtension on PrivyUser {
  bool get hasAllWallets => embeddedEthereumWallets.isNotEmpty || embeddedSolanaWallets.isNotEmpty;
}
