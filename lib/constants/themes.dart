import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart' hide themeBy;
import 'package:me_constants/me_constants.dart' as c show themeBy;

import '/res/colors.gen.dart';
import '/res/fonts.gen.dart';

const METheme defaultMEThemeDark = METheme(
  brightness: Brightness.dark,
  themeColor: ColorName.themeColorDark,
  backgroundColor: ColorName.backgroundColorDark,
  cardColor: ColorName.cardColorDark,
  dividerColor: ColorName.dividerColorDark,
  iconColor: ColorName.iconColorDark,
  listColor: ColorName.listColorDark,
  primaryTextColor: ColorName.primaryTextColorDark,
  captionTextColor: ColorName.captionTextColorDark,
  blueGreyIconColor: ColorName.blueGreyIconColorDark,
  shimmerHighlightColor: ColorName.shimmerHighlightColorDark,
  successColor: ColorName.successColor,
  failingColor: ColorName.failingColor,
  notificationColor: ColorName.notificationColor,
  borderRadius: BorderRadius.all(Radius.circular(16.0)),
  fontFamilyExtraFallbacks: [FontFamily.harmonyOSSans],
  fontFamilyLatin: FontFamily.harmonyOSSans,
  fontHeightLatin: 1.2,
);

const METheme defaultMEThemeLight = METheme(
  brightness: Brightness.light,
  themeColor: ColorName.themeColorLight,
  backgroundColor: ColorName.backgroundColorLight,
  cardColor: ColorName.cardColorLight,
  dividerColor: ColorName.dividerColorLight,
  iconColor: ColorName.iconColorLight,
  listColor: ColorName.listColorLight,
  primaryTextColor: ColorName.primaryTextColorLight,
  captionTextColor: ColorName.captionTextColorLight,
  blueGreyIconColor: ColorName.blueGreyIconColorLight,
  shimmerHighlightColor: ColorName.shimmerHighlightColorLight,
  successColor: ColorName.successColor,
  failingColor: ColorName.failingColor,
  notificationColor: ColorName.notificationColor,
  borderRadius: BorderRadius.all(Radius.circular(16.0)),
  fontFamilyExtraFallbacks: [FontFamily.harmonyOSSans],
  fontFamilyLatin: FontFamily.harmonyOSSans,
  fontHeightLatin: 1.2,
);

ThemeData themeBy({
  required METheme meTheme,
  required Locale locale,
}) {
  ThemeData theme = c.themeBy(
    meTheme: meTheme,
    locale: locale,
  );
  final baseInputBorder = const OutlineInputBorder(
    borderSide: BorderSide.none,
    borderRadius: BorderRadius.all(Radius.circular(20.0)),
  );
  theme = theme.copyWith(
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: _FadeForwardsPageTransitionsBuilder(),
        TargetPlatform.linux: _FadeForwardsPageTransitionsBuilder(),
        TargetPlatform.windows: _FadeForwardsPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
      },
    ),
    colorScheme: theme.colorScheme.copyWith(surface: meTheme.listColor),
    inputDecorationTheme: theme.inputDecorationTheme.copyWith(
      filled: true,
      fillColor: meTheme.cardColor,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      border: baseInputBorder,
      enabledBorder: baseInputBorder,
      disabledBorder: baseInputBorder,
      focusedBorder: baseInputBorder.copyWith(
        borderSide: BorderSide(color: meTheme.themeColor, width: 2.0),
      ),
      focusedErrorBorder: baseInputBorder.copyWith(
        borderSide: BorderSide(color: meTheme.failingColor, width: 2.0),
      ),
      errorBorder: baseInputBorder.copyWith(
        borderSide: BorderSide(color: meTheme.failingColor),
      ),
    ),
    bottomSheetTheme: theme.bottomSheetTheme.copyWith(
      backgroundColor: meTheme.cardColor,
      modalBackgroundColor: meTheme.cardColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(10.0),
        ),
      ),
    ),
  );
  return theme;
}

final class _FadeForwardsPageTransitionsBuilder extends FadeForwardsPageTransitionsBuilder {
  const _FadeForwardsPageTransitionsBuilder();

  @override
  Duration get transitionDuration => const Duration(milliseconds: 500);
}
