/// Toast消息管理类
/// 统一管理应用中所有的Toast消息
abstract final class ToastMessages {
  static const String copied = 'Copied';
  static const String updated = 'Updated';

  // 登录相关
  static const String loginTermsRequired = 'Please agree to the Terms and Privacy Policy';
  static const String creatingWallet = 'Creating your wallet...';
  static const String creatingWalletFailed = 'Failed to create wallet. Please try again later.';
  static const String failedToSendEmailCode = 'Failed to send login code. Please try again later.';
  static const String invalidOrExpiredLoginCode = 'Invalid or expired login code';
  static const String emailCodeResent = 'The login code has been sent';
  static const String failedToResendEmailCode = 'Failed to send login code. Please try again later.';

  // 卡片相关
  static const String nfcNotAvailable = 'Your device does not enabled NFC feature or does not support NFC.';
  static const String cardAlreadyActivated = 'Oops! It is already activated';
  static const String invalidCardFormat = 'Invalid data format';
  static const String cardActivatedSuccessfully = 'Activation complete';
  static const String cardNotActivated = 'The card has not been activated';

  // 定制相关
  static const String failedToGetCardInfo = 'Error when getting the card info. Please try again later.';

  static String failedToGetCoverInfo({
    required String code,
  }) => 'Unable to get cover info ($code). Please try again later.';

  static const String pleaseUploadYourPicture = 'Upload your profile picture';
  static const String failedToProcessImage =
      'Failed to process image. '
      'Please check the format (JPG or PNG) and size (<10 MB).';
  static const String failedToCropImage = 'Crop failed';

  // 社交相关
  static const String failedToSubmit = 'Failed to submit. Please try again later.';
  static const String failedToDelete = 'Failed to delete data. Please try again later.';

  // 个人资料相关
  static const String profileCleared = 'Profile data cleared';
  static const String failedToClearProfile = 'Failed to clear profile. Please try again later.';
  static const String uploadAvatarSuccess = 'Upload successful';
  static const String uploadAvatarError = 'Failed to upload avatar. Please try again later.';
  static const String failedToOpenImagePicker = 'Unable to select image. Please try again later.';
  static const String failedToCreateCardCover = 'Failed to upload avatar. Please try again later.';
  static const String failedToUpdate = 'Failed to update. Please try again later.';

  // 账户相关
  static const String joinToAccess = 'Join Card3 to access features!';
  static const String deleted = 'Account deleted';
  static const String deleteAccountFailed = 'Failed to delete account. Please try again later.';
  static const String modeChangedSuccessfully = 'Updated';
  static const String failedToChangeMode = 'Failed to change mode. Please try again later.';
  static const String notValidReferralQrCode = 'The QR code is invalid for referral.';

  // 网络相关
  static const String failedToSwitchNetwork = 'Failed to switch network';
  static const String failedToLoadNotifications = 'Failed to load notifications';
  static const String failedToLoadMore = 'Failed to load more';

  // 动态错误消息生成方法
  static String creatingWalletFailedWithDetails(String error) {
    final shortError = error.length > 50 ? '${error.substring(0, 50)}...' : error;
    return 'Creating wallet failed: $shortError';
  }

  static String loginFailed(String error) => 'Login failed: $error';

  static String activationError(String error) => 'Activation error: $error';

  static String couldNotLaunch(String url) => 'Could not launch $url';
}
