import 'package:collection/collection.dart';

import 'release.dart';

const tokenSOLAddress = 'So11111111111111111111111111111111111111111';
const tokenSOLWrappedAddress = 'So11111111111111111111111111111111111111112';

const globalDeepCollectionEquality = DeepCollectionEquality();

enum AppFlavor {
  app,
  beta;

  bool get isBeta => this == beta;
}

final flavor = switch (Release.appFlavor) {
  'app' => AppFlavor.app,
  'beta' => AppFlavor.beta,
  final value => throw UnsupportedError('Unsupported flavor ($value)'),
};

enum CustomizeViewType { start, main, confirm, pay, submitted }
// // 链特定的token过滤配置
// const chainTokenFilters = {
//   // 以太坊 (chainId: 1)
//   '1': ['ETH', 'USDC', 'USDT'],
//   // Base Mainnet (chainId: 8453)

//   '8453': ['ETH', 'USDT', 'USDC', 'DEGEN'],

//   // Polygon (chainId: 137)
//   '137': ['POL', 'USDC', 'USDT'],

//   // BSC (chainId: 56)
//   '56': ['BNB', 'BUSD', 'WBNB'],

//   // Arbitrum (chainId: 42161)
//   '42161': ['ETH', 'USDC', 'USDT'],

//   // Optimism (chainId: 10)
//   '10': ['ETH', 'USDC', 'USDT'],

//   // Avalanche (chainId: 43114)
//   '43114': ['ETH', 'USDC',  'USDT'],

//   // Linea (chainId: 59144)
//   '59144': ['ETH', 'USDC',  'USDT'],

//   // 其他链不过滤，显示所有token

// };
