import 'package:ff_annotation_route_library/ff_annotation_route_library.dart' show onGenerateRoute;
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:flutter/gestures.dart' show PointerDeviceKind;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart' show FlutterSmartDialog;
import 'package:me_constants/me_constants.dart' show METhemeBuildContextExtension;
import 'package:me_l10n/me_l10n.dart' show MELocalizations;
import 'package:me_misc/me_misc.dart' as misc;
import 'package:me_ui/me_ui.dart';

import 'constants/constants.dart' show flavor, AppFlavor;
import 'constants/env.dart' show Env;
import 'constants/envs.dart' show env, isAuditing, isSealed, EnvOverrides;
import 'constants/themes.dart' show defaultMEThemeLight, defaultMEThemeDark, themeBy;
import 'extensions/build_context_extension.dart';
import 'l10n/gen/app_localizations.dart';
import 'provider/settings.dart' show settingsProvider;
import 'res/colors.gen.dart';
import 'routes/card3_route.dart' show getRouteSettings;
import 'routes/card3_routes.dart' show Routes;

class Card3App extends ConsumerWidget {
  const Card3App({super.key});

  /// Turn this value into a getter to simulate device's size.
  /// This field only works under the [kDebugMode].
  // Size? get _fixedDeviceSize => const Size(320, 600);
  Size? get _fixedDeviceSize => null;

  void _configureUIConfig(BuildContext context) {
    MEUIConfig.stepsDialogConfig = const MEUIStepsDialogConfig(
      loadingGradient: LinearGradient(
        colors: [Color(0xffd0e754), Color(0xffd0e754)],
      ),
      succeedGradient: LinearGradient(
        colors: [Color(0xffd0e754), Color(0xffd0e754)],
      ),
    );
    MEUIConfig.themeTextButtonConfig = MEUIThemeTextButtonConfig(
      borderRadius: const BorderRadius.all(Radius.circular(20.0)),
      groupOutlinedCancel: true,
      groupButtonsGap: 10.0,
      groupCancelButtonColor: context.meTheme.cardColor,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const locale = Locale('en');
    final themeMode = ref.watch(settingsProvider.select((value) => value.themeMode));
    return MaterialApp(
      onGenerateTitle: (context) => switch (flavor) {
        AppFlavor.app => context.l10n.appTitle,
        AppFlavor.beta => context.l10n.appTitleBeta,
      },
      builder: FlutterSmartDialog.init(
        builder: (context, child) => _builder(context, ref, child),
      ),
      navigatorKey: misc.meNavigatorKey,
      scrollBehavior: const ScrollBehavior().copyWith(
        dragDevices: PointerDeviceKind.values.toSet(),
        scrollbars: false,
        overscroll: false,
      ),
      locale: locale,
      supportedLocales: AppLocalizations.supportedLocales,
      localizationsDelegates: const [
        MELocalizations.delegate,
        ...AppLocalizations.localizationsDelegates,
      ],
      navigatorObservers: <NavigatorObserver>[
        misc.meNavigatorObserver,
        misc.meRouteObserver,
      ],
      themeMode: themeMode,
      theme: themeBy(
        meTheme: defaultMEThemeLight,
        locale: locale,
      ),
      darkTheme: themeBy(
        meTheme: defaultMEThemeDark,
        locale: locale,
      ),
      initialRoute: Routes.root.name,
      onGenerateRoute: (RouteSettings settings) => onGenerateRoute(
        settings: settings,
        getRouteSettings: getRouteSettings,
        notFoundPageBuilder: () => Container(
          alignment: Alignment.center,
          color: Colors.black,
          child: Text(
            '${settings.name ?? 'Unknown'} route not found',
            style: const TextStyle(color: Colors.white, inherit: false),
          ),
        ),
      ),
    );
  }

  Widget _builder(BuildContext context, WidgetRef ref, Widget? child) {
    child = LogConsole.wrap(
      GestureDetector(onTap: misc.hideKeyboard, child: child),
      enable: !kDebugMode && !isSealed,
    );
    child = BrightnessLayer(
      child: RepaintBoundary(
        key: misc.meRepaintBoundaryKey,
        child: child,
      ),
    );
    if (!isSealed) {
      child = _buildEnv(context, child);
    }
    // Constraint the builder as a simulated device.
    if (_fixedDeviceSize != null && kDebugMode) {
      child = Align(
        alignment: Alignment.bottomCenter,
        child: DecoratedBox(
          position: DecorationPosition.foreground,
          decoration: BoxDecoration(
            border: Border.all(color: ColorName.themeColorDark),
          ),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(size: _fixedDeviceSize),
            child: ClipRect(
              child: ConstrainedBox(
                constraints: BoxConstraints.tight(_fixedDeviceSize!),
                child: child,
              ),
            ),
          ),
        ),
      );
    }
    _configureUIConfig(context);
    return child;
  }

  Widget _buildEnv(BuildContext context, Widget child) {
    final buffer = StringBuffer(env.env.toUpperCase());
    final flags = <String>[
      if (isAuditing) 'A',
      if (Env.overrode) 'O',
      if (EnvOverrides.sealedOverrode) 'S',
      if (EnvOverrides.jwtOverrode != null) 'J',
    ];
    if (flags.isNotEmpty) {
      buffer.write('(${flags.join()})');
    }
    return Banner(
      message: buffer.toString(),
      location: BannerLocation.bottomStart,
      color: context.themeColor.withValues(alpha: 0.5),
      shadow: BoxShadow(color: context.themeColor.withValues(alpha: 0.5)),
      child: child,
    );
  }
}
