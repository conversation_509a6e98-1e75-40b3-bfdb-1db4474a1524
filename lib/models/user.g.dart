// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserInfo _$UserInfoFromJson(Map json) => _UserInfo(
  userEmail: json['userEmail'] as String? ?? '',
  handle: json['handle'] as String? ?? '',
  name: json['name'] as String? ?? '',
  title: json['title'] as String? ?? '',
  company: json['company'] as String? ?? '',
  avatar: json['avatar'] as String? ?? '',
  dynamicAvatar: json['dynamicAvatar'] as String? ?? '',
  referralCode: json['referralCode'] as String? ?? '',
  cardCode: json['cardCode'] as String? ?? '',
  lastMessageId: (json['lastMessageId'] as num?)?.toInt() ?? 0,
  latestMessageId: (json['latestMessageId'] as num?)?.toInt() ?? 0,
  integralPoints: (json['integral'] as num?)?.toInt() ?? 0,
  firstConnectDate: json['firstConnectDate'] == null
      ? null
      : DateTime.parse(json['firstConnectDate'] as String),
);

Map<String, dynamic> _$UserInfoToJson(_UserInfo instance) => <String, dynamic>{
  'userEmail': instance.userEmail,
  'handle': instance.handle,
  'name': instance.name,
  'title': instance.title,
  'company': instance.company,
  'avatar': instance.avatar,
  'dynamicAvatar': instance.dynamicAvatar,
  'referralCode': instance.referralCode,
  'cardCode': instance.cardCode,
  'lastMessageId': instance.lastMessageId,
  'latestMessageId': instance.latestMessageId,
  'integral': instance.integralPoints,
  'firstConnectDate': instance.firstConnectDate?.toIso8601String(),
};

_Group _$GroupFromJson(Map json) => _Group(
  id: json['uniqId'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  logo: json['logo'] as String? ?? '',
  userCount: (json['userCount'] as num?)?.toInt() ?? 1,
  creatorName: json['creator'] as String,
);

Map<String, dynamic> _$GroupToJson(_Group instance) => <String, dynamic>{
  'uniqId': instance.id,
  'name': instance.name,
  'description': instance.description,
  'logo': instance.logo,
  'userCount': instance.userCount,
  'creator': instance.creatorName,
};

_UserReferralRequest _$UserReferralRequestFromJson(Map json) =>
    _UserReferralRequest(
      referralCode: json['referralCode'] as String?,
      cardCode: json['cardCode'] as String?,
      handle: json['handle'] as String?,
    );

Map<String, dynamic> _$UserReferralRequestToJson(
  _UserReferralRequest instance,
) => <String, dynamic>{
  'referralCode': instance.referralCode,
  'cardCode': instance.cardCode,
  'handle': instance.handle,
};

_UserSettingsRequest _$UserSettingsRequestFromJson(Map json) =>
    _UserSettingsRequest(
      fcmAndroid: json['fcmAndroid'] as String?,
      fcmIOS: json['fcmIos'] as String?,
    );

Map<String, dynamic> _$UserSettingsRequestToJson(
  _UserSettingsRequest instance,
) => <String, dynamic>{
  if (instance.fcmAndroid case final value?) 'fcmAndroid': value,
  if (instance.fcmIOS case final value?) 'fcmIos': value,
};

_UserRelation _$UserRelationFromJson(Map json) => _UserRelation(
  following: json['following'] as bool? ?? false,
  followedBy: json['followedBy'] as bool? ?? false,
);

Map<String, dynamic> _$UserRelationToJson(_UserRelation instance) =>
    <String, dynamic>{
      'following': instance.following,
      'followedBy': instance.followedBy,
    };

_UserFromRelation _$UserFromRelationFromJson(Map json) => _UserFromRelation(
  referralCode: json['referralCode'] as String,
  name: json['name'] as String? ?? '',
  title: json['title'] as String? ?? '',
  company: json['company'] as String? ?? '',
  avatar: json['avatar'] as String? ?? '',
  dynamicAvatar: json['dynamicAvatar'] as String? ?? '',
  following: json['following'] as bool? ?? false,
  followedBy: json['followedBy'] as bool? ?? false,
);

Map<String, dynamic> _$UserFromRelationToJson(_UserFromRelation instance) =>
    <String, dynamic>{
      'referralCode': instance.referralCode,
      'name': instance.name,
      'title': instance.title,
      'company': instance.company,
      'avatar': instance.avatar,
      'dynamicAvatar': instance.dynamicAvatar,
      'following': instance.following,
      'followedBy': instance.followedBy,
    };
