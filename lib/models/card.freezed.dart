// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'card.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Social {
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'platformName')
  String get platformName;
  @JsonKey(name: 'platformUrl')
  String get platformUrl;
  @JsonKey(name: 'handleName')
  String get handleName;
  @JsonKey(name: 'qrcode')
  String get imageUrl;
  @JsonKey(name: 'isVerify')
  bool get verified;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SocialCopyWith<Social> get copyWith =>
      _$SocialCopyWithImpl<Social>(this as Social, _$identity);

  /// Serializes this Social to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Social &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.platformName, platformName) ||
                other.platformName == platformName) &&
            (identical(other.platformUrl, platformUrl) ||
                other.platformUrl == platformUrl) &&
            (identical(other.handleName, handleName) ||
                other.handleName == handleName) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.verified, verified) ||
                other.verified == verified));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    platformName,
    platformUrl,
    handleName,
    imageUrl,
    verified,
  );

  @override
  String toString() {
    return 'Social(id: $id, platformName: $platformName, platformUrl: $platformUrl, handleName: $handleName, imageUrl: $imageUrl, verified: $verified)';
  }
}

/// @nodoc
abstract mixin class $SocialCopyWith<$Res> {
  factory $SocialCopyWith(Social value, $Res Function(Social) _then) =
      _$SocialCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'platformName') String platformName,
    @JsonKey(name: 'platformUrl') String platformUrl,
    @JsonKey(name: 'handleName') String handleName,
    @JsonKey(name: 'qrcode') String imageUrl,
    @JsonKey(name: 'isVerify') bool verified,
  });
}

/// @nodoc
class _$SocialCopyWithImpl<$Res> implements $SocialCopyWith<$Res> {
  _$SocialCopyWithImpl(this._self, this._then);

  final Social _self;
  final $Res Function(Social) _then;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? platformName = null,
    Object? platformUrl = null,
    Object? handleName = null,
    Object? imageUrl = null,
    Object? verified = null,
  }) {
    return _then(
      _self.copyWith(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        platformName: null == platformName
            ? _self.platformName
            : platformName // ignore: cast_nullable_to_non_nullable
                  as String,
        platformUrl: null == platformUrl
            ? _self.platformUrl
            : platformUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        handleName: null == handleName
            ? _self.handleName
            : handleName // ignore: cast_nullable_to_non_nullable
                  as String,
        imageUrl: null == imageUrl
            ? _self.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        verified: null == verified
            ? _self.verified
            : verified // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [Social].
extension SocialPatterns on Social {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Social value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Social() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Social value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Social():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Social value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Social() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'platformName') String platformName,
      @JsonKey(name: 'platformUrl') String platformUrl,
      @JsonKey(name: 'handleName') String handleName,
      @JsonKey(name: 'qrcode') String imageUrl,
      @JsonKey(name: 'isVerify') bool verified,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Social() when $default != null:
        return $default(
          _that.id,
          _that.platformName,
          _that.platformUrl,
          _that.handleName,
          _that.imageUrl,
          _that.verified,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'platformName') String platformName,
      @JsonKey(name: 'platformUrl') String platformUrl,
      @JsonKey(name: 'handleName') String handleName,
      @JsonKey(name: 'qrcode') String imageUrl,
      @JsonKey(name: 'isVerify') bool verified,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Social():
        return $default(
          _that.id,
          _that.platformName,
          _that.platformUrl,
          _that.handleName,
          _that.imageUrl,
          _that.verified,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'platformName') String platformName,
      @JsonKey(name: 'platformUrl') String platformUrl,
      @JsonKey(name: 'handleName') String handleName,
      @JsonKey(name: 'qrcode') String imageUrl,
      @JsonKey(name: 'isVerify') bool verified,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Social() when $default != null:
        return $default(
          _that.id,
          _that.platformName,
          _that.platformUrl,
          _that.handleName,
          _that.imageUrl,
          _that.verified,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _Social implements Social {
  const _Social({
    @JsonKey(name: 'id') required this.id,
    @JsonKey(name: 'platformName') this.platformName = '',
    @JsonKey(name: 'platformUrl') this.platformUrl = '',
    @JsonKey(name: 'handleName') this.handleName = '',
    @JsonKey(name: 'qrcode') this.imageUrl = '',
    @JsonKey(name: 'isVerify') this.verified = false,
  });
  factory _Social.fromJson(Map<String, dynamic> json) => _$SocialFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'platformName')
  final String platformName;
  @override
  @JsonKey(name: 'platformUrl')
  final String platformUrl;
  @override
  @JsonKey(name: 'handleName')
  final String handleName;
  @override
  @JsonKey(name: 'qrcode')
  final String imageUrl;
  @override
  @JsonKey(name: 'isVerify')
  final bool verified;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SocialCopyWith<_Social> get copyWith =>
      __$SocialCopyWithImpl<_Social>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SocialToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Social &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.platformName, platformName) ||
                other.platformName == platformName) &&
            (identical(other.platformUrl, platformUrl) ||
                other.platformUrl == platformUrl) &&
            (identical(other.handleName, handleName) ||
                other.handleName == handleName) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.verified, verified) ||
                other.verified == verified));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    platformName,
    platformUrl,
    handleName,
    imageUrl,
    verified,
  );

  @override
  String toString() {
    return 'Social(id: $id, platformName: $platformName, platformUrl: $platformUrl, handleName: $handleName, imageUrl: $imageUrl, verified: $verified)';
  }
}

/// @nodoc
abstract mixin class _$SocialCopyWith<$Res> implements $SocialCopyWith<$Res> {
  factory _$SocialCopyWith(_Social value, $Res Function(_Social) _then) =
      __$SocialCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'platformName') String platformName,
    @JsonKey(name: 'platformUrl') String platformUrl,
    @JsonKey(name: 'handleName') String handleName,
    @JsonKey(name: 'qrcode') String imageUrl,
    @JsonKey(name: 'isVerify') bool verified,
  });
}

/// @nodoc
class __$SocialCopyWithImpl<$Res> implements _$SocialCopyWith<$Res> {
  __$SocialCopyWithImpl(this._self, this._then);

  final _Social _self;
  final $Res Function(_Social) _then;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? platformName = null,
    Object? platformUrl = null,
    Object? handleName = null,
    Object? imageUrl = null,
    Object? verified = null,
  }) {
    return _then(
      _Social(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        platformName: null == platformName
            ? _self.platformName
            : platformName // ignore: cast_nullable_to_non_nullable
                  as String,
        platformUrl: null == platformUrl
            ? _self.platformUrl
            : platformUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        handleName: null == handleName
            ? _self.handleName
            : handleName // ignore: cast_nullable_to_non_nullable
                  as String,
        imageUrl: null == imageUrl
            ? _self.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        verified: null == verified
            ? _self.verified
            : verified // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

CardInfoBasic _$CardInfoBasicFromJson(Map<String, dynamic> json) {
  switch (json['runtimeType']) {
    case 'activated':
      return CardInfoBasicActivated.fromJson(json);
    case 'inactivated':
      return CardInfoBasicInactivated.fromJson(json);

    default:
      throw CheckedFromJsonException(
        json,
        'runtimeType',
        'CardInfoBasic',
        'Invalid union type "${json['runtimeType']}"!',
      );
  }
}

/// @nodoc
mixin _$CardInfoBasic {
  @JsonKey(name: 'activated')
  bool get activated;
  @JsonKey(name: 'redirectUrl')
  String get redirectUrl;

  /// Create a copy of CardInfoBasic
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CardInfoBasicCopyWith<CardInfoBasic> get copyWith =>
      _$CardInfoBasicCopyWithImpl<CardInfoBasic>(
        this as CardInfoBasic,
        _$identity,
      );

  /// Serializes this CardInfoBasic to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CardInfoBasic &&
            (identical(other.activated, activated) ||
                other.activated == activated) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, activated, redirectUrl);

  @override
  String toString() {
    return 'CardInfoBasic(activated: $activated, redirectUrl: $redirectUrl)';
  }
}

/// @nodoc
abstract mixin class $CardInfoBasicCopyWith<$Res> {
  factory $CardInfoBasicCopyWith(
    CardInfoBasic value,
    $Res Function(CardInfoBasic) _then,
  ) = _$CardInfoBasicCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'activated') bool activated,
    @JsonKey(name: 'redirectUrl') String redirectUrl,
  });
}

/// @nodoc
class _$CardInfoBasicCopyWithImpl<$Res>
    implements $CardInfoBasicCopyWith<$Res> {
  _$CardInfoBasicCopyWithImpl(this._self, this._then);

  final CardInfoBasic _self;
  final $Res Function(CardInfoBasic) _then;

  /// Create a copy of CardInfoBasic
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? activated = null, Object? redirectUrl = null}) {
    return _then(
      _self.copyWith(
        activated: null == activated
            ? _self.activated
            : activated // ignore: cast_nullable_to_non_nullable
                  as bool,
        redirectUrl: null == redirectUrl
            ? _self.redirectUrl
            : redirectUrl // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [CardInfoBasic].
extension CardInfoBasicPatterns on CardInfoBasic {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CardInfoBasicActivated value)? activated,
    TResult Function(CardInfoBasicInactivated value)? inactivated,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case CardInfoBasicActivated() when activated != null:
        return activated(_that);
      case CardInfoBasicInactivated() when inactivated != null:
        return inactivated(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CardInfoBasicActivated value) activated,
    required TResult Function(CardInfoBasicInactivated value) inactivated,
  }) {
    final _that = this;
    switch (_that) {
      case CardInfoBasicActivated():
        return activated(_that);
      case CardInfoBasicInactivated():
        return inactivated(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CardInfoBasicActivated value)? activated,
    TResult? Function(CardInfoBasicInactivated value)? inactivated,
  }) {
    final _that = this;
    switch (_that) {
      case CardInfoBasicActivated() when activated != null:
        return activated(_that);
      case CardInfoBasicInactivated() when inactivated != null:
        return inactivated(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      @JsonKey(name: 'activated') bool activated,
      @JsonKey(name: 'redirectUrl') String redirectUrl,
      @JsonKey(name: 'referralCode') String referralCode,
    )?
    activated,
    TResult Function(
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'activated') bool activated,
      @JsonKey(name: 'cardType') CardType cardType,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'redirectUrl') String redirectUrl,
    )?
    inactivated,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case CardInfoBasicActivated() when activated != null:
        return activated(
          _that.activated,
          _that.redirectUrl,
          _that.referralCode,
        );
      case CardInfoBasicInactivated() when inactivated != null:
        return inactivated(
          _that.id,
          _that.activated,
          _that.cardType,
          _that.backCover,
          _that.redirectUrl,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      @JsonKey(name: 'activated') bool activated,
      @JsonKey(name: 'redirectUrl') String redirectUrl,
      @JsonKey(name: 'referralCode') String referralCode,
    )
    activated,
    required TResult Function(
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'activated') bool activated,
      @JsonKey(name: 'cardType') CardType cardType,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'redirectUrl') String redirectUrl,
    )
    inactivated,
  }) {
    final _that = this;
    switch (_that) {
      case CardInfoBasicActivated():
        return activated(
          _that.activated,
          _that.redirectUrl,
          _that.referralCode,
        );
      case CardInfoBasicInactivated():
        return inactivated(
          _that.id,
          _that.activated,
          _that.cardType,
          _that.backCover,
          _that.redirectUrl,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      @JsonKey(name: 'activated') bool activated,
      @JsonKey(name: 'redirectUrl') String redirectUrl,
      @JsonKey(name: 'referralCode') String referralCode,
    )?
    activated,
    TResult? Function(
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'activated') bool activated,
      @JsonKey(name: 'cardType') CardType cardType,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'redirectUrl') String redirectUrl,
    )?
    inactivated,
  }) {
    final _that = this;
    switch (_that) {
      case CardInfoBasicActivated() when activated != null:
        return activated(
          _that.activated,
          _that.redirectUrl,
          _that.referralCode,
        );
      case CardInfoBasicInactivated() when inactivated != null:
        return inactivated(
          _that.id,
          _that.activated,
          _that.cardType,
          _that.backCover,
          _that.redirectUrl,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class CardInfoBasicActivated implements CardInfoBasic, ICardInfoActivated {
  const CardInfoBasicActivated({
    @JsonKey(name: 'activated') required this.activated,
    @JsonKey(name: 'redirectUrl') this.redirectUrl = '',
    @JsonKey(name: 'referralCode') required this.referralCode,
    final String? $type,
  }) : $type = $type ?? 'activated';
  factory CardInfoBasicActivated.fromJson(Map<String, dynamic> json) =>
      _$CardInfoBasicActivatedFromJson(json);

  @override
  @JsonKey(name: 'activated')
  final bool activated;
  @override
  @JsonKey(name: 'redirectUrl')
  final String redirectUrl;
  @JsonKey(name: 'referralCode')
  final String referralCode;

  @JsonKey(name: 'runtimeType')
  final String $type;

  /// Create a copy of CardInfoBasic
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CardInfoBasicActivatedCopyWith<CardInfoBasicActivated> get copyWith =>
      _$CardInfoBasicActivatedCopyWithImpl<CardInfoBasicActivated>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$CardInfoBasicActivatedToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CardInfoBasicActivated &&
            (identical(other.activated, activated) ||
                other.activated == activated) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, activated, redirectUrl, referralCode);

  @override
  String toString() {
    return 'CardInfoBasic.activated(activated: $activated, redirectUrl: $redirectUrl, referralCode: $referralCode)';
  }
}

/// @nodoc
abstract mixin class $CardInfoBasicActivatedCopyWith<$Res>
    implements $CardInfoBasicCopyWith<$Res> {
  factory $CardInfoBasicActivatedCopyWith(
    CardInfoBasicActivated value,
    $Res Function(CardInfoBasicActivated) _then,
  ) = _$CardInfoBasicActivatedCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'activated') bool activated,
    @JsonKey(name: 'redirectUrl') String redirectUrl,
    @JsonKey(name: 'referralCode') String referralCode,
  });
}

/// @nodoc
class _$CardInfoBasicActivatedCopyWithImpl<$Res>
    implements $CardInfoBasicActivatedCopyWith<$Res> {
  _$CardInfoBasicActivatedCopyWithImpl(this._self, this._then);

  final CardInfoBasicActivated _self;
  final $Res Function(CardInfoBasicActivated) _then;

  /// Create a copy of CardInfoBasic
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? activated = null,
    Object? redirectUrl = null,
    Object? referralCode = null,
  }) {
    return _then(
      CardInfoBasicActivated(
        activated: null == activated
            ? _self.activated
            : activated // ignore: cast_nullable_to_non_nullable
                  as bool,
        redirectUrl: null == redirectUrl
            ? _self.redirectUrl
            : redirectUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class CardInfoBasicInactivated implements CardInfoBasic {
  const CardInfoBasicInactivated({
    @JsonKey(name: 'id') required this.id,
    @JsonKey(name: 'activated') required this.activated,
    @JsonKey(name: 'cardType') required this.cardType,
    @JsonKey(name: 'backCover') this.backCover = '',
    @JsonKey(name: 'redirectUrl') this.redirectUrl = '',
    final String? $type,
  }) : $type = $type ?? 'inactivated';
  factory CardInfoBasicInactivated.fromJson(Map<String, dynamic> json) =>
      _$CardInfoBasicInactivatedFromJson(json);

  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'activated')
  final bool activated;
  @JsonKey(name: 'cardType')
  final CardType cardType;
  @JsonKey(name: 'backCover')
  final String backCover;
  @override
  @JsonKey(name: 'redirectUrl')
  final String redirectUrl;

  @JsonKey(name: 'runtimeType')
  final String $type;

  /// Create a copy of CardInfoBasic
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CardInfoBasicInactivatedCopyWith<CardInfoBasicInactivated> get copyWith =>
      _$CardInfoBasicInactivatedCopyWithImpl<CardInfoBasicInactivated>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$CardInfoBasicInactivatedToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CardInfoBasicInactivated &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.activated, activated) ||
                other.activated == activated) &&
            (identical(other.cardType, cardType) ||
                other.cardType == cardType) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, activated, cardType, backCover, redirectUrl);

  @override
  String toString() {
    return 'CardInfoBasic.inactivated(id: $id, activated: $activated, cardType: $cardType, backCover: $backCover, redirectUrl: $redirectUrl)';
  }
}

/// @nodoc
abstract mixin class $CardInfoBasicInactivatedCopyWith<$Res>
    implements $CardInfoBasicCopyWith<$Res> {
  factory $CardInfoBasicInactivatedCopyWith(
    CardInfoBasicInactivated value,
    $Res Function(CardInfoBasicInactivated) _then,
  ) = _$CardInfoBasicInactivatedCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'activated') bool activated,
    @JsonKey(name: 'cardType') CardType cardType,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'redirectUrl') String redirectUrl,
  });
}

/// @nodoc
class _$CardInfoBasicInactivatedCopyWithImpl<$Res>
    implements $CardInfoBasicInactivatedCopyWith<$Res> {
  _$CardInfoBasicInactivatedCopyWithImpl(this._self, this._then);

  final CardInfoBasicInactivated _self;
  final $Res Function(CardInfoBasicInactivated) _then;

  /// Create a copy of CardInfoBasic
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? activated = null,
    Object? cardType = null,
    Object? backCover = null,
    Object? redirectUrl = null,
  }) {
    return _then(
      CardInfoBasicInactivated(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        activated: null == activated
            ? _self.activated
            : activated // ignore: cast_nullable_to_non_nullable
                  as bool,
        cardType: null == cardType
            ? _self.cardType
            : cardType // ignore: cast_nullable_to_non_nullable
                  as CardType,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        redirectUrl: null == redirectUrl
            ? _self.redirectUrl
            : redirectUrl // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$CardInfo {
  @JsonKey(name: 'referralCode')
  String get referralCode;
  @JsonKey(name: 'redirectUrl')
  String get redirectUrl;
  @JsonKey(name: 'active')
  bool get activated;
  @JsonKey(name: 'activeTime')
  String get activeTime;
  @JsonKey(name: 'backCover')
  String get backCover;
  @JsonKey(name: 'cardCode')
  String get cardCode;
  @JsonKey(name: 'cardType')
  CardType get cardType;
  @JsonKey(name: 'chainId')
  int get chainId;
  @JsonKey(name: 'card3EventId')
  int get eventId;
  @JsonKey(name: 'eventName')
  String get eventName;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'isActive')
  bool get isActive;
  @JsonKey(name: 'virtualCard')
  bool get virtualCard;
  @JsonKey(name: 'nfcType')
  NfcType get nfcType;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CardInfoCopyWith<CardInfo> get copyWith =>
      _$CardInfoCopyWithImpl<CardInfo>(this as CardInfo, _$identity);

  /// Serializes this CardInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CardInfo &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.activated, activated) ||
                other.activated == activated) &&
            (identical(other.activeTime, activeTime) ||
                other.activeTime == activeTime) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.cardType, cardType) ||
                other.cardType == cardType) &&
            (identical(other.chainId, chainId) || other.chainId == chainId) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.virtualCard, virtualCard) ||
                other.virtualCard == virtualCard) &&
            (identical(other.nfcType, nfcType) || other.nfcType == nfcType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    referralCode,
    redirectUrl,
    activated,
    activeTime,
    backCover,
    cardCode,
    cardType,
    chainId,
    eventId,
    eventName,
    id,
    isActive,
    virtualCard,
    nfcType,
  );

  @override
  String toString() {
    return 'CardInfo(referralCode: $referralCode, redirectUrl: $redirectUrl, activated: $activated, activeTime: $activeTime, backCover: $backCover, cardCode: $cardCode, cardType: $cardType, chainId: $chainId, eventId: $eventId, eventName: $eventName, id: $id, isActive: $isActive, virtualCard: $virtualCard, nfcType: $nfcType)';
  }
}

/// @nodoc
abstract mixin class $CardInfoCopyWith<$Res> {
  factory $CardInfoCopyWith(CardInfo value, $Res Function(CardInfo) _then) =
      _$CardInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'redirectUrl') String redirectUrl,
    @JsonKey(name: 'active') bool activated,
    @JsonKey(name: 'activeTime') String activeTime,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'cardType') CardType cardType,
    @JsonKey(name: 'chainId') int chainId,
    @JsonKey(name: 'card3EventId') int eventId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'isActive') bool isActive,
    @JsonKey(name: 'virtualCard') bool virtualCard,
    @JsonKey(name: 'nfcType') NfcType nfcType,
  });
}

/// @nodoc
class _$CardInfoCopyWithImpl<$Res> implements $CardInfoCopyWith<$Res> {
  _$CardInfoCopyWithImpl(this._self, this._then);

  final CardInfo _self;
  final $Res Function(CardInfo) _then;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referralCode = null,
    Object? redirectUrl = null,
    Object? activated = null,
    Object? activeTime = null,
    Object? backCover = null,
    Object? cardCode = null,
    Object? cardType = null,
    Object? chainId = null,
    Object? eventId = null,
    Object? eventName = null,
    Object? id = null,
    Object? isActive = null,
    Object? virtualCard = null,
    Object? nfcType = null,
  }) {
    return _then(
      _self.copyWith(
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        redirectUrl: null == redirectUrl
            ? _self.redirectUrl
            : redirectUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        activated: null == activated
            ? _self.activated
            : activated // ignore: cast_nullable_to_non_nullable
                  as bool,
        activeTime: null == activeTime
            ? _self.activeTime
            : activeTime // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        cardType: null == cardType
            ? _self.cardType
            : cardType // ignore: cast_nullable_to_non_nullable
                  as CardType,
        chainId: null == chainId
            ? _self.chainId
            : chainId // ignore: cast_nullable_to_non_nullable
                  as int,
        eventId: null == eventId
            ? _self.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as int,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        isActive: null == isActive
            ? _self.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        virtualCard: null == virtualCard
            ? _self.virtualCard
            : virtualCard // ignore: cast_nullable_to_non_nullable
                  as bool,
        nfcType: null == nfcType
            ? _self.nfcType
            : nfcType // ignore: cast_nullable_to_non_nullable
                  as NfcType,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [CardInfo].
extension CardInfoPatterns on CardInfo {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CardInfo value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CardInfo() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CardInfo value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CardInfo():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CardInfo value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CardInfo() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'referralCode') String referralCode,
      @JsonKey(name: 'redirectUrl') String redirectUrl,
      @JsonKey(name: 'active') bool activated,
      @JsonKey(name: 'activeTime') String activeTime,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'cardCode') String cardCode,
      @JsonKey(name: 'cardType') CardType cardType,
      @JsonKey(name: 'chainId') int chainId,
      @JsonKey(name: 'card3EventId') int eventId,
      @JsonKey(name: 'eventName') String eventName,
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'isActive') bool isActive,
      @JsonKey(name: 'virtualCard') bool virtualCard,
      @JsonKey(name: 'nfcType') NfcType nfcType,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CardInfo() when $default != null:
        return $default(
          _that.referralCode,
          _that.redirectUrl,
          _that.activated,
          _that.activeTime,
          _that.backCover,
          _that.cardCode,
          _that.cardType,
          _that.chainId,
          _that.eventId,
          _that.eventName,
          _that.id,
          _that.isActive,
          _that.virtualCard,
          _that.nfcType,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'referralCode') String referralCode,
      @JsonKey(name: 'redirectUrl') String redirectUrl,
      @JsonKey(name: 'active') bool activated,
      @JsonKey(name: 'activeTime') String activeTime,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'cardCode') String cardCode,
      @JsonKey(name: 'cardType') CardType cardType,
      @JsonKey(name: 'chainId') int chainId,
      @JsonKey(name: 'card3EventId') int eventId,
      @JsonKey(name: 'eventName') String eventName,
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'isActive') bool isActive,
      @JsonKey(name: 'virtualCard') bool virtualCard,
      @JsonKey(name: 'nfcType') NfcType nfcType,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CardInfo():
        return $default(
          _that.referralCode,
          _that.redirectUrl,
          _that.activated,
          _that.activeTime,
          _that.backCover,
          _that.cardCode,
          _that.cardType,
          _that.chainId,
          _that.eventId,
          _that.eventName,
          _that.id,
          _that.isActive,
          _that.virtualCard,
          _that.nfcType,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'referralCode') String referralCode,
      @JsonKey(name: 'redirectUrl') String redirectUrl,
      @JsonKey(name: 'active') bool activated,
      @JsonKey(name: 'activeTime') String activeTime,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'cardCode') String cardCode,
      @JsonKey(name: 'cardType') CardType cardType,
      @JsonKey(name: 'chainId') int chainId,
      @JsonKey(name: 'card3EventId') int eventId,
      @JsonKey(name: 'eventName') String eventName,
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'isActive') bool isActive,
      @JsonKey(name: 'virtualCard') bool virtualCard,
      @JsonKey(name: 'nfcType') NfcType nfcType,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CardInfo() when $default != null:
        return $default(
          _that.referralCode,
          _that.redirectUrl,
          _that.activated,
          _that.activeTime,
          _that.backCover,
          _that.cardCode,
          _that.cardType,
          _that.chainId,
          _that.eventId,
          _that.eventName,
          _that.id,
          _that.isActive,
          _that.virtualCard,
          _that.nfcType,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _CardInfo implements CardInfo, ICardInfoActivated {
  const _CardInfo({
    @JsonKey(name: 'referralCode') this.referralCode = '',
    @JsonKey(name: 'redirectUrl') this.redirectUrl = '',
    @JsonKey(name: 'active') required this.activated,
    @JsonKey(name: 'activeTime') this.activeTime = '',
    @JsonKey(name: 'backCover') this.backCover = '',
    @JsonKey(name: 'cardCode') this.cardCode = '',
    @JsonKey(name: 'cardType') this.cardType = CardType.CARD,
    @JsonKey(name: 'chainId') this.chainId = 0,
    @JsonKey(name: 'card3EventId') this.eventId = 0,
    @JsonKey(name: 'eventName') this.eventName = '',
    @JsonKey(name: 'id') this.id = 0,
    @JsonKey(name: 'isActive') this.isActive = false,
    @JsonKey(name: 'virtualCard') this.virtualCard = false,
    @JsonKey(name: 'nfcType') this.nfcType = NfcType.NFC215,
  });
  factory _CardInfo.fromJson(Map<String, dynamic> json) =>
      _$CardInfoFromJson(json);

  @override
  @JsonKey(name: 'referralCode')
  final String referralCode;
  @override
  @JsonKey(name: 'redirectUrl')
  final String redirectUrl;
  @override
  @JsonKey(name: 'active')
  final bool activated;
  @override
  @JsonKey(name: 'activeTime')
  final String activeTime;
  @override
  @JsonKey(name: 'backCover')
  final String backCover;
  @override
  @JsonKey(name: 'cardCode')
  final String cardCode;
  @override
  @JsonKey(name: 'cardType')
  final CardType cardType;
  @override
  @JsonKey(name: 'chainId')
  final int chainId;
  @override
  @JsonKey(name: 'card3EventId')
  final int eventId;
  @override
  @JsonKey(name: 'eventName')
  final String eventName;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'isActive')
  final bool isActive;
  @override
  @JsonKey(name: 'virtualCard')
  final bool virtualCard;
  @override
  @JsonKey(name: 'nfcType')
  final NfcType nfcType;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CardInfoCopyWith<_CardInfo> get copyWith =>
      __$CardInfoCopyWithImpl<_CardInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CardInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CardInfo &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.activated, activated) ||
                other.activated == activated) &&
            (identical(other.activeTime, activeTime) ||
                other.activeTime == activeTime) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.cardType, cardType) ||
                other.cardType == cardType) &&
            (identical(other.chainId, chainId) || other.chainId == chainId) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.virtualCard, virtualCard) ||
                other.virtualCard == virtualCard) &&
            (identical(other.nfcType, nfcType) || other.nfcType == nfcType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    referralCode,
    redirectUrl,
    activated,
    activeTime,
    backCover,
    cardCode,
    cardType,
    chainId,
    eventId,
    eventName,
    id,
    isActive,
    virtualCard,
    nfcType,
  );

  @override
  String toString() {
    return 'CardInfo(referralCode: $referralCode, redirectUrl: $redirectUrl, activated: $activated, activeTime: $activeTime, backCover: $backCover, cardCode: $cardCode, cardType: $cardType, chainId: $chainId, eventId: $eventId, eventName: $eventName, id: $id, isActive: $isActive, virtualCard: $virtualCard, nfcType: $nfcType)';
  }
}

/// @nodoc
abstract mixin class _$CardInfoCopyWith<$Res>
    implements $CardInfoCopyWith<$Res> {
  factory _$CardInfoCopyWith(_CardInfo value, $Res Function(_CardInfo) _then) =
      __$CardInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'redirectUrl') String redirectUrl,
    @JsonKey(name: 'active') bool activated,
    @JsonKey(name: 'activeTime') String activeTime,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'cardType') CardType cardType,
    @JsonKey(name: 'chainId') int chainId,
    @JsonKey(name: 'card3EventId') int eventId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'isActive') bool isActive,
    @JsonKey(name: 'virtualCard') bool virtualCard,
    @JsonKey(name: 'nfcType') NfcType nfcType,
  });
}

/// @nodoc
class __$CardInfoCopyWithImpl<$Res> implements _$CardInfoCopyWith<$Res> {
  __$CardInfoCopyWithImpl(this._self, this._then);

  final _CardInfo _self;
  final $Res Function(_CardInfo) _then;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? referralCode = null,
    Object? redirectUrl = null,
    Object? activated = null,
    Object? activeTime = null,
    Object? backCover = null,
    Object? cardCode = null,
    Object? cardType = null,
    Object? chainId = null,
    Object? eventId = null,
    Object? eventName = null,
    Object? id = null,
    Object? isActive = null,
    Object? virtualCard = null,
    Object? nfcType = null,
  }) {
    return _then(
      _CardInfo(
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        redirectUrl: null == redirectUrl
            ? _self.redirectUrl
            : redirectUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        activated: null == activated
            ? _self.activated
            : activated // ignore: cast_nullable_to_non_nullable
                  as bool,
        activeTime: null == activeTime
            ? _self.activeTime
            : activeTime // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        cardType: null == cardType
            ? _self.cardType
            : cardType // ignore: cast_nullable_to_non_nullable
                  as CardType,
        chainId: null == chainId
            ? _self.chainId
            : chainId // ignore: cast_nullable_to_non_nullable
                  as int,
        eventId: null == eventId
            ? _self.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as int,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        isActive: null == isActive
            ? _self.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        virtualCard: null == virtualCard
            ? _self.virtualCard
            : virtualCard // ignore: cast_nullable_to_non_nullable
                  as bool,
        nfcType: null == nfcType
            ? _self.nfcType
            : nfcType // ignore: cast_nullable_to_non_nullable
                  as NfcType,
      ),
    );
  }
}

/// @nodoc
mixin _$CoverInfo {
  @JsonKey(name: 'activeMode')
  String get activeMode;
  @JsonKey(name: 'backCover')
  String get backCover;
  @JsonKey(name: 'eventId')
  String get eventId;
  @JsonKey(name: 'eventName')
  String get eventName;
  @JsonKey(name: 'paymentLink')
  String get paymentLink;
  @JsonKey(name: 'price')
  int get price;
  @JsonKey(name: 'priceDescription')
  String get priceDescription;
  @JsonKey(name: 'printType')
  PrintType get printType;
  @JsonKey(name: 'thirdPartyLink')
  String get thirdPartyLink;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CoverInfoCopyWith<CoverInfo> get copyWith =>
      _$CoverInfoCopyWithImpl<CoverInfo>(this as CoverInfo, _$identity);

  /// Serializes this CoverInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CoverInfo &&
            (identical(other.activeMode, activeMode) ||
                other.activeMode == activeMode) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.priceDescription, priceDescription) ||
                other.priceDescription == priceDescription) &&
            (identical(other.printType, printType) ||
                other.printType == printType) &&
            (identical(other.thirdPartyLink, thirdPartyLink) ||
                other.thirdPartyLink == thirdPartyLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    activeMode,
    backCover,
    eventId,
    eventName,
    paymentLink,
    price,
    priceDescription,
    printType,
    thirdPartyLink,
  );

  @override
  String toString() {
    return 'CoverInfo(activeMode: $activeMode, backCover: $backCover, eventId: $eventId, eventName: $eventName, paymentLink: $paymentLink, price: $price, priceDescription: $priceDescription, printType: $printType, thirdPartyLink: $thirdPartyLink)';
  }
}

/// @nodoc
abstract mixin class $CoverInfoCopyWith<$Res> {
  factory $CoverInfoCopyWith(CoverInfo value, $Res Function(CoverInfo) _then) =
      _$CoverInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'activeMode') String activeMode,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'eventId') String eventId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'paymentLink') String paymentLink,
    @JsonKey(name: 'price') int price,
    @JsonKey(name: 'priceDescription') String priceDescription,
    @JsonKey(name: 'printType') PrintType printType,
    @JsonKey(name: 'thirdPartyLink') String thirdPartyLink,
  });
}

/// @nodoc
class _$CoverInfoCopyWithImpl<$Res> implements $CoverInfoCopyWith<$Res> {
  _$CoverInfoCopyWithImpl(this._self, this._then);

  final CoverInfo _self;
  final $Res Function(CoverInfo) _then;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeMode = null,
    Object? backCover = null,
    Object? eventId = null,
    Object? eventName = null,
    Object? paymentLink = null,
    Object? price = null,
    Object? priceDescription = null,
    Object? printType = null,
    Object? thirdPartyLink = null,
  }) {
    return _then(
      _self.copyWith(
        activeMode: null == activeMode
            ? _self.activeMode
            : activeMode // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        eventId: null == eventId
            ? _self.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as int,
        priceDescription: null == priceDescription
            ? _self.priceDescription
            : priceDescription // ignore: cast_nullable_to_non_nullable
                  as String,
        printType: null == printType
            ? _self.printType
            : printType // ignore: cast_nullable_to_non_nullable
                  as PrintType,
        thirdPartyLink: null == thirdPartyLink
            ? _self.thirdPartyLink
            : thirdPartyLink // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [CoverInfo].
extension CoverInfoPatterns on CoverInfo {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CoverInfo value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CoverInfo() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CoverInfo value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CoverInfo():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CoverInfo value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CoverInfo() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'activeMode') String activeMode,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'eventId') String eventId,
      @JsonKey(name: 'eventName') String eventName,
      @JsonKey(name: 'paymentLink') String paymentLink,
      @JsonKey(name: 'price') int price,
      @JsonKey(name: 'priceDescription') String priceDescription,
      @JsonKey(name: 'printType') PrintType printType,
      @JsonKey(name: 'thirdPartyLink') String thirdPartyLink,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CoverInfo() when $default != null:
        return $default(
          _that.activeMode,
          _that.backCover,
          _that.eventId,
          _that.eventName,
          _that.paymentLink,
          _that.price,
          _that.priceDescription,
          _that.printType,
          _that.thirdPartyLink,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'activeMode') String activeMode,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'eventId') String eventId,
      @JsonKey(name: 'eventName') String eventName,
      @JsonKey(name: 'paymentLink') String paymentLink,
      @JsonKey(name: 'price') int price,
      @JsonKey(name: 'priceDescription') String priceDescription,
      @JsonKey(name: 'printType') PrintType printType,
      @JsonKey(name: 'thirdPartyLink') String thirdPartyLink,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CoverInfo():
        return $default(
          _that.activeMode,
          _that.backCover,
          _that.eventId,
          _that.eventName,
          _that.paymentLink,
          _that.price,
          _that.priceDescription,
          _that.printType,
          _that.thirdPartyLink,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'activeMode') String activeMode,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'eventId') String eventId,
      @JsonKey(name: 'eventName') String eventName,
      @JsonKey(name: 'paymentLink') String paymentLink,
      @JsonKey(name: 'price') int price,
      @JsonKey(name: 'priceDescription') String priceDescription,
      @JsonKey(name: 'printType') PrintType printType,
      @JsonKey(name: 'thirdPartyLink') String thirdPartyLink,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CoverInfo() when $default != null:
        return $default(
          _that.activeMode,
          _that.backCover,
          _that.eventId,
          _that.eventName,
          _that.paymentLink,
          _that.price,
          _that.priceDescription,
          _that.printType,
          _that.thirdPartyLink,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _CoverInfo implements CoverInfo {
  const _CoverInfo({
    @JsonKey(name: 'activeMode') this.activeMode = '',
    @JsonKey(name: 'backCover') this.backCover = '',
    @JsonKey(name: 'eventId') this.eventId = '',
    @JsonKey(name: 'eventName') this.eventName = '',
    @JsonKey(name: 'paymentLink') this.paymentLink = '',
    @JsonKey(name: 'price') this.price = 0,
    @JsonKey(name: 'priceDescription') this.priceDescription = '',
    @JsonKey(name: 'printType') this.printType = PrintType.NORMAL,
    @JsonKey(name: 'thirdPartyLink') this.thirdPartyLink = '',
  });
  factory _CoverInfo.fromJson(Map<String, dynamic> json) =>
      _$CoverInfoFromJson(json);

  @override
  @JsonKey(name: 'activeMode')
  final String activeMode;
  @override
  @JsonKey(name: 'backCover')
  final String backCover;
  @override
  @JsonKey(name: 'eventId')
  final String eventId;
  @override
  @JsonKey(name: 'eventName')
  final String eventName;
  @override
  @JsonKey(name: 'paymentLink')
  final String paymentLink;
  @override
  @JsonKey(name: 'price')
  final int price;
  @override
  @JsonKey(name: 'priceDescription')
  final String priceDescription;
  @override
  @JsonKey(name: 'printType')
  final PrintType printType;
  @override
  @JsonKey(name: 'thirdPartyLink')
  final String thirdPartyLink;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CoverInfoCopyWith<_CoverInfo> get copyWith =>
      __$CoverInfoCopyWithImpl<_CoverInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CoverInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CoverInfo &&
            (identical(other.activeMode, activeMode) ||
                other.activeMode == activeMode) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.priceDescription, priceDescription) ||
                other.priceDescription == priceDescription) &&
            (identical(other.printType, printType) ||
                other.printType == printType) &&
            (identical(other.thirdPartyLink, thirdPartyLink) ||
                other.thirdPartyLink == thirdPartyLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    activeMode,
    backCover,
    eventId,
    eventName,
    paymentLink,
    price,
    priceDescription,
    printType,
    thirdPartyLink,
  );

  @override
  String toString() {
    return 'CoverInfo(activeMode: $activeMode, backCover: $backCover, eventId: $eventId, eventName: $eventName, paymentLink: $paymentLink, price: $price, priceDescription: $priceDescription, printType: $printType, thirdPartyLink: $thirdPartyLink)';
  }
}

/// @nodoc
abstract mixin class _$CoverInfoCopyWith<$Res>
    implements $CoverInfoCopyWith<$Res> {
  factory _$CoverInfoCopyWith(
    _CoverInfo value,
    $Res Function(_CoverInfo) _then,
  ) = __$CoverInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'activeMode') String activeMode,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'eventId') String eventId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'paymentLink') String paymentLink,
    @JsonKey(name: 'price') int price,
    @JsonKey(name: 'priceDescription') String priceDescription,
    @JsonKey(name: 'printType') PrintType printType,
    @JsonKey(name: 'thirdPartyLink') String thirdPartyLink,
  });
}

/// @nodoc
class __$CoverInfoCopyWithImpl<$Res> implements _$CoverInfoCopyWith<$Res> {
  __$CoverInfoCopyWithImpl(this._self, this._then);

  final _CoverInfo _self;
  final $Res Function(_CoverInfo) _then;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? activeMode = null,
    Object? backCover = null,
    Object? eventId = null,
    Object? eventName = null,
    Object? paymentLink = null,
    Object? price = null,
    Object? priceDescription = null,
    Object? printType = null,
    Object? thirdPartyLink = null,
  }) {
    return _then(
      _CoverInfo(
        activeMode: null == activeMode
            ? _self.activeMode
            : activeMode // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        eventId: null == eventId
            ? _self.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as int,
        priceDescription: null == priceDescription
            ? _self.priceDescription
            : priceDescription // ignore: cast_nullable_to_non_nullable
                  as String,
        printType: null == printType
            ? _self.printType
            : printType // ignore: cast_nullable_to_non_nullable
                  as PrintType,
        thirdPartyLink: null == thirdPartyLink
            ? _self.thirdPartyLink
            : thirdPartyLink // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$CustomizeCardOrder {
  @JsonKey(name: 'username')
  String get name;
  @JsonKey(name: 'title')
  String get title;
  @JsonKey(name: 'company')
  String get company;
  @JsonKey(name: 'image')
  String get image;
  @JsonKey(name: 'code')
  String get code;
  @JsonKey(name: 'preview')
  String get preview;
  @JsonKey(name: 'backCover')
  String get backCover;
  @JsonKey(name: 'eventName')
  String get eventName;
  @JsonKey(name: 'isPrinted')
  bool get printed;
  @JsonKey(name: 'printType')
  PrintType get printType;
  @JsonKey(name: 'trackingNumber')
  String get trackingNumber;
  @JsonKey(name: 'logisticsCompany')
  String get trackingCompany;
  @JsonKey(name: 'price')
  int get payPrice;
  @JsonKey(name: 'payStatus')
  CardPayStatus get payStatus;
  @JsonKey(name: 'paymentLink')
  String get paymentLink;
  @JsonKey(name: 'thirdPartyLink', readValue: CustomizeCardPayInfo.readValue)
  CustomizeCardPayInfo get payInfo;

  /// Create a copy of CustomizeCardOrder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CustomizeCardOrderCopyWith<CustomizeCardOrder> get copyWith =>
      _$CustomizeCardOrderCopyWithImpl<CustomizeCardOrder>(
        this as CustomizeCardOrder,
        _$identity,
      );

  /// Serializes this CustomizeCardOrder to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CustomizeCardOrder &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.preview, preview) || other.preview == preview) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.printed, printed) || other.printed == printed) &&
            (identical(other.printType, printType) ||
                other.printType == printType) &&
            (identical(other.trackingNumber, trackingNumber) ||
                other.trackingNumber == trackingNumber) &&
            (identical(other.trackingCompany, trackingCompany) ||
                other.trackingCompany == trackingCompany) &&
            (identical(other.payPrice, payPrice) ||
                other.payPrice == payPrice) &&
            (identical(other.payStatus, payStatus) ||
                other.payStatus == payStatus) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink) &&
            (identical(other.payInfo, payInfo) || other.payInfo == payInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    title,
    company,
    image,
    code,
    preview,
    backCover,
    eventName,
    printed,
    printType,
    trackingNumber,
    trackingCompany,
    payPrice,
    payStatus,
    paymentLink,
    payInfo,
  );

  @override
  String toString() {
    return 'CustomizeCardOrder(name: $name, title: $title, company: $company, image: $image, code: $code, preview: $preview, backCover: $backCover, eventName: $eventName, printed: $printed, printType: $printType, trackingNumber: $trackingNumber, trackingCompany: $trackingCompany, payPrice: $payPrice, payStatus: $payStatus, paymentLink: $paymentLink, payInfo: $payInfo)';
  }
}

/// @nodoc
abstract mixin class $CustomizeCardOrderCopyWith<$Res> {
  factory $CustomizeCardOrderCopyWith(
    CustomizeCardOrder value,
    $Res Function(CustomizeCardOrder) _then,
  ) = _$CustomizeCardOrderCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'username') String name,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'image') String image,
    @JsonKey(name: 'code') String code,
    @JsonKey(name: 'preview') String preview,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'isPrinted') bool printed,
    @JsonKey(name: 'printType') PrintType printType,
    @JsonKey(name: 'trackingNumber') String trackingNumber,
    @JsonKey(name: 'logisticsCompany') String trackingCompany,
    @JsonKey(name: 'price') int payPrice,
    @JsonKey(name: 'payStatus') CardPayStatus payStatus,
    @JsonKey(name: 'paymentLink') String paymentLink,
    @JsonKey(name: 'thirdPartyLink', readValue: CustomizeCardPayInfo.readValue)
    CustomizeCardPayInfo payInfo,
  });

  $CustomizeCardPayInfoCopyWith<$Res> get payInfo;
}

/// @nodoc
class _$CustomizeCardOrderCopyWithImpl<$Res>
    implements $CustomizeCardOrderCopyWith<$Res> {
  _$CustomizeCardOrderCopyWithImpl(this._self, this._then);

  final CustomizeCardOrder _self;
  final $Res Function(CustomizeCardOrder) _then;

  /// Create a copy of CustomizeCardOrder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? image = null,
    Object? code = null,
    Object? preview = null,
    Object? backCover = null,
    Object? eventName = null,
    Object? printed = null,
    Object? printType = null,
    Object? trackingNumber = null,
    Object? trackingCompany = null,
    Object? payPrice = null,
    Object? payStatus = null,
    Object? paymentLink = null,
    Object? payInfo = null,
  }) {
    return _then(
      _self.copyWith(
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        image: null == image
            ? _self.image
            : image // ignore: cast_nullable_to_non_nullable
                  as String,
        code: null == code
            ? _self.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String,
        preview: null == preview
            ? _self.preview
            : preview // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        printed: null == printed
            ? _self.printed
            : printed // ignore: cast_nullable_to_non_nullable
                  as bool,
        printType: null == printType
            ? _self.printType
            : printType // ignore: cast_nullable_to_non_nullable
                  as PrintType,
        trackingNumber: null == trackingNumber
            ? _self.trackingNumber
            : trackingNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        trackingCompany: null == trackingCompany
            ? _self.trackingCompany
            : trackingCompany // ignore: cast_nullable_to_non_nullable
                  as String,
        payPrice: null == payPrice
            ? _self.payPrice
            : payPrice // ignore: cast_nullable_to_non_nullable
                  as int,
        payStatus: null == payStatus
            ? _self.payStatus
            : payStatus // ignore: cast_nullable_to_non_nullable
                  as CardPayStatus,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
        payInfo: null == payInfo
            ? _self.payInfo
            : payInfo // ignore: cast_nullable_to_non_nullable
                  as CustomizeCardPayInfo,
      ),
    );
  }

  /// Create a copy of CustomizeCardOrder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomizeCardPayInfoCopyWith<$Res> get payInfo {
    return $CustomizeCardPayInfoCopyWith<$Res>(_self.payInfo, (value) {
      return _then(_self.copyWith(payInfo: value));
    });
  }
}

/// Adds pattern-matching-related methods to [CustomizeCardOrder].
extension CustomizeCardOrderPatterns on CustomizeCardOrder {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CustomizeCardOrder value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardOrder() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CustomizeCardOrder value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardOrder():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CustomizeCardOrder value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardOrder() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'username') String name,
      @JsonKey(name: 'title') String title,
      @JsonKey(name: 'company') String company,
      @JsonKey(name: 'image') String image,
      @JsonKey(name: 'code') String code,
      @JsonKey(name: 'preview') String preview,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'eventName') String eventName,
      @JsonKey(name: 'isPrinted') bool printed,
      @JsonKey(name: 'printType') PrintType printType,
      @JsonKey(name: 'trackingNumber') String trackingNumber,
      @JsonKey(name: 'logisticsCompany') String trackingCompany,
      @JsonKey(name: 'price') int payPrice,
      @JsonKey(name: 'payStatus') CardPayStatus payStatus,
      @JsonKey(name: 'paymentLink') String paymentLink,
      @JsonKey(
        name: 'thirdPartyLink',
        readValue: CustomizeCardPayInfo.readValue,
      )
      CustomizeCardPayInfo payInfo,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardOrder() when $default != null:
        return $default(
          _that.name,
          _that.title,
          _that.company,
          _that.image,
          _that.code,
          _that.preview,
          _that.backCover,
          _that.eventName,
          _that.printed,
          _that.printType,
          _that.trackingNumber,
          _that.trackingCompany,
          _that.payPrice,
          _that.payStatus,
          _that.paymentLink,
          _that.payInfo,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'username') String name,
      @JsonKey(name: 'title') String title,
      @JsonKey(name: 'company') String company,
      @JsonKey(name: 'image') String image,
      @JsonKey(name: 'code') String code,
      @JsonKey(name: 'preview') String preview,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'eventName') String eventName,
      @JsonKey(name: 'isPrinted') bool printed,
      @JsonKey(name: 'printType') PrintType printType,
      @JsonKey(name: 'trackingNumber') String trackingNumber,
      @JsonKey(name: 'logisticsCompany') String trackingCompany,
      @JsonKey(name: 'price') int payPrice,
      @JsonKey(name: 'payStatus') CardPayStatus payStatus,
      @JsonKey(name: 'paymentLink') String paymentLink,
      @JsonKey(
        name: 'thirdPartyLink',
        readValue: CustomizeCardPayInfo.readValue,
      )
      CustomizeCardPayInfo payInfo,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardOrder():
        return $default(
          _that.name,
          _that.title,
          _that.company,
          _that.image,
          _that.code,
          _that.preview,
          _that.backCover,
          _that.eventName,
          _that.printed,
          _that.printType,
          _that.trackingNumber,
          _that.trackingCompany,
          _that.payPrice,
          _that.payStatus,
          _that.paymentLink,
          _that.payInfo,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'username') String name,
      @JsonKey(name: 'title') String title,
      @JsonKey(name: 'company') String company,
      @JsonKey(name: 'image') String image,
      @JsonKey(name: 'code') String code,
      @JsonKey(name: 'preview') String preview,
      @JsonKey(name: 'backCover') String backCover,
      @JsonKey(name: 'eventName') String eventName,
      @JsonKey(name: 'isPrinted') bool printed,
      @JsonKey(name: 'printType') PrintType printType,
      @JsonKey(name: 'trackingNumber') String trackingNumber,
      @JsonKey(name: 'logisticsCompany') String trackingCompany,
      @JsonKey(name: 'price') int payPrice,
      @JsonKey(name: 'payStatus') CardPayStatus payStatus,
      @JsonKey(name: 'paymentLink') String paymentLink,
      @JsonKey(
        name: 'thirdPartyLink',
        readValue: CustomizeCardPayInfo.readValue,
      )
      CustomizeCardPayInfo payInfo,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardOrder() when $default != null:
        return $default(
          _that.name,
          _that.title,
          _that.company,
          _that.image,
          _that.code,
          _that.preview,
          _that.backCover,
          _that.eventName,
          _that.printed,
          _that.printType,
          _that.trackingNumber,
          _that.trackingCompany,
          _that.payPrice,
          _that.payStatus,
          _that.paymentLink,
          _that.payInfo,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _CustomizeCardOrder implements CustomizeCardOrder {
  const _CustomizeCardOrder({
    @JsonKey(name: 'username') required this.name,
    @JsonKey(name: 'title') this.title = '',
    @JsonKey(name: 'company') this.company = '',
    @JsonKey(name: 'image') this.image = '',
    @JsonKey(name: 'code') required this.code,
    @JsonKey(name: 'preview') this.preview = '',
    @JsonKey(name: 'backCover') this.backCover = '',
    @JsonKey(name: 'eventName') this.eventName = '',
    @JsonKey(name: 'isPrinted') this.printed = false,
    @JsonKey(name: 'printType') required this.printType,
    @JsonKey(name: 'trackingNumber') this.trackingNumber = '',
    @JsonKey(name: 'logisticsCompany') this.trackingCompany = '',
    @JsonKey(name: 'price') this.payPrice = 0,
    @JsonKey(name: 'payStatus') this.payStatus = CardPayStatus.FREE,
    @JsonKey(name: 'paymentLink') this.paymentLink = '',
    @JsonKey(name: 'thirdPartyLink', readValue: CustomizeCardPayInfo.readValue)
    required this.payInfo,
  });
  factory _CustomizeCardOrder.fromJson(Map<String, dynamic> json) =>
      _$CustomizeCardOrderFromJson(json);

  @override
  @JsonKey(name: 'username')
  final String name;
  @override
  @JsonKey(name: 'title')
  final String title;
  @override
  @JsonKey(name: 'company')
  final String company;
  @override
  @JsonKey(name: 'image')
  final String image;
  @override
  @JsonKey(name: 'code')
  final String code;
  @override
  @JsonKey(name: 'preview')
  final String preview;
  @override
  @JsonKey(name: 'backCover')
  final String backCover;
  @override
  @JsonKey(name: 'eventName')
  final String eventName;
  @override
  @JsonKey(name: 'isPrinted')
  final bool printed;
  @override
  @JsonKey(name: 'printType')
  final PrintType printType;
  @override
  @JsonKey(name: 'trackingNumber')
  final String trackingNumber;
  @override
  @JsonKey(name: 'logisticsCompany')
  final String trackingCompany;
  @override
  @JsonKey(name: 'price')
  final int payPrice;
  @override
  @JsonKey(name: 'payStatus')
  final CardPayStatus payStatus;
  @override
  @JsonKey(name: 'paymentLink')
  final String paymentLink;
  @override
  @JsonKey(name: 'thirdPartyLink', readValue: CustomizeCardPayInfo.readValue)
  final CustomizeCardPayInfo payInfo;

  /// Create a copy of CustomizeCardOrder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CustomizeCardOrderCopyWith<_CustomizeCardOrder> get copyWith =>
      __$CustomizeCardOrderCopyWithImpl<_CustomizeCardOrder>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CustomizeCardOrderToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CustomizeCardOrder &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.preview, preview) || other.preview == preview) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.printed, printed) || other.printed == printed) &&
            (identical(other.printType, printType) ||
                other.printType == printType) &&
            (identical(other.trackingNumber, trackingNumber) ||
                other.trackingNumber == trackingNumber) &&
            (identical(other.trackingCompany, trackingCompany) ||
                other.trackingCompany == trackingCompany) &&
            (identical(other.payPrice, payPrice) ||
                other.payPrice == payPrice) &&
            (identical(other.payStatus, payStatus) ||
                other.payStatus == payStatus) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink) &&
            (identical(other.payInfo, payInfo) || other.payInfo == payInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    title,
    company,
    image,
    code,
    preview,
    backCover,
    eventName,
    printed,
    printType,
    trackingNumber,
    trackingCompany,
    payPrice,
    payStatus,
    paymentLink,
    payInfo,
  );

  @override
  String toString() {
    return 'CustomizeCardOrder(name: $name, title: $title, company: $company, image: $image, code: $code, preview: $preview, backCover: $backCover, eventName: $eventName, printed: $printed, printType: $printType, trackingNumber: $trackingNumber, trackingCompany: $trackingCompany, payPrice: $payPrice, payStatus: $payStatus, paymentLink: $paymentLink, payInfo: $payInfo)';
  }
}

/// @nodoc
abstract mixin class _$CustomizeCardOrderCopyWith<$Res>
    implements $CustomizeCardOrderCopyWith<$Res> {
  factory _$CustomizeCardOrderCopyWith(
    _CustomizeCardOrder value,
    $Res Function(_CustomizeCardOrder) _then,
  ) = __$CustomizeCardOrderCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'username') String name,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'image') String image,
    @JsonKey(name: 'code') String code,
    @JsonKey(name: 'preview') String preview,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'isPrinted') bool printed,
    @JsonKey(name: 'printType') PrintType printType,
    @JsonKey(name: 'trackingNumber') String trackingNumber,
    @JsonKey(name: 'logisticsCompany') String trackingCompany,
    @JsonKey(name: 'price') int payPrice,
    @JsonKey(name: 'payStatus') CardPayStatus payStatus,
    @JsonKey(name: 'paymentLink') String paymentLink,
    @JsonKey(name: 'thirdPartyLink', readValue: CustomizeCardPayInfo.readValue)
    CustomizeCardPayInfo payInfo,
  });

  @override
  $CustomizeCardPayInfoCopyWith<$Res> get payInfo;
}

/// @nodoc
class __$CustomizeCardOrderCopyWithImpl<$Res>
    implements _$CustomizeCardOrderCopyWith<$Res> {
  __$CustomizeCardOrderCopyWithImpl(this._self, this._then);

  final _CustomizeCardOrder _self;
  final $Res Function(_CustomizeCardOrder) _then;

  /// Create a copy of CustomizeCardOrder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? image = null,
    Object? code = null,
    Object? preview = null,
    Object? backCover = null,
    Object? eventName = null,
    Object? printed = null,
    Object? printType = null,
    Object? trackingNumber = null,
    Object? trackingCompany = null,
    Object? payPrice = null,
    Object? payStatus = null,
    Object? paymentLink = null,
    Object? payInfo = null,
  }) {
    return _then(
      _CustomizeCardOrder(
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        image: null == image
            ? _self.image
            : image // ignore: cast_nullable_to_non_nullable
                  as String,
        code: null == code
            ? _self.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String,
        preview: null == preview
            ? _self.preview
            : preview // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        printed: null == printed
            ? _self.printed
            : printed // ignore: cast_nullable_to_non_nullable
                  as bool,
        printType: null == printType
            ? _self.printType
            : printType // ignore: cast_nullable_to_non_nullable
                  as PrintType,
        trackingNumber: null == trackingNumber
            ? _self.trackingNumber
            : trackingNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        trackingCompany: null == trackingCompany
            ? _self.trackingCompany
            : trackingCompany // ignore: cast_nullable_to_non_nullable
                  as String,
        payPrice: null == payPrice
            ? _self.payPrice
            : payPrice // ignore: cast_nullable_to_non_nullable
                  as int,
        payStatus: null == payStatus
            ? _self.payStatus
            : payStatus // ignore: cast_nullable_to_non_nullable
                  as CardPayStatus,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
        payInfo: null == payInfo
            ? _self.payInfo
            : payInfo // ignore: cast_nullable_to_non_nullable
                  as CustomizeCardPayInfo,
      ),
    );
  }

  /// Create a copy of CustomizeCardOrder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomizeCardPayInfoCopyWith<$Res> get payInfo {
    return $CustomizeCardPayInfoCopyWith<$Res>(_self.payInfo, (value) {
      return _then(_self.copyWith(payInfo: value));
    });
  }
}

/// @nodoc
mixin _$CustomizeCardPayInfo {
  @JsonKey(name: 'link')
  String get link;
  @JsonKey(name: 'priceUnit')
  @CustomizeCardPriceUnitConverter()
  CustomizeCardPriceUnit? get priceUnit;
  @JsonKey(name: 'shippingUnit')
  @CustomizeCardPriceUnitConverter()
  CustomizeCardPriceUnit? get shippingUnit;

  /// Create a copy of CustomizeCardPayInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CustomizeCardPayInfoCopyWith<CustomizeCardPayInfo> get copyWith =>
      _$CustomizeCardPayInfoCopyWithImpl<CustomizeCardPayInfo>(
        this as CustomizeCardPayInfo,
        _$identity,
      );

  /// Serializes this CustomizeCardPayInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CustomizeCardPayInfo &&
            (identical(other.link, link) || other.link == link) &&
            (identical(other.priceUnit, priceUnit) ||
                other.priceUnit == priceUnit) &&
            (identical(other.shippingUnit, shippingUnit) ||
                other.shippingUnit == shippingUnit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, link, priceUnit, shippingUnit);

  @override
  String toString() {
    return 'CustomizeCardPayInfo(link: $link, priceUnit: $priceUnit, shippingUnit: $shippingUnit)';
  }
}

/// @nodoc
abstract mixin class $CustomizeCardPayInfoCopyWith<$Res> {
  factory $CustomizeCardPayInfoCopyWith(
    CustomizeCardPayInfo value,
    $Res Function(CustomizeCardPayInfo) _then,
  ) = _$CustomizeCardPayInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'link') String link,
    @JsonKey(name: 'priceUnit')
    @CustomizeCardPriceUnitConverter()
    CustomizeCardPriceUnit? priceUnit,
    @JsonKey(name: 'shippingUnit')
    @CustomizeCardPriceUnitConverter()
    CustomizeCardPriceUnit? shippingUnit,
  });

  $CustomizeCardPriceUnitCopyWith<$Res>? get priceUnit;
  $CustomizeCardPriceUnitCopyWith<$Res>? get shippingUnit;
}

/// @nodoc
class _$CustomizeCardPayInfoCopyWithImpl<$Res>
    implements $CustomizeCardPayInfoCopyWith<$Res> {
  _$CustomizeCardPayInfoCopyWithImpl(this._self, this._then);

  final CustomizeCardPayInfo _self;
  final $Res Function(CustomizeCardPayInfo) _then;

  /// Create a copy of CustomizeCardPayInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? link = null,
    Object? priceUnit = freezed,
    Object? shippingUnit = freezed,
  }) {
    return _then(
      _self.copyWith(
        link: null == link
            ? _self.link
            : link // ignore: cast_nullable_to_non_nullable
                  as String,
        priceUnit: freezed == priceUnit
            ? _self.priceUnit
            : priceUnit // ignore: cast_nullable_to_non_nullable
                  as CustomizeCardPriceUnit?,
        shippingUnit: freezed == shippingUnit
            ? _self.shippingUnit
            : shippingUnit // ignore: cast_nullable_to_non_nullable
                  as CustomizeCardPriceUnit?,
      ),
    );
  }

  /// Create a copy of CustomizeCardPayInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomizeCardPriceUnitCopyWith<$Res>? get priceUnit {
    if (_self.priceUnit == null) {
      return null;
    }

    return $CustomizeCardPriceUnitCopyWith<$Res>(_self.priceUnit!, (value) {
      return _then(_self.copyWith(priceUnit: value));
    });
  }

  /// Create a copy of CustomizeCardPayInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomizeCardPriceUnitCopyWith<$Res>? get shippingUnit {
    if (_self.shippingUnit == null) {
      return null;
    }

    return $CustomizeCardPriceUnitCopyWith<$Res>(_self.shippingUnit!, (value) {
      return _then(_self.copyWith(shippingUnit: value));
    });
  }
}

/// Adds pattern-matching-related methods to [CustomizeCardPayInfo].
extension CustomizeCardPayInfoPatterns on CustomizeCardPayInfo {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CustomizeCardPayInfo value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPayInfo() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CustomizeCardPayInfo value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPayInfo():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CustomizeCardPayInfo value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPayInfo() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'link') String link,
      @JsonKey(name: 'priceUnit')
      @CustomizeCardPriceUnitConverter()
      CustomizeCardPriceUnit? priceUnit,
      @JsonKey(name: 'shippingUnit')
      @CustomizeCardPriceUnitConverter()
      CustomizeCardPriceUnit? shippingUnit,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPayInfo() when $default != null:
        return $default(_that.link, _that.priceUnit, _that.shippingUnit);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'link') String link,
      @JsonKey(name: 'priceUnit')
      @CustomizeCardPriceUnitConverter()
      CustomizeCardPriceUnit? priceUnit,
      @JsonKey(name: 'shippingUnit')
      @CustomizeCardPriceUnitConverter()
      CustomizeCardPriceUnit? shippingUnit,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPayInfo():
        return $default(_that.link, _that.priceUnit, _that.shippingUnit);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'link') String link,
      @JsonKey(name: 'priceUnit')
      @CustomizeCardPriceUnitConverter()
      CustomizeCardPriceUnit? priceUnit,
      @JsonKey(name: 'shippingUnit')
      @CustomizeCardPriceUnitConverter()
      CustomizeCardPriceUnit? shippingUnit,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPayInfo() when $default != null:
        return $default(_that.link, _that.priceUnit, _that.shippingUnit);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _CustomizeCardPayInfo implements CustomizeCardPayInfo {
  const _CustomizeCardPayInfo({
    @JsonKey(name: 'link') this.link = '',
    @JsonKey(name: 'priceUnit')
    @CustomizeCardPriceUnitConverter()
    this.priceUnit,
    @JsonKey(name: 'shippingUnit')
    @CustomizeCardPriceUnitConverter()
    this.shippingUnit,
  });
  factory _CustomizeCardPayInfo.fromJson(Map<String, dynamic> json) =>
      _$CustomizeCardPayInfoFromJson(json);

  @override
  @JsonKey(name: 'link')
  final String link;
  @override
  @JsonKey(name: 'priceUnit')
  @CustomizeCardPriceUnitConverter()
  final CustomizeCardPriceUnit? priceUnit;
  @override
  @JsonKey(name: 'shippingUnit')
  @CustomizeCardPriceUnitConverter()
  final CustomizeCardPriceUnit? shippingUnit;

  /// Create a copy of CustomizeCardPayInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CustomizeCardPayInfoCopyWith<_CustomizeCardPayInfo> get copyWith =>
      __$CustomizeCardPayInfoCopyWithImpl<_CustomizeCardPayInfo>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$CustomizeCardPayInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CustomizeCardPayInfo &&
            (identical(other.link, link) || other.link == link) &&
            (identical(other.priceUnit, priceUnit) ||
                other.priceUnit == priceUnit) &&
            (identical(other.shippingUnit, shippingUnit) ||
                other.shippingUnit == shippingUnit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, link, priceUnit, shippingUnit);

  @override
  String toString() {
    return 'CustomizeCardPayInfo(link: $link, priceUnit: $priceUnit, shippingUnit: $shippingUnit)';
  }
}

/// @nodoc
abstract mixin class _$CustomizeCardPayInfoCopyWith<$Res>
    implements $CustomizeCardPayInfoCopyWith<$Res> {
  factory _$CustomizeCardPayInfoCopyWith(
    _CustomizeCardPayInfo value,
    $Res Function(_CustomizeCardPayInfo) _then,
  ) = __$CustomizeCardPayInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'link') String link,
    @JsonKey(name: 'priceUnit')
    @CustomizeCardPriceUnitConverter()
    CustomizeCardPriceUnit? priceUnit,
    @JsonKey(name: 'shippingUnit')
    @CustomizeCardPriceUnitConverter()
    CustomizeCardPriceUnit? shippingUnit,
  });

  @override
  $CustomizeCardPriceUnitCopyWith<$Res>? get priceUnit;
  @override
  $CustomizeCardPriceUnitCopyWith<$Res>? get shippingUnit;
}

/// @nodoc
class __$CustomizeCardPayInfoCopyWithImpl<$Res>
    implements _$CustomizeCardPayInfoCopyWith<$Res> {
  __$CustomizeCardPayInfoCopyWithImpl(this._self, this._then);

  final _CustomizeCardPayInfo _self;
  final $Res Function(_CustomizeCardPayInfo) _then;

  /// Create a copy of CustomizeCardPayInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? link = null,
    Object? priceUnit = freezed,
    Object? shippingUnit = freezed,
  }) {
    return _then(
      _CustomizeCardPayInfo(
        link: null == link
            ? _self.link
            : link // ignore: cast_nullable_to_non_nullable
                  as String,
        priceUnit: freezed == priceUnit
            ? _self.priceUnit
            : priceUnit // ignore: cast_nullable_to_non_nullable
                  as CustomizeCardPriceUnit?,
        shippingUnit: freezed == shippingUnit
            ? _self.shippingUnit
            : shippingUnit // ignore: cast_nullable_to_non_nullable
                  as CustomizeCardPriceUnit?,
      ),
    );
  }

  /// Create a copy of CustomizeCardPayInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomizeCardPriceUnitCopyWith<$Res>? get priceUnit {
    if (_self.priceUnit == null) {
      return null;
    }

    return $CustomizeCardPriceUnitCopyWith<$Res>(_self.priceUnit!, (value) {
      return _then(_self.copyWith(priceUnit: value));
    });
  }

  /// Create a copy of CustomizeCardPayInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomizeCardPriceUnitCopyWith<$Res>? get shippingUnit {
    if (_self.shippingUnit == null) {
      return null;
    }

    return $CustomizeCardPriceUnitCopyWith<$Res>(_self.shippingUnit!, (value) {
      return _then(_self.copyWith(shippingUnit: value));
    });
  }
}

/// @nodoc
mixin _$CustomizeCardPriceUnit {
  @JsonKey(name: 'symbol')
  String get symbol;
  @JsonKey(name: 'amount')
  String get amount;
  @JsonKey(name: 'currency')
  String get currency;

  /// Create a copy of CustomizeCardPriceUnit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CustomizeCardPriceUnitCopyWith<CustomizeCardPriceUnit> get copyWith =>
      _$CustomizeCardPriceUnitCopyWithImpl<CustomizeCardPriceUnit>(
        this as CustomizeCardPriceUnit,
        _$identity,
      );

  /// Serializes this CustomizeCardPriceUnit to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CustomizeCardPriceUnit &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, symbol, amount, currency);

  @override
  String toString() {
    return 'CustomizeCardPriceUnit(symbol: $symbol, amount: $amount, currency: $currency)';
  }
}

/// @nodoc
abstract mixin class $CustomizeCardPriceUnitCopyWith<$Res> {
  factory $CustomizeCardPriceUnitCopyWith(
    CustomizeCardPriceUnit value,
    $Res Function(CustomizeCardPriceUnit) _then,
  ) = _$CustomizeCardPriceUnitCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'amount') String amount,
    @JsonKey(name: 'currency') String currency,
  });
}

/// @nodoc
class _$CustomizeCardPriceUnitCopyWithImpl<$Res>
    implements $CustomizeCardPriceUnitCopyWith<$Res> {
  _$CustomizeCardPriceUnitCopyWithImpl(this._self, this._then);

  final CustomizeCardPriceUnit _self;
  final $Res Function(CustomizeCardPriceUnit) _then;

  /// Create a copy of CustomizeCardPriceUnit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? symbol = null,
    Object? amount = null,
    Object? currency = null,
  }) {
    return _then(
      _self.copyWith(
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _self.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as String,
        currency: null == currency
            ? _self.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [CustomizeCardPriceUnit].
extension CustomizeCardPriceUnitPatterns on CustomizeCardPriceUnit {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CustomizeCardPriceUnit value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPriceUnit() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CustomizeCardPriceUnit value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPriceUnit():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CustomizeCardPriceUnit value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPriceUnit() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'symbol') String symbol,
      @JsonKey(name: 'amount') String amount,
      @JsonKey(name: 'currency') String currency,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPriceUnit() when $default != null:
        return $default(_that.symbol, _that.amount, _that.currency);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'symbol') String symbol,
      @JsonKey(name: 'amount') String amount,
      @JsonKey(name: 'currency') String currency,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPriceUnit():
        return $default(_that.symbol, _that.amount, _that.currency);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'symbol') String symbol,
      @JsonKey(name: 'amount') String amount,
      @JsonKey(name: 'currency') String currency,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CustomizeCardPriceUnit() when $default != null:
        return $default(_that.symbol, _that.amount, _that.currency);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _CustomizeCardPriceUnit extends CustomizeCardPriceUnit {
  const _CustomizeCardPriceUnit({
    @JsonKey(name: 'symbol') this.symbol = '',
    @JsonKey(name: 'amount') this.amount = '',
    @JsonKey(name: 'currency') this.currency = '',
  }) : super._();
  factory _CustomizeCardPriceUnit.fromJson(Map<String, dynamic> json) =>
      _$CustomizeCardPriceUnitFromJson(json);

  @override
  @JsonKey(name: 'symbol')
  final String symbol;
  @override
  @JsonKey(name: 'amount')
  final String amount;
  @override
  @JsonKey(name: 'currency')
  final String currency;

  /// Create a copy of CustomizeCardPriceUnit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CustomizeCardPriceUnitCopyWith<_CustomizeCardPriceUnit> get copyWith =>
      __$CustomizeCardPriceUnitCopyWithImpl<_CustomizeCardPriceUnit>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$CustomizeCardPriceUnitToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CustomizeCardPriceUnit &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, symbol, amount, currency);

  @override
  String toString() {
    return 'CustomizeCardPriceUnit(symbol: $symbol, amount: $amount, currency: $currency)';
  }
}

/// @nodoc
abstract mixin class _$CustomizeCardPriceUnitCopyWith<$Res>
    implements $CustomizeCardPriceUnitCopyWith<$Res> {
  factory _$CustomizeCardPriceUnitCopyWith(
    _CustomizeCardPriceUnit value,
    $Res Function(_CustomizeCardPriceUnit) _then,
  ) = __$CustomizeCardPriceUnitCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'amount') String amount,
    @JsonKey(name: 'currency') String currency,
  });
}

/// @nodoc
class __$CustomizeCardPriceUnitCopyWithImpl<$Res>
    implements _$CustomizeCardPriceUnitCopyWith<$Res> {
  __$CustomizeCardPriceUnitCopyWithImpl(this._self, this._then);

  final _CustomizeCardPriceUnit _self;
  final $Res Function(_CustomizeCardPriceUnit) _then;

  /// Create a copy of CustomizeCardPriceUnit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? symbol = null,
    Object? amount = null,
    Object? currency = null,
  }) {
    return _then(
      _CustomizeCardPriceUnit(
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _self.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as String,
        currency: null == currency
            ? _self.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$ExtendProfile {
  @JsonKey(name: 'githubHandle')
  String get githubHandle;
  @JsonKey(
    name: 'topics',
    fromJson: ExtendProfile.fromJoinedString,
    toJson: ExtendProfile.toJoinedString,
  )
  List<String> get topics;
  @JsonKey(
    name: 'role',
    fromJson: ExtendProfile.fromJoinedString,
    toJson: ExtendProfile.toJoinedString,
  )
  @JsonKey(name: 'role')
  List<String> get roles;

  /// Create a copy of ExtendProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ExtendProfileCopyWith<ExtendProfile> get copyWith =>
      _$ExtendProfileCopyWithImpl<ExtendProfile>(
        this as ExtendProfile,
        _$identity,
      );

  /// Serializes this ExtendProfile to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ExtendProfile &&
            (identical(other.githubHandle, githubHandle) ||
                other.githubHandle == githubHandle) &&
            const DeepCollectionEquality().equals(other.topics, topics) &&
            const DeepCollectionEquality().equals(other.roles, roles));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    githubHandle,
    const DeepCollectionEquality().hash(topics),
    const DeepCollectionEquality().hash(roles),
  );

  @override
  String toString() {
    return 'ExtendProfile(githubHandle: $githubHandle, topics: $topics, roles: $roles)';
  }
}

/// @nodoc
abstract mixin class $ExtendProfileCopyWith<$Res> {
  factory $ExtendProfileCopyWith(
    ExtendProfile value,
    $Res Function(ExtendProfile) _then,
  ) = _$ExtendProfileCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'githubHandle') String githubHandle,
    @JsonKey(
      name: 'topics',
      fromJson: ExtendProfile.fromJoinedString,
      toJson: ExtendProfile.toJoinedString,
    )
    List<String> topics,
    @JsonKey(
      name: 'role',
      fromJson: ExtendProfile.fromJoinedString,
      toJson: ExtendProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    List<String> roles,
  });
}

/// @nodoc
class _$ExtendProfileCopyWithImpl<$Res>
    implements $ExtendProfileCopyWith<$Res> {
  _$ExtendProfileCopyWithImpl(this._self, this._then);

  final ExtendProfile _self;
  final $Res Function(ExtendProfile) _then;

  /// Create a copy of ExtendProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? githubHandle = null,
    Object? topics = null,
    Object? roles = null,
  }) {
    return _then(
      _self.copyWith(
        githubHandle: null == githubHandle
            ? _self.githubHandle
            : githubHandle // ignore: cast_nullable_to_non_nullable
                  as String,
        topics: null == topics
            ? _self.topics
            : topics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        roles: null == roles
            ? _self.roles
            : roles // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [ExtendProfile].
extension ExtendProfilePatterns on ExtendProfile {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ExtendProfile value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ExtendProfile() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ExtendProfile value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ExtendProfile():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ExtendProfile value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ExtendProfile() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'githubHandle') String githubHandle,
      @JsonKey(
        name: 'topics',
        fromJson: ExtendProfile.fromJoinedString,
        toJson: ExtendProfile.toJoinedString,
      )
      List<String> topics,
      @JsonKey(
        name: 'role',
        fromJson: ExtendProfile.fromJoinedString,
        toJson: ExtendProfile.toJoinedString,
      )
      @JsonKey(name: 'role')
      List<String> roles,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ExtendProfile() when $default != null:
        return $default(_that.githubHandle, _that.topics, _that.roles);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'githubHandle') String githubHandle,
      @JsonKey(
        name: 'topics',
        fromJson: ExtendProfile.fromJoinedString,
        toJson: ExtendProfile.toJoinedString,
      )
      List<String> topics,
      @JsonKey(
        name: 'role',
        fromJson: ExtendProfile.fromJoinedString,
        toJson: ExtendProfile.toJoinedString,
      )
      @JsonKey(name: 'role')
      List<String> roles,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ExtendProfile():
        return $default(_that.githubHandle, _that.topics, _that.roles);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'githubHandle') String githubHandle,
      @JsonKey(
        name: 'topics',
        fromJson: ExtendProfile.fromJoinedString,
        toJson: ExtendProfile.toJoinedString,
      )
      List<String> topics,
      @JsonKey(
        name: 'role',
        fromJson: ExtendProfile.fromJoinedString,
        toJson: ExtendProfile.toJoinedString,
      )
      @JsonKey(name: 'role')
      List<String> roles,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ExtendProfile() when $default != null:
        return $default(_that.githubHandle, _that.topics, _that.roles);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ExtendProfile extends ExtendProfile {
  const _ExtendProfile({
    @JsonKey(name: 'githubHandle') this.githubHandle = '',
    @JsonKey(
      name: 'topics',
      fromJson: ExtendProfile.fromJoinedString,
      toJson: ExtendProfile.toJoinedString,
    )
    final List<String> topics = const [],
    @JsonKey(
      name: 'role',
      fromJson: ExtendProfile.fromJoinedString,
      toJson: ExtendProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    final List<String> roles = const [],
  }) : _topics = topics,
       _roles = roles,
       super._();
  factory _ExtendProfile.fromJson(Map<String, dynamic> json) =>
      _$ExtendProfileFromJson(json);

  @override
  @JsonKey(name: 'githubHandle')
  final String githubHandle;
  final List<String> _topics;
  @override
  @JsonKey(
    name: 'topics',
    fromJson: ExtendProfile.fromJoinedString,
    toJson: ExtendProfile.toJoinedString,
  )
  List<String> get topics {
    if (_topics is EqualUnmodifiableListView) return _topics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topics);
  }

  final List<String> _roles;
  @override
  @JsonKey(
    name: 'role',
    fromJson: ExtendProfile.fromJoinedString,
    toJson: ExtendProfile.toJoinedString,
  )
  @JsonKey(name: 'role')
  List<String> get roles {
    if (_roles is EqualUnmodifiableListView) return _roles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_roles);
  }

  /// Create a copy of ExtendProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ExtendProfileCopyWith<_ExtendProfile> get copyWith =>
      __$ExtendProfileCopyWithImpl<_ExtendProfile>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ExtendProfileToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ExtendProfile &&
            (identical(other.githubHandle, githubHandle) ||
                other.githubHandle == githubHandle) &&
            const DeepCollectionEquality().equals(other._topics, _topics) &&
            const DeepCollectionEquality().equals(other._roles, _roles));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    githubHandle,
    const DeepCollectionEquality().hash(_topics),
    const DeepCollectionEquality().hash(_roles),
  );

  @override
  String toString() {
    return 'ExtendProfile(githubHandle: $githubHandle, topics: $topics, roles: $roles)';
  }
}

/// @nodoc
abstract mixin class _$ExtendProfileCopyWith<$Res>
    implements $ExtendProfileCopyWith<$Res> {
  factory _$ExtendProfileCopyWith(
    _ExtendProfile value,
    $Res Function(_ExtendProfile) _then,
  ) = __$ExtendProfileCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'githubHandle') String githubHandle,
    @JsonKey(
      name: 'topics',
      fromJson: ExtendProfile.fromJoinedString,
      toJson: ExtendProfile.toJoinedString,
    )
    List<String> topics,
    @JsonKey(
      name: 'role',
      fromJson: ExtendProfile.fromJoinedString,
      toJson: ExtendProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    List<String> roles,
  });
}

/// @nodoc
class __$ExtendProfileCopyWithImpl<$Res>
    implements _$ExtendProfileCopyWith<$Res> {
  __$ExtendProfileCopyWithImpl(this._self, this._then);

  final _ExtendProfile _self;
  final $Res Function(_ExtendProfile) _then;

  /// Create a copy of ExtendProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? githubHandle = null,
    Object? topics = null,
    Object? roles = null,
  }) {
    return _then(
      _ExtendProfile(
        githubHandle: null == githubHandle
            ? _self.githubHandle
            : githubHandle // ignore: cast_nullable_to_non_nullable
                  as String,
        topics: null == topics
            ? _self._topics
            : topics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        roles: null == roles
            ? _self._roles
            : roles // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
mixin _$QRCodeDynamicResult {
  @JsonKey(name: 'url')
  String get url;
  @JsonKey(
    name: 'expireTime',
    fromJson: QRCodeDynamicResult._expireFromJson,
    toJson: QRCodeDynamicResult._expireToJson,
  )
  DateTime get expireTime;
  @JsonKey(name: 'qrcodeValidity')
  int get validSeconds;

  /// Create a copy of QRCodeDynamicResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QRCodeDynamicResultCopyWith<QRCodeDynamicResult> get copyWith =>
      _$QRCodeDynamicResultCopyWithImpl<QRCodeDynamicResult>(
        this as QRCodeDynamicResult,
        _$identity,
      );

  /// Serializes this QRCodeDynamicResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QRCodeDynamicResult &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.expireTime, expireTime) ||
                other.expireTime == expireTime) &&
            (identical(other.validSeconds, validSeconds) ||
                other.validSeconds == validSeconds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, url, expireTime, validSeconds);

  @override
  String toString() {
    return 'QRCodeDynamicResult(url: $url, expireTime: $expireTime, validSeconds: $validSeconds)';
  }
}

/// @nodoc
abstract mixin class $QRCodeDynamicResultCopyWith<$Res> {
  factory $QRCodeDynamicResultCopyWith(
    QRCodeDynamicResult value,
    $Res Function(QRCodeDynamicResult) _then,
  ) = _$QRCodeDynamicResultCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'url') String url,
    @JsonKey(
      name: 'expireTime',
      fromJson: QRCodeDynamicResult._expireFromJson,
      toJson: QRCodeDynamicResult._expireToJson,
    )
    DateTime expireTime,
    @JsonKey(name: 'qrcodeValidity') int validSeconds,
  });
}

/// @nodoc
class _$QRCodeDynamicResultCopyWithImpl<$Res>
    implements $QRCodeDynamicResultCopyWith<$Res> {
  _$QRCodeDynamicResultCopyWithImpl(this._self, this._then);

  final QRCodeDynamicResult _self;
  final $Res Function(QRCodeDynamicResult) _then;

  /// Create a copy of QRCodeDynamicResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = null,
    Object? expireTime = null,
    Object? validSeconds = null,
  }) {
    return _then(
      _self.copyWith(
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        expireTime: null == expireTime
            ? _self.expireTime
            : expireTime // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        validSeconds: null == validSeconds
            ? _self.validSeconds
            : validSeconds // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [QRCodeDynamicResult].
extension QRCodeDynamicResultPatterns on QRCodeDynamicResult {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_QRCodeDynamicResult value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _QRCodeDynamicResult() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_QRCodeDynamicResult value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QRCodeDynamicResult():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_QRCodeDynamicResult value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QRCodeDynamicResult() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'url') String url,
      @JsonKey(
        name: 'expireTime',
        fromJson: QRCodeDynamicResult._expireFromJson,
        toJson: QRCodeDynamicResult._expireToJson,
      )
      DateTime expireTime,
      @JsonKey(name: 'qrcodeValidity') int validSeconds,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _QRCodeDynamicResult() when $default != null:
        return $default(_that.url, _that.expireTime, _that.validSeconds);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'url') String url,
      @JsonKey(
        name: 'expireTime',
        fromJson: QRCodeDynamicResult._expireFromJson,
        toJson: QRCodeDynamicResult._expireToJson,
      )
      DateTime expireTime,
      @JsonKey(name: 'qrcodeValidity') int validSeconds,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QRCodeDynamicResult():
        return $default(_that.url, _that.expireTime, _that.validSeconds);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'url') String url,
      @JsonKey(
        name: 'expireTime',
        fromJson: QRCodeDynamicResult._expireFromJson,
        toJson: QRCodeDynamicResult._expireToJson,
      )
      DateTime expireTime,
      @JsonKey(name: 'qrcodeValidity') int validSeconds,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QRCodeDynamicResult() when $default != null:
        return $default(_that.url, _that.expireTime, _that.validSeconds);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _QRCodeDynamicResult implements QRCodeDynamicResult {
  const _QRCodeDynamicResult({
    @JsonKey(name: 'url') required this.url,
    @JsonKey(
      name: 'expireTime',
      fromJson: QRCodeDynamicResult._expireFromJson,
      toJson: QRCodeDynamicResult._expireToJson,
    )
    required this.expireTime,
    @JsonKey(name: 'qrcodeValidity') required this.validSeconds,
  });
  factory _QRCodeDynamicResult.fromJson(Map<String, dynamic> json) =>
      _$QRCodeDynamicResultFromJson(json);

  @override
  @JsonKey(name: 'url')
  final String url;
  @override
  @JsonKey(
    name: 'expireTime',
    fromJson: QRCodeDynamicResult._expireFromJson,
    toJson: QRCodeDynamicResult._expireToJson,
  )
  final DateTime expireTime;
  @override
  @JsonKey(name: 'qrcodeValidity')
  final int validSeconds;

  /// Create a copy of QRCodeDynamicResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QRCodeDynamicResultCopyWith<_QRCodeDynamicResult> get copyWith =>
      __$QRCodeDynamicResultCopyWithImpl<_QRCodeDynamicResult>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$QRCodeDynamicResultToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QRCodeDynamicResult &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.expireTime, expireTime) ||
                other.expireTime == expireTime) &&
            (identical(other.validSeconds, validSeconds) ||
                other.validSeconds == validSeconds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, url, expireTime, validSeconds);

  @override
  String toString() {
    return 'QRCodeDynamicResult(url: $url, expireTime: $expireTime, validSeconds: $validSeconds)';
  }
}

/// @nodoc
abstract mixin class _$QRCodeDynamicResultCopyWith<$Res>
    implements $QRCodeDynamicResultCopyWith<$Res> {
  factory _$QRCodeDynamicResultCopyWith(
    _QRCodeDynamicResult value,
    $Res Function(_QRCodeDynamicResult) _then,
  ) = __$QRCodeDynamicResultCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'url') String url,
    @JsonKey(
      name: 'expireTime',
      fromJson: QRCodeDynamicResult._expireFromJson,
      toJson: QRCodeDynamicResult._expireToJson,
    )
    DateTime expireTime,
    @JsonKey(name: 'qrcodeValidity') int validSeconds,
  });
}

/// @nodoc
class __$QRCodeDynamicResultCopyWithImpl<$Res>
    implements _$QRCodeDynamicResultCopyWith<$Res> {
  __$QRCodeDynamicResultCopyWithImpl(this._self, this._then);

  final _QRCodeDynamicResult _self;
  final $Res Function(_QRCodeDynamicResult) _then;

  /// Create a copy of QRCodeDynamicResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? url = null,
    Object? expireTime = null,
    Object? validSeconds = null,
  }) {
    return _then(
      _QRCodeDynamicResult(
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        expireTime: null == expireTime
            ? _self.expireTime
            : expireTime // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        validSeconds: null == validSeconds
            ? _self.validSeconds
            : validSeconds // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}
