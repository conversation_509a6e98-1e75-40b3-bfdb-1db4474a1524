import 'dart:convert' show jsonDecode;

import 'package:collection/collection.dart';
import 'package:flutter/material.dart' show Color, TextInputType;
import 'package:freezed_annotation/freezed_annotation.dart';

import '/constants/envs.dart' show envIsProd;
import '/models/business.dart' show ImagePickResult, ImagePickUrlResult;

part 'card.freezed.dart';

part 'card.g.dart';

const groupPreviewLogoSize = 100.0;

const eventIdAllowAll = 0;

enum NfcType {
  @JsonValue('NFC215')
  NFC215,
  @JsonValue('NFC424')
  NFC424,
}

enum CardType {
  @JsonValue('STICKER')
  STICKER,
  @JsonValue('CARD')
  CARD,
  @JsonValue('WRISTBAND')
  WRISTBAND,
}

enum PrintType {
  @JsonValue('METAL')
  METAL,
  @JsonValue('NORMAL')
  NORMAL,
}

enum CardPayStatus {
  @JsonValue('NOT_PAYED')
  NOT_PAYED,
  @JsonValue('PAYED')
  PAYED,
  @JsonValue('FREE')
  FREE;

  bool get paid => this == CardPayStatus.FREE || this == CardPayStatus.PAYED;
}

@freezed
sealed class Social with _$Social {
  const factory Social({
    @JsonKey(name: 'id') required int id,
    @JsonKey(name: 'platformName') @Default('') String platformName,
    @JsonKey(name: 'platformUrl') @Default('') String platformUrl,
    @JsonKey(name: 'handleName') @Default('') String handleName,
    @JsonKey(name: 'qrcode') @Default('') String imageUrl,
    @JsonKey(name: 'isVerify') @Default(false) bool verified,
  }) = _Social;

  factory Social.fromJson(Map<String, dynamic> json) => _$SocialFromJson(json);
}

/// 社交平台配置
enum SocialPlatform {
  twitter(
    name: 'Twitter',
    alias: 'X (Twitter)',
    url: 'https://x.com/',
    addon: '@',
    placeholder: 'Handle',
    demoImageKey: 'X-Twitter',
    iconImageKey: 'twitter',
    keyboardType: TextInputType.twitter,
  ),
  telegram(
    name: 'Telegram',
    url: 'https://t.me/',
    addon: '@',
    placeholder: 'Username',
    demoImageKey: 'Telegram',
    iconImageKey: 'telegram',
    keyboardType: TextInputType.twitter,
  ),
  instagram(
    name: 'Instagram',
    url: 'https://www.instagram.com/',
    addon: '@',
    placeholder: 'Username',
    demoImageKey: 'Instagram',
    iconImageKey: 'instagram',
    keyboardType: TextInputType.twitter,
  ),
  linkedIn(
    name: 'LinkedIn',
    url: 'https://www.linkedin.com/in/',
    placeholder: 'Profile Username',
    demoImageKey: 'LinkedIn',
    iconImageKey: 'linkedIn',
    keyboardType: TextInputType.name,
  ),
  farcaster(
    name: 'Farcaster',
    url: 'https://farcaster.xyz/',
    addon: '@',
    placeholder: 'Username',
    demoImageKey: 'Farcaster',
    iconImageKey: 'farcaster',
    keyboardType: TextInputType.twitter,
  ),
  memex(
    name: 'MemeX',
    url: 'https://app.memex.xyz/profile/',
    borderColor: Color(0xFFAAAAAA),
    placeholder: 'Link to your MemeX profile',
    demoImageKey: 'MemeX',
    iconImageKey: 'memex',
    keyboardType: TextInputType.number,
  ),
  whatsapp(
    name: 'Whatsapp',
    url: 'https://wa.me/',
    placeholder: 'Phone Number',
    iconImageKey: 'whatsapp',
    keyboardType: TextInputType.phone,
  ),
  github(
    name: 'Github',
    url: 'https://github.com/',
    addon: '@',
    placeholder: 'Username',
    demoImageKey: 'GitHub',
    iconImageKey: 'github',
    keyboardType: TextInputType.twitter,
  ),
  discord(
    name: 'Discord',
    url: 'https://discord.gg/',
    placeholder: 'Server Name',
    iconImageKey: 'discord',
    keyboardType: TextInputType.name,
  ),
  tiktok(
    name: 'TikTok',
    url: 'https://www.tiktok.com/@',
    addon: '@',
    placeholder: 'Username',
    demoImageKey: 'TikTok',
    iconImageKey: 'tiktok',
    keyboardType: TextInputType.twitter,
  ),
  linktree(
    name: 'Linktree',
    url: 'https://linktr.ee/',
    placeholder: 'Username',
    demoImageKey: 'Linktree',
    iconImageKey: 'linkTree',
    keyboardType: TextInputType.name,
  ),
  youtube(
    name: 'Youtube',
    url: 'https://www.youtube.com/@',
    addon: '@',
    placeholder: 'Username',
    demoImageKey: 'YouTube',
    iconImageKey: 'youtube',
    keyboardType: TextInputType.twitter,
  ),
  calendly(
    name: 'Calendly',
    url: 'https://calendly.com/',
    addon: '@',
    placeholder: 'Username',
    iconImageKey: 'calendly',
    keyboardType: TextInputType.twitter,
  ),
  ordme(
    name: 'Ord.me',
    url: 'https://www.ord.me/',
    addon: '@',
    placeholder: 'Username',
    iconImageKey: 'ordme',
    keyboardType: TextInputType.twitter,
  ),
  zalo(
    name: 'Zalo',
    url: 'https://zalo.me/',
    placeholder: 'Number',
    iconImageKey: 'zalo',
  ),
  line(
    name: 'LINE',
    url: 'https://line.me/ti/p/',
    placeholder: 'Your link',
    iconImageKey: 'line',
    keyboardType: TextInputType.url,
  ),
  wechat(
    name: 'WeChat',
    iconImageKey: 'wechat',
    allowImage: true,
    allowHandle: false,
    imageUploadTip: 'Upload QR Code',
    imageExtraTip: 'Find it in WeChat -> Me -> My QR Code',
  ),
  kakao(
    name: 'KakaoTalk',
    iconImageKey: 'kakao',
    allowImage: true,
    allowHandle: false,
    imageUploadTip: 'Upload QR Code',
    imageExtraTip: 'Find it in KakaoTalk -> More -> QR Code',
  ),
  phone(
    name: 'Phone',
    url: 'tel:+',
    placeholder: 'Your phone number',
    iconImageKey: 'phone',
    keyboardType: TextInputType.phone,
  ),
  email(
    name: 'Email',
    url: 'mailto:',
    placeholder: 'Your email',
    iconImageKey: 'email',
    keyboardType: TextInputType.emailAddress,
  ),
  link(
    name: 'Custom Link',
    url: 'https://',
    placeholder: 'Your link',
    iconImageKey: 'link',
    keyboardType: TextInputType.url,
  );

  const SocialPlatform({
    required this.name,
    this.alias,
    this.url = '',
    this.borderColor,
    this.placeholder,
    this.addon,
    this.demoImageKey,
    required this.iconImageKey,
    this.keyboardType,
    this.allowHandle = true,
    this.allowImage = false,
    this.imageUploadTip,
    this.imageExtraTip,
  });

  final String name;
  final String? alias;
  final String url;
  final Color? borderColor;
  final String? placeholder;
  final String? addon;
  final String? demoImageKey;
  final String iconImageKey;
  final TextInputType? keyboardType;
  final bool allowHandle;
  final bool allowImage;
  final String? imageUploadTip;
  final String? imageExtraTip;

  static SocialPlatform? fromName(String? name) {
    if (name == null || name.isEmpty) {
      return null;
    }
    return SocialPlatform.values.firstWhereOrNull((e) => e.name == name);
  }

  String? validateHandle(String? value) {
    if (!allowHandle) {
      return null;
    }

    if (value == null || value.trim().isEmpty) {
      return 'Required value cannot be empty.';
    }

    value = formalizeHandle(value);
    if (value.trim().isEmpty) {
      return 'Required value cannot be empty.';
    }

    if (this == SocialPlatform.email) {
      final emailRegex = RegExp(r'^[\w-.]+@([\w-]+\.)+[\w-]{2,}$');
      if (!emailRegex.hasMatch(value)) {
        return 'Please enter a valid email address.';
      }
    } else if (this == SocialPlatform.phone) {
      final regexp = RegExp(r'^(\+?\d-?){2,}\d+$');
      if (!regexp.hasMatch(value)) {
        return 'Please enter a valid phone number.';
      }
    } else if (this == SocialPlatform.line) {
      final regexp = RegExp(r'^(https://line\.me/ti/p/)?(\S{4,})$');
      if (!regexp.hasMatch(value)) {
        return 'Please enter a valid LINE ID.';
      }
    } else if (this == SocialPlatform.link) {
      final regexp = RegExp(
        r'^(https?://)?(?!-)[A-Za-z0-9-]{1,63}(?<!-)(\.[A-Za-z0-9-]{1,63}(?<!-))*(\.[A-Za-z]{2,})',
        caseSensitive: false,
      );
      if (!regexp.hasMatch(value)) {
        return 'Please enter a valid URL.';
      }
    }
    return null;
  }

  String formalizeHandle(String handle) {
    handle = handle.trim();
    if (url case final url when url.isNotEmpty) {
      handle = handle.replaceFirst(RegExp('^$url'), '');
    }
    if (addon case final addon? when addon.isNotEmpty) {
      handle = handle.replaceFirst(RegExp('^$addon'), '');
    }
    if (this == SocialPlatform.line) {
      handle = handle.replaceFirst('https://line.me/ti/p/', '');
    }
    if (keyboardType == TextInputType.phone) {
      handle = handle.replaceFirst(RegExp(r'^＋*'), '').replaceFirst(RegExp(r'^\+*'), '');
    }
    return handle.trim();
  }

  String? validateImage(ImagePickResult? result) {
    if (!allowImage || allowHandle) {
      return null;
    }
    if (result == null) {
      return 'Required value cannot be empty.';
    }
    if (result is ImagePickUrlResult) {
      return 'Image does not changed.';
    }
    return null;
  }

  List<int>? get events {
    switch (this) {
      case SocialPlatform.ordme:
        return [
          120,
          if (envIsProd) ...[122] else ...[121, 125],
        ];
      default:
        return null;
    }
  }
}

abstract interface class ICardInfoActivated {
  String get redirectUrl;

  String get referralCode;
}

@freezed
sealed class CardInfoBasic with _$CardInfoBasic {
  @Implements<ICardInfoActivated>()
  const factory CardInfoBasic.activated({
    @JsonKey(name: 'activated') required bool activated,
    @JsonKey(name: 'redirectUrl') @Default('') String redirectUrl,
    @JsonKey(name: 'referralCode') required String referralCode,
  }) = CardInfoBasicActivated;

  const factory CardInfoBasic.inactivated({
    @JsonKey(name: 'id') required int id,
    @JsonKey(name: 'activated') required bool activated,
    @JsonKey(name: 'cardType') required CardType cardType,
    @JsonKey(name: 'backCover') @Default('') String backCover,
    @JsonKey(name: 'redirectUrl') @Default('') String redirectUrl,
  }) = CardInfoBasicInactivated;

  /// Use [CardInfoBasicConverter] to get rid of union type requirements.
  factory CardInfoBasic.fromJson(Map<String, dynamic> json) => CardInfoBasicConverter.fromJson(json);
}

abstract final class CardInfoBasicConverter {
  static CardInfoBasic fromJson(Map<String, dynamic> json) {
    if (json['activated'] == true) {
      return CardInfoBasicActivated.fromJson(json);
    } else {
      return CardInfoBasicInactivated.fromJson(json);
    }
  }
}

@freezed
sealed class CardInfo with _$CardInfo implements ICardInfoActivated {
  @Implements<ICardInfoActivated>()
  const factory CardInfo({
    @JsonKey(name: 'referralCode') @Default('') String referralCode,
    @JsonKey(name: 'redirectUrl') @Default('') String redirectUrl,

    @JsonKey(name: 'active') required bool activated,
    @JsonKey(name: 'activeTime') @Default('') String activeTime,
    @JsonKey(name: 'backCover') @Default('') String backCover,
    @JsonKey(name: 'cardCode') @Default('') String cardCode,
    @JsonKey(name: 'cardType') @Default(CardType.CARD) CardType cardType,
    @JsonKey(name: 'chainId') @Default(0) int chainId,
    @JsonKey(name: 'card3EventId') @Default(0) int eventId,
    @JsonKey(name: 'eventName') @Default('') String eventName,
    @JsonKey(name: 'id') @Default(0) int id,
    @JsonKey(name: 'isActive') @Default(false) bool isActive,
    @JsonKey(name: 'virtualCard') @Default(false) bool virtualCard,
    @JsonKey(name: 'nfcType') @Default(NfcType.NFC215) NfcType nfcType,
  }) = _CardInfo;

  factory CardInfo.fromJson(Map<String, dynamic> json) => _$CardInfoFromJson(json);
}

@freezed
sealed class CoverInfo with _$CoverInfo {
  const factory CoverInfo({
    @JsonKey(name: 'activeMode') @Default('') String activeMode,
    @JsonKey(name: 'backCover') @Default('') String backCover,
    @JsonKey(name: 'eventId') @Default('') String eventId,
    @JsonKey(name: 'eventName') @Default('') String eventName,
    @JsonKey(name: 'paymentLink') @Default('') String paymentLink,
    @JsonKey(name: 'price') @Default(0) int price,
    @JsonKey(name: 'priceDescription') @Default('') String priceDescription,
    @JsonKey(name: 'printType') @Default(PrintType.NORMAL) PrintType printType,
    @JsonKey(name: 'thirdPartyLink') @Default('') String thirdPartyLink,
  }) = _CoverInfo;

  factory CoverInfo.fromJson(Map<String, dynamic> json) => _$CoverInfoFromJson(json);
}

@freezed
sealed class CustomizeCardOrder with _$CustomizeCardOrder {
  const factory CustomizeCardOrder({
    @JsonKey(name: 'username') required String name,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'company') @Default('') String company,
    @JsonKey(name: 'image') @Default('') String image,
    @JsonKey(name: 'code') required String code,
    @JsonKey(name: 'preview') @Default('') String preview,
    @JsonKey(name: 'backCover') @Default('') String backCover,
    @JsonKey(name: 'eventName') @Default('') String eventName,
    @JsonKey(name: 'isPrinted') @Default(false) bool printed,
    @JsonKey(name: 'printType') required PrintType printType,
    @JsonKey(name: 'trackingNumber') @Default('') String trackingNumber,
    @JsonKey(name: 'logisticsCompany') @Default('') String trackingCompany,
    @JsonKey(name: 'price') @Default(0) int payPrice,
    @JsonKey(name: 'payStatus') @Default(CardPayStatus.FREE) CardPayStatus payStatus,
    @JsonKey(name: 'paymentLink') @Default('') String paymentLink,
    @JsonKey(name: 'thirdPartyLink', readValue: CustomizeCardPayInfo.readValue) required CustomizeCardPayInfo payInfo,
  }) = _CustomizeCardOrder;

  factory CustomizeCardOrder.fromJson(Map<String, dynamic> json) => _$CustomizeCardOrderFromJson(json);
}

@freezed
sealed class CustomizeCardPayInfo with _$CustomizeCardPayInfo {
  const factory CustomizeCardPayInfo({
    @JsonKey(name: 'link') @Default('') String link,
    @JsonKey(name: 'priceUnit') @CustomizeCardPriceUnitConverter() CustomizeCardPriceUnit? priceUnit,
    @JsonKey(name: 'shippingUnit') @CustomizeCardPriceUnitConverter() CustomizeCardPriceUnit? shippingUnit,
  }) = _CustomizeCardPayInfo;

  factory CustomizeCardPayInfo.fromJson(Map<String, dynamic> json) => _$CustomizeCardPayInfoFromJson(json);

  static Map<String, dynamic> readValue(Map json, String key) {
    return jsonDecode(json[key]);
  }
}

@freezed
sealed class CustomizeCardPriceUnit with _$CustomizeCardPriceUnit {
  const factory CustomizeCardPriceUnit({
    @JsonKey(name: 'symbol') @Default('') String symbol,
    @JsonKey(name: 'amount') @Default('') String amount,
    @JsonKey(name: 'currency') @Default('') String currency,
  }) = _CustomizeCardPriceUnit;

  const CustomizeCardPriceUnit._();

  factory CustomizeCardPriceUnit.fromJson(Map<String, dynamic> json) => _$CustomizeCardPriceUnitFromJson(json);

  String get display => '$symbol $amount $currency';
}

final class CustomizeCardPriceUnitConverter implements JsonConverter<CustomizeCardPriceUnit?, String?> {
  const CustomizeCardPriceUnitConverter();

  @override
  CustomizeCardPriceUnit? fromJson(String? json) {
    if (json?.trim().split('/') case [
      final symbol,
      final amount,
      final currency,
    ]) {
      return CustomizeCardPriceUnit(symbol: symbol, amount: amount, currency: currency);
    }
    return null;
  }

  @override
  String? toJson(CustomizeCardPriceUnit? object) {
    if (object == null) {
      return null;
    }
    return '${object.symbol}/${object.amount}/${object.currency}';
  }
}

@freezed
sealed class ExtendProfile with _$ExtendProfile {
  const factory ExtendProfile({
    @JsonKey(name: 'githubHandle') @Default('') String githubHandle,
    @JsonKey(
      name: 'topics',
      fromJson: ExtendProfile.fromJoinedString,
      toJson: ExtendProfile.toJoinedString,
    )
    @Default([])
    List<String> topics,
    @JsonKey(
      name: 'role',
      fromJson: ExtendProfile.fromJoinedString,
      toJson: ExtendProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    @Default([])
    List<String> roles,
  }) = _ExtendProfile;

  const ExtendProfile._();

  factory ExtendProfile.fromJson(Map<String, dynamic> json) => _$ExtendProfileFromJson(json);

  static List<String> fromJoinedString(String value) {
    value = value.trim();
    if (value.isEmpty) {
      return [];
    }
    return value.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
  }

  static String toJoinedString(List<String> value) => value.map((e) => e.trim()).where((e) => e.isNotEmpty).join(',');

  bool get isEmpty => githubHandle.isEmpty && topics.isEmpty && roles.isEmpty;
}

@freezed
sealed class QRCodeDynamicResult with _$QRCodeDynamicResult {
  const factory QRCodeDynamicResult({
    @JsonKey(name: 'url') required String url,
    @JsonKey(
      name: 'expireTime',
      fromJson: QRCodeDynamicResult._expireFromJson,
      toJson: QRCodeDynamicResult._expireToJson,
    )
    required DateTime expireTime,
    @JsonKey(name: 'qrcodeValidity') required int validSeconds,
  }) = _QRCodeDynamicResult;

  factory QRCodeDynamicResult.fromJson(Map<String, dynamic> json) => _$QRCodeDynamicResultFromJson(json);

  static DateTime _expireFromJson(int value) {
    return DateTime.fromMillisecondsSinceEpoch(value * 1000);
  }

  static int _expireToJson(DateTime value) {
    return value.millisecondsSinceEpoch ~/ 1000;
  }
}
