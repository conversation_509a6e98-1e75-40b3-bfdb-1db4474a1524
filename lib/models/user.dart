import 'dart:io' show Platform;

import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart' show BuildContext;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart';

import '/constants/envs.dart' show envUrlShort, envUrlWebsite;

part 'user.freezed.dart';

part 'user.g.dart';

@freezed
sealed class UserInfo with _$UserInfo implements UserWithAvatar {
  @Implements<UserWithAvatar>()
  const factory UserInfo({
    @Json<PERSON>ey(name: 'userEmail') @Default('') String userEmail,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'handle') @Default('') String handle,
    @J<PERSON><PERSON><PERSON>(name: 'name') @Default('') String name,
    @JsonKey(name: 'title') @Default('') String title,
    @Json<PERSON>ey(name: 'company') @Default('') String company,
    @JsonKey(name: 'avatar') @Default('') String avatar,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'dynamicAvatar') @Default('') String dynamicAvatar,
    @JsonKey(name: 'referralCode') @Default('') String referralCode,
    @JsonKey(name: 'cardCode') @Default('') String cardCode,
    @JsonKey(name: 'lastMessageId') @Default(0) int lastMessageId,
    @JsonKey(name: 'latestMessageId') @Default(0) int latestMessageId,
    @JsonKey(name: 'integral') @Default(0) int integralPoints,
    @JsonKey(name: 'firstConnectDate') DateTime? firstConnectDate,
  }) = _UserInfo;

  const UserInfo._();

  factory UserInfo.fromJson(Map<String, dynamic> json) => _$UserInfoFromJson(json);

  String get staticLink {
    if (handle.trim() case final handle when handle.isNotEmpty) {
      return '$envUrlWebsite/$handle';
    }
    if (cardCode.trim() case final cardCode when cardCode.isNotEmpty) {
      return '$envUrlShort/$cardCode';
    }
    return '$envUrlWebsite/profile?card_code=${referralCode.trim()}';
  }
}

abstract interface class UserWithAvatar {
  String get referralCode;

  String get avatar;

  String get dynamicAvatar;
}

@freezed
sealed class Group with _$Group {
  const factory Group({
    @JsonKey(name: 'uniqId') required String id,
    @JsonKey(name: 'name') required String name,
    @JsonKey(name: 'description') required String description,
    @JsonKey(name: 'logo') @Default('') String logo,
    @JsonKey(name: 'userCount') @Default(1) int userCount,
    @JsonKey(name: 'creator') required String creatorName,
  }) = _Group;

  factory Group.fromJson(Map<String, dynamic> json) => _$GroupFromJson(json);
}

@freezed
sealed class UserReferralRequest with _$UserReferralRequest {
  const factory UserReferralRequest({
    @JsonKey(name: 'referralCode') String? referralCode,
    @JsonKey(name: 'cardCode') String? cardCode,
    @JsonKey(name: 'handle') String? handle,
  }) = _UserReferralRequest;

  const UserReferralRequest._();

  factory UserReferralRequest.fromJson(Map<String, dynamic> json) => _$UserReferralRequestFromJson(json);

  static const empty = UserReferralRequest();

  bool get isEmpty => referralCode == null && cardCode == null && handle == null;

  Map<String, dynamic> toRequestData() => toJson()..removeAllEmptyEntry();

  String get effectiveDisplay => referralCode ?? cardCode ?? handle ?? '';
}

@freezed
sealed class UserSettingsRequest with _$UserSettingsRequest {
  const factory UserSettingsRequest({
    @JsonKey(name: 'fcmAndroid', includeIfNull: false) String? fcmAndroid,
    @JsonKey(name: 'fcmIos', includeIfNull: false) String? fcmIOS,
  }) = _UserSettingsRequest;

  factory UserSettingsRequest.fromJson(Map<String, dynamic> json) => _$UserSettingsRequestFromJson(json);

  factory UserSettingsRequest.fromPlatform({required String fcm}) {
    return UserSettingsRequest(
      fcmAndroid: Platform.isAndroid ? fcm : null,
      fcmIOS: Platform.isIOS ? fcm : null,
    );
  }
}

@freezed
sealed class UserRelation with _$UserRelation {
  const factory UserRelation({
    @JsonKey(name: 'following') @Default(false) bool following,
    @JsonKey(name: 'followedBy') @Default(false) bool followedBy,
  }) = _UserRelation;

  factory UserRelation.fromJson(Map<String, dynamic> json) => _$UserRelationFromJson(json);
}

enum UserFromRelationType {
  following,
  follower;

  String displayName(BuildContext context) {
    switch (this) {
      case UserFromRelationType.following:
        return 'Following';
      case UserFromRelationType.follower:
        return 'Followers';
    }
  }

  static UserFromRelationType? fromName(String? value) {
    if (value == null || value.isEmpty) {
      return null;
    }
    return values.firstWhereOrNull((e) => e.name == value);
  }
}

@freezed
sealed class UserFromRelation with _$UserFromRelation implements UserWithAvatar {
  @Implements<UserWithAvatar>()
  const factory UserFromRelation({
    @JsonKey(name: 'referralCode') required String referralCode,
    @JsonKey(name: 'name') @Default('') String name,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'company') @Default('') String company,
    @JsonKey(name: 'avatar') @Default('') String avatar,
    @JsonKey(name: 'dynamicAvatar') @Default('') String dynamicAvatar,
    @JsonKey(name: 'following') @Default(false) bool following,
    @JsonKey(name: 'followedBy') @Default(false) bool followedBy,
  }) = _UserFromRelation;

  factory UserFromRelation.fromJson(Map<String, dynamic> json) => _$UserFromRelationFromJson(json);
}
