// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserInfo {
  @JsonKey(name: 'userEmail')
  String get userEmail;
  @JsonKey(name: 'handle')
  String get handle;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'title')
  String get title;
  @JsonKey(name: 'company')
  String get company;
  @JsonKey(name: 'avatar')
  String get avatar;
  @JsonKey(name: 'dynamicAvatar')
  String get dynamicAvatar;
  @JsonKey(name: 'referralCode')
  String get referralCode;
  @JsonKey(name: 'cardCode')
  String get cardCode;
  @JsonKey(name: 'lastMessageId')
  int get lastMessageId;
  @JsonKey(name: 'latestMessageId')
  int get latestMessageId;
  @JsonKey(name: 'integral')
  int get integralPoints;
  @JsonKey(name: 'firstConnectDate')
  DateTime? get firstConnectDate;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserInfoCopyWith<UserInfo> get copyWith =>
      _$UserInfoCopyWithImpl<UserInfo>(this as UserInfo, _$identity);

  /// Serializes this UserInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserInfo &&
            (identical(other.userEmail, userEmail) ||
                other.userEmail == userEmail) &&
            (identical(other.handle, handle) || other.handle == handle) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.dynamicAvatar, dynamicAvatar) ||
                other.dynamicAvatar == dynamicAvatar) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.lastMessageId, lastMessageId) ||
                other.lastMessageId == lastMessageId) &&
            (identical(other.latestMessageId, latestMessageId) ||
                other.latestMessageId == latestMessageId) &&
            (identical(other.integralPoints, integralPoints) ||
                other.integralPoints == integralPoints) &&
            (identical(other.firstConnectDate, firstConnectDate) ||
                other.firstConnectDate == firstConnectDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userEmail,
    handle,
    name,
    title,
    company,
    avatar,
    dynamicAvatar,
    referralCode,
    cardCode,
    lastMessageId,
    latestMessageId,
    integralPoints,
    firstConnectDate,
  );

  @override
  String toString() {
    return 'UserInfo(userEmail: $userEmail, handle: $handle, name: $name, title: $title, company: $company, avatar: $avatar, dynamicAvatar: $dynamicAvatar, referralCode: $referralCode, cardCode: $cardCode, lastMessageId: $lastMessageId, latestMessageId: $latestMessageId, integralPoints: $integralPoints, firstConnectDate: $firstConnectDate)';
  }
}

/// @nodoc
abstract mixin class $UserInfoCopyWith<$Res> {
  factory $UserInfoCopyWith(UserInfo value, $Res Function(UserInfo) _then) =
      _$UserInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'userEmail') String userEmail,
    @JsonKey(name: 'handle') String handle,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'dynamicAvatar') String dynamicAvatar,
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'lastMessageId') int lastMessageId,
    @JsonKey(name: 'latestMessageId') int latestMessageId,
    @JsonKey(name: 'integral') int integralPoints,
    @JsonKey(name: 'firstConnectDate') DateTime? firstConnectDate,
  });
}

/// @nodoc
class _$UserInfoCopyWithImpl<$Res> implements $UserInfoCopyWith<$Res> {
  _$UserInfoCopyWithImpl(this._self, this._then);

  final UserInfo _self;
  final $Res Function(UserInfo) _then;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userEmail = null,
    Object? handle = null,
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? avatar = null,
    Object? dynamicAvatar = null,
    Object? referralCode = null,
    Object? cardCode = null,
    Object? lastMessageId = null,
    Object? latestMessageId = null,
    Object? integralPoints = null,
    Object? firstConnectDate = freezed,
  }) {
    return _then(
      _self.copyWith(
        userEmail: null == userEmail
            ? _self.userEmail
            : userEmail // ignore: cast_nullable_to_non_nullable
                  as String,
        handle: null == handle
            ? _self.handle
            : handle // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        dynamicAvatar: null == dynamicAvatar
            ? _self.dynamicAvatar
            : dynamicAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        lastMessageId: null == lastMessageId
            ? _self.lastMessageId
            : lastMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        latestMessageId: null == latestMessageId
            ? _self.latestMessageId
            : latestMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        integralPoints: null == integralPoints
            ? _self.integralPoints
            : integralPoints // ignore: cast_nullable_to_non_nullable
                  as int,
        firstConnectDate: freezed == firstConnectDate
            ? _self.firstConnectDate
            : firstConnectDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [UserInfo].
extension UserInfoPatterns on UserInfo {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UserInfo value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserInfo() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UserInfo value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserInfo():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UserInfo value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserInfo() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'userEmail') String userEmail,
      @JsonKey(name: 'handle') String handle,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'title') String title,
      @JsonKey(name: 'company') String company,
      @JsonKey(name: 'avatar') String avatar,
      @JsonKey(name: 'dynamicAvatar') String dynamicAvatar,
      @JsonKey(name: 'referralCode') String referralCode,
      @JsonKey(name: 'cardCode') String cardCode,
      @JsonKey(name: 'lastMessageId') int lastMessageId,
      @JsonKey(name: 'latestMessageId') int latestMessageId,
      @JsonKey(name: 'integral') int integralPoints,
      @JsonKey(name: 'firstConnectDate') DateTime? firstConnectDate,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserInfo() when $default != null:
        return $default(
          _that.userEmail,
          _that.handle,
          _that.name,
          _that.title,
          _that.company,
          _that.avatar,
          _that.dynamicAvatar,
          _that.referralCode,
          _that.cardCode,
          _that.lastMessageId,
          _that.latestMessageId,
          _that.integralPoints,
          _that.firstConnectDate,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'userEmail') String userEmail,
      @JsonKey(name: 'handle') String handle,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'title') String title,
      @JsonKey(name: 'company') String company,
      @JsonKey(name: 'avatar') String avatar,
      @JsonKey(name: 'dynamicAvatar') String dynamicAvatar,
      @JsonKey(name: 'referralCode') String referralCode,
      @JsonKey(name: 'cardCode') String cardCode,
      @JsonKey(name: 'lastMessageId') int lastMessageId,
      @JsonKey(name: 'latestMessageId') int latestMessageId,
      @JsonKey(name: 'integral') int integralPoints,
      @JsonKey(name: 'firstConnectDate') DateTime? firstConnectDate,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserInfo():
        return $default(
          _that.userEmail,
          _that.handle,
          _that.name,
          _that.title,
          _that.company,
          _that.avatar,
          _that.dynamicAvatar,
          _that.referralCode,
          _that.cardCode,
          _that.lastMessageId,
          _that.latestMessageId,
          _that.integralPoints,
          _that.firstConnectDate,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'userEmail') String userEmail,
      @JsonKey(name: 'handle') String handle,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'title') String title,
      @JsonKey(name: 'company') String company,
      @JsonKey(name: 'avatar') String avatar,
      @JsonKey(name: 'dynamicAvatar') String dynamicAvatar,
      @JsonKey(name: 'referralCode') String referralCode,
      @JsonKey(name: 'cardCode') String cardCode,
      @JsonKey(name: 'lastMessageId') int lastMessageId,
      @JsonKey(name: 'latestMessageId') int latestMessageId,
      @JsonKey(name: 'integral') int integralPoints,
      @JsonKey(name: 'firstConnectDate') DateTime? firstConnectDate,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserInfo() when $default != null:
        return $default(
          _that.userEmail,
          _that.handle,
          _that.name,
          _that.title,
          _that.company,
          _that.avatar,
          _that.dynamicAvatar,
          _that.referralCode,
          _that.cardCode,
          _that.lastMessageId,
          _that.latestMessageId,
          _that.integralPoints,
          _that.firstConnectDate,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _UserInfo extends UserInfo implements UserWithAvatar {
  const _UserInfo({
    @JsonKey(name: 'userEmail') this.userEmail = '',
    @JsonKey(name: 'handle') this.handle = '',
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'title') this.title = '',
    @JsonKey(name: 'company') this.company = '',
    @JsonKey(name: 'avatar') this.avatar = '',
    @JsonKey(name: 'dynamicAvatar') this.dynamicAvatar = '',
    @JsonKey(name: 'referralCode') this.referralCode = '',
    @JsonKey(name: 'cardCode') this.cardCode = '',
    @JsonKey(name: 'lastMessageId') this.lastMessageId = 0,
    @JsonKey(name: 'latestMessageId') this.latestMessageId = 0,
    @JsonKey(name: 'integral') this.integralPoints = 0,
    @JsonKey(name: 'firstConnectDate') this.firstConnectDate,
  }) : super._();
  factory _UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);

  @override
  @JsonKey(name: 'userEmail')
  final String userEmail;
  @override
  @JsonKey(name: 'handle')
  final String handle;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'title')
  final String title;
  @override
  @JsonKey(name: 'company')
  final String company;
  @override
  @JsonKey(name: 'avatar')
  final String avatar;
  @override
  @JsonKey(name: 'dynamicAvatar')
  final String dynamicAvatar;
  @override
  @JsonKey(name: 'referralCode')
  final String referralCode;
  @override
  @JsonKey(name: 'cardCode')
  final String cardCode;
  @override
  @JsonKey(name: 'lastMessageId')
  final int lastMessageId;
  @override
  @JsonKey(name: 'latestMessageId')
  final int latestMessageId;
  @override
  @JsonKey(name: 'integral')
  final int integralPoints;
  @override
  @JsonKey(name: 'firstConnectDate')
  final DateTime? firstConnectDate;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserInfoCopyWith<_UserInfo> get copyWith =>
      __$UserInfoCopyWithImpl<_UserInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserInfo &&
            (identical(other.userEmail, userEmail) ||
                other.userEmail == userEmail) &&
            (identical(other.handle, handle) || other.handle == handle) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.dynamicAvatar, dynamicAvatar) ||
                other.dynamicAvatar == dynamicAvatar) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.lastMessageId, lastMessageId) ||
                other.lastMessageId == lastMessageId) &&
            (identical(other.latestMessageId, latestMessageId) ||
                other.latestMessageId == latestMessageId) &&
            (identical(other.integralPoints, integralPoints) ||
                other.integralPoints == integralPoints) &&
            (identical(other.firstConnectDate, firstConnectDate) ||
                other.firstConnectDate == firstConnectDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userEmail,
    handle,
    name,
    title,
    company,
    avatar,
    dynamicAvatar,
    referralCode,
    cardCode,
    lastMessageId,
    latestMessageId,
    integralPoints,
    firstConnectDate,
  );

  @override
  String toString() {
    return 'UserInfo(userEmail: $userEmail, handle: $handle, name: $name, title: $title, company: $company, avatar: $avatar, dynamicAvatar: $dynamicAvatar, referralCode: $referralCode, cardCode: $cardCode, lastMessageId: $lastMessageId, latestMessageId: $latestMessageId, integralPoints: $integralPoints, firstConnectDate: $firstConnectDate)';
  }
}

/// @nodoc
abstract mixin class _$UserInfoCopyWith<$Res>
    implements $UserInfoCopyWith<$Res> {
  factory _$UserInfoCopyWith(_UserInfo value, $Res Function(_UserInfo) _then) =
      __$UserInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'userEmail') String userEmail,
    @JsonKey(name: 'handle') String handle,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'dynamicAvatar') String dynamicAvatar,
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'lastMessageId') int lastMessageId,
    @JsonKey(name: 'latestMessageId') int latestMessageId,
    @JsonKey(name: 'integral') int integralPoints,
    @JsonKey(name: 'firstConnectDate') DateTime? firstConnectDate,
  });
}

/// @nodoc
class __$UserInfoCopyWithImpl<$Res> implements _$UserInfoCopyWith<$Res> {
  __$UserInfoCopyWithImpl(this._self, this._then);

  final _UserInfo _self;
  final $Res Function(_UserInfo) _then;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userEmail = null,
    Object? handle = null,
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? avatar = null,
    Object? dynamicAvatar = null,
    Object? referralCode = null,
    Object? cardCode = null,
    Object? lastMessageId = null,
    Object? latestMessageId = null,
    Object? integralPoints = null,
    Object? firstConnectDate = freezed,
  }) {
    return _then(
      _UserInfo(
        userEmail: null == userEmail
            ? _self.userEmail
            : userEmail // ignore: cast_nullable_to_non_nullable
                  as String,
        handle: null == handle
            ? _self.handle
            : handle // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        dynamicAvatar: null == dynamicAvatar
            ? _self.dynamicAvatar
            : dynamicAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        lastMessageId: null == lastMessageId
            ? _self.lastMessageId
            : lastMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        latestMessageId: null == latestMessageId
            ? _self.latestMessageId
            : latestMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        integralPoints: null == integralPoints
            ? _self.integralPoints
            : integralPoints // ignore: cast_nullable_to_non_nullable
                  as int,
        firstConnectDate: freezed == firstConnectDate
            ? _self.firstConnectDate
            : firstConnectDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
mixin _$Group {
  @JsonKey(name: 'uniqId')
  String get id;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'description')
  String get description;
  @JsonKey(name: 'logo')
  String get logo;
  @JsonKey(name: 'userCount')
  int get userCount;
  @JsonKey(name: 'creator')
  String get creatorName;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GroupCopyWith<Group> get copyWith =>
      _$GroupCopyWithImpl<Group>(this as Group, _$identity);

  /// Serializes this Group to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Group &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.userCount, userCount) ||
                other.userCount == userCount) &&
            (identical(other.creatorName, creatorName) ||
                other.creatorName == creatorName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    logo,
    userCount,
    creatorName,
  );

  @override
  String toString() {
    return 'Group(id: $id, name: $name, description: $description, logo: $logo, userCount: $userCount, creatorName: $creatorName)';
  }
}

/// @nodoc
abstract mixin class $GroupCopyWith<$Res> {
  factory $GroupCopyWith(Group value, $Res Function(Group) _then) =
      _$GroupCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'logo') String logo,
    @JsonKey(name: 'userCount') int userCount,
    @JsonKey(name: 'creator') String creatorName,
  });
}

/// @nodoc
class _$GroupCopyWithImpl<$Res> implements $GroupCopyWith<$Res> {
  _$GroupCopyWithImpl(this._self, this._then);

  final Group _self;
  final $Res Function(Group) _then;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? logo = null,
    Object? userCount = null,
    Object? creatorName = null,
  }) {
    return _then(
      _self.copyWith(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        userCount: null == userCount
            ? _self.userCount
            : userCount // ignore: cast_nullable_to_non_nullable
                  as int,
        creatorName: null == creatorName
            ? _self.creatorName
            : creatorName // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [Group].
extension GroupPatterns on Group {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Group value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Group() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Group value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Group():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Group value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Group() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'uniqId') String id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'description') String description,
      @JsonKey(name: 'logo') String logo,
      @JsonKey(name: 'userCount') int userCount,
      @JsonKey(name: 'creator') String creatorName,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Group() when $default != null:
        return $default(
          _that.id,
          _that.name,
          _that.description,
          _that.logo,
          _that.userCount,
          _that.creatorName,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'uniqId') String id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'description') String description,
      @JsonKey(name: 'logo') String logo,
      @JsonKey(name: 'userCount') int userCount,
      @JsonKey(name: 'creator') String creatorName,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Group():
        return $default(
          _that.id,
          _that.name,
          _that.description,
          _that.logo,
          _that.userCount,
          _that.creatorName,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'uniqId') String id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'description') String description,
      @JsonKey(name: 'logo') String logo,
      @JsonKey(name: 'userCount') int userCount,
      @JsonKey(name: 'creator') String creatorName,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Group() when $default != null:
        return $default(
          _that.id,
          _that.name,
          _that.description,
          _that.logo,
          _that.userCount,
          _that.creatorName,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _Group implements Group {
  const _Group({
    @JsonKey(name: 'uniqId') required this.id,
    @JsonKey(name: 'name') required this.name,
    @JsonKey(name: 'description') required this.description,
    @JsonKey(name: 'logo') this.logo = '',
    @JsonKey(name: 'userCount') this.userCount = 1,
    @JsonKey(name: 'creator') required this.creatorName,
  });
  factory _Group.fromJson(Map<String, dynamic> json) => _$GroupFromJson(json);

  @override
  @JsonKey(name: 'uniqId')
  final String id;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'description')
  final String description;
  @override
  @JsonKey(name: 'logo')
  final String logo;
  @override
  @JsonKey(name: 'userCount')
  final int userCount;
  @override
  @JsonKey(name: 'creator')
  final String creatorName;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GroupCopyWith<_Group> get copyWith =>
      __$GroupCopyWithImpl<_Group>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GroupToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Group &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.userCount, userCount) ||
                other.userCount == userCount) &&
            (identical(other.creatorName, creatorName) ||
                other.creatorName == creatorName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    logo,
    userCount,
    creatorName,
  );

  @override
  String toString() {
    return 'Group(id: $id, name: $name, description: $description, logo: $logo, userCount: $userCount, creatorName: $creatorName)';
  }
}

/// @nodoc
abstract mixin class _$GroupCopyWith<$Res> implements $GroupCopyWith<$Res> {
  factory _$GroupCopyWith(_Group value, $Res Function(_Group) _then) =
      __$GroupCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'logo') String logo,
    @JsonKey(name: 'userCount') int userCount,
    @JsonKey(name: 'creator') String creatorName,
  });
}

/// @nodoc
class __$GroupCopyWithImpl<$Res> implements _$GroupCopyWith<$Res> {
  __$GroupCopyWithImpl(this._self, this._then);

  final _Group _self;
  final $Res Function(_Group) _then;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? logo = null,
    Object? userCount = null,
    Object? creatorName = null,
  }) {
    return _then(
      _Group(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        userCount: null == userCount
            ? _self.userCount
            : userCount // ignore: cast_nullable_to_non_nullable
                  as int,
        creatorName: null == creatorName
            ? _self.creatorName
            : creatorName // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$UserReferralRequest {
  @JsonKey(name: 'referralCode')
  String? get referralCode;
  @JsonKey(name: 'cardCode')
  String? get cardCode;
  @JsonKey(name: 'handle')
  String? get handle;

  /// Create a copy of UserReferralRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserReferralRequestCopyWith<UserReferralRequest> get copyWith =>
      _$UserReferralRequestCopyWithImpl<UserReferralRequest>(
        this as UserReferralRequest,
        _$identity,
      );

  /// Serializes this UserReferralRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserReferralRequest &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.handle, handle) || other.handle == handle));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, referralCode, cardCode, handle);

  @override
  String toString() {
    return 'UserReferralRequest(referralCode: $referralCode, cardCode: $cardCode, handle: $handle)';
  }
}

/// @nodoc
abstract mixin class $UserReferralRequestCopyWith<$Res> {
  factory $UserReferralRequestCopyWith(
    UserReferralRequest value,
    $Res Function(UserReferralRequest) _then,
  ) = _$UserReferralRequestCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'referralCode') String? referralCode,
    @JsonKey(name: 'cardCode') String? cardCode,
    @JsonKey(name: 'handle') String? handle,
  });
}

/// @nodoc
class _$UserReferralRequestCopyWithImpl<$Res>
    implements $UserReferralRequestCopyWith<$Res> {
  _$UserReferralRequestCopyWithImpl(this._self, this._then);

  final UserReferralRequest _self;
  final $Res Function(UserReferralRequest) _then;

  /// Create a copy of UserReferralRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referralCode = freezed,
    Object? cardCode = freezed,
    Object? handle = freezed,
  }) {
    return _then(
      _self.copyWith(
        referralCode: freezed == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        cardCode: freezed == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        handle: freezed == handle
            ? _self.handle
            : handle // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [UserReferralRequest].
extension UserReferralRequestPatterns on UserReferralRequest {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UserReferralRequest value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserReferralRequest() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UserReferralRequest value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserReferralRequest():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UserReferralRequest value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserReferralRequest() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'referralCode') String? referralCode,
      @JsonKey(name: 'cardCode') String? cardCode,
      @JsonKey(name: 'handle') String? handle,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserReferralRequest() when $default != null:
        return $default(_that.referralCode, _that.cardCode, _that.handle);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'referralCode') String? referralCode,
      @JsonKey(name: 'cardCode') String? cardCode,
      @JsonKey(name: 'handle') String? handle,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserReferralRequest():
        return $default(_that.referralCode, _that.cardCode, _that.handle);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'referralCode') String? referralCode,
      @JsonKey(name: 'cardCode') String? cardCode,
      @JsonKey(name: 'handle') String? handle,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserReferralRequest() when $default != null:
        return $default(_that.referralCode, _that.cardCode, _that.handle);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _UserReferralRequest extends UserReferralRequest {
  const _UserReferralRequest({
    @JsonKey(name: 'referralCode') this.referralCode,
    @JsonKey(name: 'cardCode') this.cardCode,
    @JsonKey(name: 'handle') this.handle,
  }) : super._();
  factory _UserReferralRequest.fromJson(Map<String, dynamic> json) =>
      _$UserReferralRequestFromJson(json);

  @override
  @JsonKey(name: 'referralCode')
  final String? referralCode;
  @override
  @JsonKey(name: 'cardCode')
  final String? cardCode;
  @override
  @JsonKey(name: 'handle')
  final String? handle;

  /// Create a copy of UserReferralRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserReferralRequestCopyWith<_UserReferralRequest> get copyWith =>
      __$UserReferralRequestCopyWithImpl<_UserReferralRequest>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$UserReferralRequestToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserReferralRequest &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.handle, handle) || other.handle == handle));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, referralCode, cardCode, handle);

  @override
  String toString() {
    return 'UserReferralRequest(referralCode: $referralCode, cardCode: $cardCode, handle: $handle)';
  }
}

/// @nodoc
abstract mixin class _$UserReferralRequestCopyWith<$Res>
    implements $UserReferralRequestCopyWith<$Res> {
  factory _$UserReferralRequestCopyWith(
    _UserReferralRequest value,
    $Res Function(_UserReferralRequest) _then,
  ) = __$UserReferralRequestCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'referralCode') String? referralCode,
    @JsonKey(name: 'cardCode') String? cardCode,
    @JsonKey(name: 'handle') String? handle,
  });
}

/// @nodoc
class __$UserReferralRequestCopyWithImpl<$Res>
    implements _$UserReferralRequestCopyWith<$Res> {
  __$UserReferralRequestCopyWithImpl(this._self, this._then);

  final _UserReferralRequest _self;
  final $Res Function(_UserReferralRequest) _then;

  /// Create a copy of UserReferralRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? referralCode = freezed,
    Object? cardCode = freezed,
    Object? handle = freezed,
  }) {
    return _then(
      _UserReferralRequest(
        referralCode: freezed == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        cardCode: freezed == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        handle: freezed == handle
            ? _self.handle
            : handle // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
mixin _$UserSettingsRequest {
  @JsonKey(name: 'fcmAndroid', includeIfNull: false)
  String? get fcmAndroid;
  @JsonKey(name: 'fcmIos', includeIfNull: false)
  String? get fcmIOS;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserSettingsRequestCopyWith<UserSettingsRequest> get copyWith =>
      _$UserSettingsRequestCopyWithImpl<UserSettingsRequest>(
        this as UserSettingsRequest,
        _$identity,
      );

  /// Serializes this UserSettingsRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserSettingsRequest &&
            (identical(other.fcmAndroid, fcmAndroid) ||
                other.fcmAndroid == fcmAndroid) &&
            (identical(other.fcmIOS, fcmIOS) || other.fcmIOS == fcmIOS));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fcmAndroid, fcmIOS);

  @override
  String toString() {
    return 'UserSettingsRequest(fcmAndroid: $fcmAndroid, fcmIOS: $fcmIOS)';
  }
}

/// @nodoc
abstract mixin class $UserSettingsRequestCopyWith<$Res> {
  factory $UserSettingsRequestCopyWith(
    UserSettingsRequest value,
    $Res Function(UserSettingsRequest) _then,
  ) = _$UserSettingsRequestCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'fcmAndroid', includeIfNull: false) String? fcmAndroid,
    @JsonKey(name: 'fcmIos', includeIfNull: false) String? fcmIOS,
  });
}

/// @nodoc
class _$UserSettingsRequestCopyWithImpl<$Res>
    implements $UserSettingsRequestCopyWith<$Res> {
  _$UserSettingsRequestCopyWithImpl(this._self, this._then);

  final UserSettingsRequest _self;
  final $Res Function(UserSettingsRequest) _then;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? fcmAndroid = freezed, Object? fcmIOS = freezed}) {
    return _then(
      _self.copyWith(
        fcmAndroid: freezed == fcmAndroid
            ? _self.fcmAndroid
            : fcmAndroid // ignore: cast_nullable_to_non_nullable
                  as String?,
        fcmIOS: freezed == fcmIOS
            ? _self.fcmIOS
            : fcmIOS // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [UserSettingsRequest].
extension UserSettingsRequestPatterns on UserSettingsRequest {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UserSettingsRequest value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserSettingsRequest() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UserSettingsRequest value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserSettingsRequest():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UserSettingsRequest value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserSettingsRequest() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'fcmAndroid', includeIfNull: false) String? fcmAndroid,
      @JsonKey(name: 'fcmIos', includeIfNull: false) String? fcmIOS,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserSettingsRequest() when $default != null:
        return $default(_that.fcmAndroid, _that.fcmIOS);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'fcmAndroid', includeIfNull: false) String? fcmAndroid,
      @JsonKey(name: 'fcmIos', includeIfNull: false) String? fcmIOS,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserSettingsRequest():
        return $default(_that.fcmAndroid, _that.fcmIOS);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'fcmAndroid', includeIfNull: false) String? fcmAndroid,
      @JsonKey(name: 'fcmIos', includeIfNull: false) String? fcmIOS,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserSettingsRequest() when $default != null:
        return $default(_that.fcmAndroid, _that.fcmIOS);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _UserSettingsRequest implements UserSettingsRequest {
  const _UserSettingsRequest({
    @JsonKey(name: 'fcmAndroid', includeIfNull: false) this.fcmAndroid,
    @JsonKey(name: 'fcmIos', includeIfNull: false) this.fcmIOS,
  });
  factory _UserSettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsRequestFromJson(json);

  @override
  @JsonKey(name: 'fcmAndroid', includeIfNull: false)
  final String? fcmAndroid;
  @override
  @JsonKey(name: 'fcmIos', includeIfNull: false)
  final String? fcmIOS;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserSettingsRequestCopyWith<_UserSettingsRequest> get copyWith =>
      __$UserSettingsRequestCopyWithImpl<_UserSettingsRequest>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$UserSettingsRequestToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserSettingsRequest &&
            (identical(other.fcmAndroid, fcmAndroid) ||
                other.fcmAndroid == fcmAndroid) &&
            (identical(other.fcmIOS, fcmIOS) || other.fcmIOS == fcmIOS));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fcmAndroid, fcmIOS);

  @override
  String toString() {
    return 'UserSettingsRequest(fcmAndroid: $fcmAndroid, fcmIOS: $fcmIOS)';
  }
}

/// @nodoc
abstract mixin class _$UserSettingsRequestCopyWith<$Res>
    implements $UserSettingsRequestCopyWith<$Res> {
  factory _$UserSettingsRequestCopyWith(
    _UserSettingsRequest value,
    $Res Function(_UserSettingsRequest) _then,
  ) = __$UserSettingsRequestCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'fcmAndroid', includeIfNull: false) String? fcmAndroid,
    @JsonKey(name: 'fcmIos', includeIfNull: false) String? fcmIOS,
  });
}

/// @nodoc
class __$UserSettingsRequestCopyWithImpl<$Res>
    implements _$UserSettingsRequestCopyWith<$Res> {
  __$UserSettingsRequestCopyWithImpl(this._self, this._then);

  final _UserSettingsRequest _self;
  final $Res Function(_UserSettingsRequest) _then;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? fcmAndroid = freezed, Object? fcmIOS = freezed}) {
    return _then(
      _UserSettingsRequest(
        fcmAndroid: freezed == fcmAndroid
            ? _self.fcmAndroid
            : fcmAndroid // ignore: cast_nullable_to_non_nullable
                  as String?,
        fcmIOS: freezed == fcmIOS
            ? _self.fcmIOS
            : fcmIOS // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
mixin _$UserRelation {
  @JsonKey(name: 'following')
  bool get following;
  @JsonKey(name: 'followedBy')
  bool get followedBy;

  /// Create a copy of UserRelation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserRelationCopyWith<UserRelation> get copyWith =>
      _$UserRelationCopyWithImpl<UserRelation>(
        this as UserRelation,
        _$identity,
      );

  /// Serializes this UserRelation to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserRelation &&
            (identical(other.following, following) ||
                other.following == following) &&
            (identical(other.followedBy, followedBy) ||
                other.followedBy == followedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, following, followedBy);

  @override
  String toString() {
    return 'UserRelation(following: $following, followedBy: $followedBy)';
  }
}

/// @nodoc
abstract mixin class $UserRelationCopyWith<$Res> {
  factory $UserRelationCopyWith(
    UserRelation value,
    $Res Function(UserRelation) _then,
  ) = _$UserRelationCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'following') bool following,
    @JsonKey(name: 'followedBy') bool followedBy,
  });
}

/// @nodoc
class _$UserRelationCopyWithImpl<$Res> implements $UserRelationCopyWith<$Res> {
  _$UserRelationCopyWithImpl(this._self, this._then);

  final UserRelation _self;
  final $Res Function(UserRelation) _then;

  /// Create a copy of UserRelation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? following = null, Object? followedBy = null}) {
    return _then(
      _self.copyWith(
        following: null == following
            ? _self.following
            : following // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedBy: null == followedBy
            ? _self.followedBy
            : followedBy // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [UserRelation].
extension UserRelationPatterns on UserRelation {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UserRelation value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserRelation() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UserRelation value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserRelation():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UserRelation value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserRelation() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'following') bool following,
      @JsonKey(name: 'followedBy') bool followedBy,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserRelation() when $default != null:
        return $default(_that.following, _that.followedBy);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'following') bool following,
      @JsonKey(name: 'followedBy') bool followedBy,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserRelation():
        return $default(_that.following, _that.followedBy);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'following') bool following,
      @JsonKey(name: 'followedBy') bool followedBy,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserRelation() when $default != null:
        return $default(_that.following, _that.followedBy);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _UserRelation implements UserRelation {
  const _UserRelation({
    @JsonKey(name: 'following') this.following = false,
    @JsonKey(name: 'followedBy') this.followedBy = false,
  });
  factory _UserRelation.fromJson(Map<String, dynamic> json) =>
      _$UserRelationFromJson(json);

  @override
  @JsonKey(name: 'following')
  final bool following;
  @override
  @JsonKey(name: 'followedBy')
  final bool followedBy;

  /// Create a copy of UserRelation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserRelationCopyWith<_UserRelation> get copyWith =>
      __$UserRelationCopyWithImpl<_UserRelation>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserRelationToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserRelation &&
            (identical(other.following, following) ||
                other.following == following) &&
            (identical(other.followedBy, followedBy) ||
                other.followedBy == followedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, following, followedBy);

  @override
  String toString() {
    return 'UserRelation(following: $following, followedBy: $followedBy)';
  }
}

/// @nodoc
abstract mixin class _$UserRelationCopyWith<$Res>
    implements $UserRelationCopyWith<$Res> {
  factory _$UserRelationCopyWith(
    _UserRelation value,
    $Res Function(_UserRelation) _then,
  ) = __$UserRelationCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'following') bool following,
    @JsonKey(name: 'followedBy') bool followedBy,
  });
}

/// @nodoc
class __$UserRelationCopyWithImpl<$Res>
    implements _$UserRelationCopyWith<$Res> {
  __$UserRelationCopyWithImpl(this._self, this._then);

  final _UserRelation _self;
  final $Res Function(_UserRelation) _then;

  /// Create a copy of UserRelation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? following = null, Object? followedBy = null}) {
    return _then(
      _UserRelation(
        following: null == following
            ? _self.following
            : following // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedBy: null == followedBy
            ? _self.followedBy
            : followedBy // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
mixin _$UserFromRelation {
  @JsonKey(name: 'referralCode')
  String get referralCode;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'title')
  String get title;
  @JsonKey(name: 'company')
  String get company;
  @JsonKey(name: 'avatar')
  String get avatar;
  @JsonKey(name: 'dynamicAvatar')
  String get dynamicAvatar;
  @JsonKey(name: 'following')
  bool get following;
  @JsonKey(name: 'followedBy')
  bool get followedBy;

  /// Create a copy of UserFromRelation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserFromRelationCopyWith<UserFromRelation> get copyWith =>
      _$UserFromRelationCopyWithImpl<UserFromRelation>(
        this as UserFromRelation,
        _$identity,
      );

  /// Serializes this UserFromRelation to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserFromRelation &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.dynamicAvatar, dynamicAvatar) ||
                other.dynamicAvatar == dynamicAvatar) &&
            (identical(other.following, following) ||
                other.following == following) &&
            (identical(other.followedBy, followedBy) ||
                other.followedBy == followedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    referralCode,
    name,
    title,
    company,
    avatar,
    dynamicAvatar,
    following,
    followedBy,
  );

  @override
  String toString() {
    return 'UserFromRelation(referralCode: $referralCode, name: $name, title: $title, company: $company, avatar: $avatar, dynamicAvatar: $dynamicAvatar, following: $following, followedBy: $followedBy)';
  }
}

/// @nodoc
abstract mixin class $UserFromRelationCopyWith<$Res> {
  factory $UserFromRelationCopyWith(
    UserFromRelation value,
    $Res Function(UserFromRelation) _then,
  ) = _$UserFromRelationCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'dynamicAvatar') String dynamicAvatar,
    @JsonKey(name: 'following') bool following,
    @JsonKey(name: 'followedBy') bool followedBy,
  });
}

/// @nodoc
class _$UserFromRelationCopyWithImpl<$Res>
    implements $UserFromRelationCopyWith<$Res> {
  _$UserFromRelationCopyWithImpl(this._self, this._then);

  final UserFromRelation _self;
  final $Res Function(UserFromRelation) _then;

  /// Create a copy of UserFromRelation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referralCode = null,
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? avatar = null,
    Object? dynamicAvatar = null,
    Object? following = null,
    Object? followedBy = null,
  }) {
    return _then(
      _self.copyWith(
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        dynamicAvatar: null == dynamicAvatar
            ? _self.dynamicAvatar
            : dynamicAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        following: null == following
            ? _self.following
            : following // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedBy: null == followedBy
            ? _self.followedBy
            : followedBy // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [UserFromRelation].
extension UserFromRelationPatterns on UserFromRelation {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UserFromRelation value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserFromRelation() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UserFromRelation value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserFromRelation():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UserFromRelation value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserFromRelation() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'referralCode') String referralCode,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'title') String title,
      @JsonKey(name: 'company') String company,
      @JsonKey(name: 'avatar') String avatar,
      @JsonKey(name: 'dynamicAvatar') String dynamicAvatar,
      @JsonKey(name: 'following') bool following,
      @JsonKey(name: 'followedBy') bool followedBy,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserFromRelation() when $default != null:
        return $default(
          _that.referralCode,
          _that.name,
          _that.title,
          _that.company,
          _that.avatar,
          _that.dynamicAvatar,
          _that.following,
          _that.followedBy,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'referralCode') String referralCode,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'title') String title,
      @JsonKey(name: 'company') String company,
      @JsonKey(name: 'avatar') String avatar,
      @JsonKey(name: 'dynamicAvatar') String dynamicAvatar,
      @JsonKey(name: 'following') bool following,
      @JsonKey(name: 'followedBy') bool followedBy,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserFromRelation():
        return $default(
          _that.referralCode,
          _that.name,
          _that.title,
          _that.company,
          _that.avatar,
          _that.dynamicAvatar,
          _that.following,
          _that.followedBy,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'referralCode') String referralCode,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'title') String title,
      @JsonKey(name: 'company') String company,
      @JsonKey(name: 'avatar') String avatar,
      @JsonKey(name: 'dynamicAvatar') String dynamicAvatar,
      @JsonKey(name: 'following') bool following,
      @JsonKey(name: 'followedBy') bool followedBy,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserFromRelation() when $default != null:
        return $default(
          _that.referralCode,
          _that.name,
          _that.title,
          _that.company,
          _that.avatar,
          _that.dynamicAvatar,
          _that.following,
          _that.followedBy,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _UserFromRelation implements UserFromRelation, UserWithAvatar {
  const _UserFromRelation({
    @JsonKey(name: 'referralCode') required this.referralCode,
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'title') this.title = '',
    @JsonKey(name: 'company') this.company = '',
    @JsonKey(name: 'avatar') this.avatar = '',
    @JsonKey(name: 'dynamicAvatar') this.dynamicAvatar = '',
    @JsonKey(name: 'following') this.following = false,
    @JsonKey(name: 'followedBy') this.followedBy = false,
  });
  factory _UserFromRelation.fromJson(Map<String, dynamic> json) =>
      _$UserFromRelationFromJson(json);

  @override
  @JsonKey(name: 'referralCode')
  final String referralCode;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'title')
  final String title;
  @override
  @JsonKey(name: 'company')
  final String company;
  @override
  @JsonKey(name: 'avatar')
  final String avatar;
  @override
  @JsonKey(name: 'dynamicAvatar')
  final String dynamicAvatar;
  @override
  @JsonKey(name: 'following')
  final bool following;
  @override
  @JsonKey(name: 'followedBy')
  final bool followedBy;

  /// Create a copy of UserFromRelation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserFromRelationCopyWith<_UserFromRelation> get copyWith =>
      __$UserFromRelationCopyWithImpl<_UserFromRelation>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserFromRelationToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserFromRelation &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.dynamicAvatar, dynamicAvatar) ||
                other.dynamicAvatar == dynamicAvatar) &&
            (identical(other.following, following) ||
                other.following == following) &&
            (identical(other.followedBy, followedBy) ||
                other.followedBy == followedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    referralCode,
    name,
    title,
    company,
    avatar,
    dynamicAvatar,
    following,
    followedBy,
  );

  @override
  String toString() {
    return 'UserFromRelation(referralCode: $referralCode, name: $name, title: $title, company: $company, avatar: $avatar, dynamicAvatar: $dynamicAvatar, following: $following, followedBy: $followedBy)';
  }
}

/// @nodoc
abstract mixin class _$UserFromRelationCopyWith<$Res>
    implements $UserFromRelationCopyWith<$Res> {
  factory _$UserFromRelationCopyWith(
    _UserFromRelation value,
    $Res Function(_UserFromRelation) _then,
  ) = __$UserFromRelationCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'dynamicAvatar') String dynamicAvatar,
    @JsonKey(name: 'following') bool following,
    @JsonKey(name: 'followedBy') bool followedBy,
  });
}

/// @nodoc
class __$UserFromRelationCopyWithImpl<$Res>
    implements _$UserFromRelationCopyWith<$Res> {
  __$UserFromRelationCopyWithImpl(this._self, this._then);

  final _UserFromRelation _self;
  final $Res Function(_UserFromRelation) _then;

  /// Create a copy of UserFromRelation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? referralCode = null,
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? avatar = null,
    Object? dynamicAvatar = null,
    Object? following = null,
    Object? followedBy = null,
  }) {
    return _then(
      _UserFromRelation(
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        dynamicAvatar: null == dynamicAvatar
            ? _self.dynamicAvatar
            : dynamicAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        following: null == following
            ? _self.following
            : following // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedBy: null == followedBy
            ? _self.followedBy
            : followedBy // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}
