// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'card.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Social _$SocialFromJson(Map json) => _Social(
  id: (json['id'] as num).toInt(),
  platformName: json['platformName'] as String? ?? '',
  platformUrl: json['platformUrl'] as String? ?? '',
  handleName: json['handleName'] as String? ?? '',
  imageUrl: json['qrcode'] as String? ?? '',
  verified: json['isVerify'] as bool? ?? false,
);

Map<String, dynamic> _$SocialToJson(_Social instance) => <String, dynamic>{
  'id': instance.id,
  'platformName': instance.platformName,
  'platformUrl': instance.platformUrl,
  'handleName': instance.handleName,
  'qrcode': instance.imageUrl,
  'isVerify': instance.verified,
};

CardInfoBasicActivated _$CardInfoBasicActivatedFromJson(Map json) =>
    CardInfoBasicActivated(
      activated: json['activated'] as bool,
      redirectUrl: json['redirectUrl'] as String? ?? '',
      referralCode: json['referralCode'] as String,
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$CardInfoBasicActivatedToJson(
  CardInfoBasicActivated instance,
) => <String, dynamic>{
  'activated': instance.activated,
  'redirectUrl': instance.redirectUrl,
  'referralCode': instance.referralCode,
  'runtimeType': instance.$type,
};

CardInfoBasicInactivated _$CardInfoBasicInactivatedFromJson(Map json) =>
    CardInfoBasicInactivated(
      id: (json['id'] as num).toInt(),
      activated: json['activated'] as bool,
      cardType: $enumDecode(_$CardTypeEnumMap, json['cardType']),
      backCover: json['backCover'] as String? ?? '',
      redirectUrl: json['redirectUrl'] as String? ?? '',
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$CardInfoBasicInactivatedToJson(
  CardInfoBasicInactivated instance,
) => <String, dynamic>{
  'id': instance.id,
  'activated': instance.activated,
  'cardType': _$CardTypeEnumMap[instance.cardType]!,
  'backCover': instance.backCover,
  'redirectUrl': instance.redirectUrl,
  'runtimeType': instance.$type,
};

const _$CardTypeEnumMap = {
  CardType.STICKER: 'STICKER',
  CardType.CARD: 'CARD',
  CardType.WRISTBAND: 'WRISTBAND',
};

_CardInfo _$CardInfoFromJson(Map json) => _CardInfo(
  referralCode: json['referralCode'] as String? ?? '',
  redirectUrl: json['redirectUrl'] as String? ?? '',
  activated: json['active'] as bool,
  activeTime: json['activeTime'] as String? ?? '',
  backCover: json['backCover'] as String? ?? '',
  cardCode: json['cardCode'] as String? ?? '',
  cardType:
      $enumDecodeNullable(_$CardTypeEnumMap, json['cardType']) ?? CardType.CARD,
  chainId: (json['chainId'] as num?)?.toInt() ?? 0,
  eventId: (json['card3EventId'] as num?)?.toInt() ?? 0,
  eventName: json['eventName'] as String? ?? '',
  id: (json['id'] as num?)?.toInt() ?? 0,
  isActive: json['isActive'] as bool? ?? false,
  virtualCard: json['virtualCard'] as bool? ?? false,
  nfcType:
      $enumDecodeNullable(_$NfcTypeEnumMap, json['nfcType']) ?? NfcType.NFC215,
);

Map<String, dynamic> _$CardInfoToJson(_CardInfo instance) => <String, dynamic>{
  'referralCode': instance.referralCode,
  'redirectUrl': instance.redirectUrl,
  'active': instance.activated,
  'activeTime': instance.activeTime,
  'backCover': instance.backCover,
  'cardCode': instance.cardCode,
  'cardType': _$CardTypeEnumMap[instance.cardType]!,
  'chainId': instance.chainId,
  'card3EventId': instance.eventId,
  'eventName': instance.eventName,
  'id': instance.id,
  'isActive': instance.isActive,
  'virtualCard': instance.virtualCard,
  'nfcType': _$NfcTypeEnumMap[instance.nfcType]!,
};

const _$NfcTypeEnumMap = {NfcType.NFC215: 'NFC215', NfcType.NFC424: 'NFC424'};

_CoverInfo _$CoverInfoFromJson(Map json) => _CoverInfo(
  activeMode: json['activeMode'] as String? ?? '',
  backCover: json['backCover'] as String? ?? '',
  eventId: json['eventId'] as String? ?? '',
  eventName: json['eventName'] as String? ?? '',
  paymentLink: json['paymentLink'] as String? ?? '',
  price: (json['price'] as num?)?.toInt() ?? 0,
  priceDescription: json['priceDescription'] as String? ?? '',
  printType:
      $enumDecodeNullable(_$PrintTypeEnumMap, json['printType']) ??
      PrintType.NORMAL,
  thirdPartyLink: json['thirdPartyLink'] as String? ?? '',
);

Map<String, dynamic> _$CoverInfoToJson(_CoverInfo instance) =>
    <String, dynamic>{
      'activeMode': instance.activeMode,
      'backCover': instance.backCover,
      'eventId': instance.eventId,
      'eventName': instance.eventName,
      'paymentLink': instance.paymentLink,
      'price': instance.price,
      'priceDescription': instance.priceDescription,
      'printType': _$PrintTypeEnumMap[instance.printType]!,
      'thirdPartyLink': instance.thirdPartyLink,
    };

const _$PrintTypeEnumMap = {
  PrintType.METAL: 'METAL',
  PrintType.NORMAL: 'NORMAL',
};

_CustomizeCardOrder _$CustomizeCardOrderFromJson(Map json) =>
    _CustomizeCardOrder(
      name: json['username'] as String,
      title: json['title'] as String? ?? '',
      company: json['company'] as String? ?? '',
      image: json['image'] as String? ?? '',
      code: json['code'] as String,
      preview: json['preview'] as String? ?? '',
      backCover: json['backCover'] as String? ?? '',
      eventName: json['eventName'] as String? ?? '',
      printed: json['isPrinted'] as bool? ?? false,
      printType: $enumDecode(_$PrintTypeEnumMap, json['printType']),
      trackingNumber: json['trackingNumber'] as String? ?? '',
      trackingCompany: json['logisticsCompany'] as String? ?? '',
      payPrice: (json['price'] as num?)?.toInt() ?? 0,
      payStatus:
          $enumDecodeNullable(_$CardPayStatusEnumMap, json['payStatus']) ??
          CardPayStatus.FREE,
      paymentLink: json['paymentLink'] as String? ?? '',
      payInfo: CustomizeCardPayInfo.fromJson(
        Map<String, dynamic>.from(
          CustomizeCardPayInfo.readValue(json, 'thirdPartyLink') as Map,
        ),
      ),
    );

Map<String, dynamic> _$CustomizeCardOrderToJson(_CustomizeCardOrder instance) =>
    <String, dynamic>{
      'username': instance.name,
      'title': instance.title,
      'company': instance.company,
      'image': instance.image,
      'code': instance.code,
      'preview': instance.preview,
      'backCover': instance.backCover,
      'eventName': instance.eventName,
      'isPrinted': instance.printed,
      'printType': _$PrintTypeEnumMap[instance.printType]!,
      'trackingNumber': instance.trackingNumber,
      'logisticsCompany': instance.trackingCompany,
      'price': instance.payPrice,
      'payStatus': _$CardPayStatusEnumMap[instance.payStatus]!,
      'paymentLink': instance.paymentLink,
      'thirdPartyLink': instance.payInfo.toJson(),
    };

const _$CardPayStatusEnumMap = {
  CardPayStatus.NOT_PAYED: 'NOT_PAYED',
  CardPayStatus.PAYED: 'PAYED',
  CardPayStatus.FREE: 'FREE',
};

_CustomizeCardPayInfo _$CustomizeCardPayInfoFromJson(Map json) =>
    _CustomizeCardPayInfo(
      link: json['link'] as String? ?? '',
      priceUnit: const CustomizeCardPriceUnitConverter().fromJson(
        json['priceUnit'] as String?,
      ),
      shippingUnit: const CustomizeCardPriceUnitConverter().fromJson(
        json['shippingUnit'] as String?,
      ),
    );

Map<String, dynamic> _$CustomizeCardPayInfoToJson(
  _CustomizeCardPayInfo instance,
) => <String, dynamic>{
  'link': instance.link,
  'priceUnit': const CustomizeCardPriceUnitConverter().toJson(
    instance.priceUnit,
  ),
  'shippingUnit': const CustomizeCardPriceUnitConverter().toJson(
    instance.shippingUnit,
  ),
};

_CustomizeCardPriceUnit _$CustomizeCardPriceUnitFromJson(Map json) =>
    _CustomizeCardPriceUnit(
      symbol: json['symbol'] as String? ?? '',
      amount: json['amount'] as String? ?? '',
      currency: json['currency'] as String? ?? '',
    );

Map<String, dynamic> _$CustomizeCardPriceUnitToJson(
  _CustomizeCardPriceUnit instance,
) => <String, dynamic>{
  'symbol': instance.symbol,
  'amount': instance.amount,
  'currency': instance.currency,
};

_ExtendProfile _$ExtendProfileFromJson(Map json) => _ExtendProfile(
  githubHandle: json['githubHandle'] as String? ?? '',
  topics: json['topics'] == null
      ? const []
      : ExtendProfile.fromJoinedString(json['topics'] as String),
  roles: json['role'] == null
      ? const []
      : ExtendProfile.fromJoinedString(json['role'] as String),
);

Map<String, dynamic> _$ExtendProfileToJson(_ExtendProfile instance) =>
    <String, dynamic>{
      'githubHandle': instance.githubHandle,
      'topics': ExtendProfile.toJoinedString(instance.topics),
      'role': ExtendProfile.toJoinedString(instance.roles),
    };

_QRCodeDynamicResult _$QRCodeDynamicResultFromJson(Map json) =>
    _QRCodeDynamicResult(
      url: json['url'] as String,
      expireTime: QRCodeDynamicResult._expireFromJson(
        (json['expireTime'] as num).toInt(),
      ),
      validSeconds: (json['qrcodeValidity'] as num).toInt(),
    );

Map<String, dynamic> _$QRCodeDynamicResultToJson(
  _QRCodeDynamicResult instance,
) => <String, dynamic>{
  'url': instance.url,
  'expireTime': QRCodeDynamicResult._expireToJson(instance.expireTime),
  'qrcodeValidity': instance.validSeconds,
};
