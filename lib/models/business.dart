import 'dart:convert' show jsonEncode;

import 'package:flutter/painting.dart' show BoxFit;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart';

import '_shared.dart' show JsonStringConverter, jsonTryDecode;
import 'user.dart' show UserWithAvatar;

part 'business.freezed.dart';

part 'business.g.dart';

@freezed
sealed class AppConfig with _$AppConfig {
  const factory AppConfig({
    @JsonKey(name: 'key') required String key,
    @JsonKey(name: 'data') required Map<String, dynamic> data,
    @JsonKey(name: 'availableWhenAuditing') @Default(false) bool availableWhenAuditing,
  }) = _AppConfig;

  factory AppConfig.fromJson(Map<String, dynamic> json) => _$AppConfigFromJson(json);
}

// 区块浏览器信息
@freezed
sealed class BlockExplorer with _$BlockExplorer {
  const factory BlockExplorer({
    @JsonKey(name: 'explorerName') required String explorerName,
    @JsonKey(name: 'url') required String url,
  }) = _BlockExplorer;

  factory BlockExplorer.fromJson(Map<String, dynamic> json) => _$BlockExplorerFromJson(json);
}

// ENS信息
@freezed
sealed class Ens with _$Ens {
  const factory Ens({
    @JsonKey(name: 'address') required String address,
    @JsonKey(name: 'blockCreated') required int blockCreated,
  }) = _Ens;

  factory Ens.fromJson(Map<String, dynamic> json) => _$EnsFromJson(json);
}

// 原生货币信息
@freezed
sealed class NativeCurrency with _$NativeCurrency {
  const factory NativeCurrency({
    @JsonKey(name: 'decimals') required int decimals,
    @JsonKey(name: 'name') required String name,
    @JsonKey(name: 'symbol') required String symbol,
  }) = _NativeCurrency;

  factory NativeCurrency.fromJson(Map<String, dynamic> json) => _$NativeCurrencyFromJson(json);
}

// RPC提供商信息
@freezed
sealed class RPCProvider with _$RPCProvider {
  const factory RPCProvider({
    @JsonKey(name: 'providerName') @Default('') String providerName,
    @JsonKey(name: 'url') required String url,
    @JsonKey(name: 'websocket') String? websocket,
  }) = _RPCProvider;

  factory RPCProvider.fromJson(Map<String, dynamic> json) => _$RPCProviderFromJson(json);
}

// 网络信息主模型
@freezed
sealed class Network with _$Network {
  const factory Network({
    @JsonKey(name: 'blockExplorers') @Default([]) List<BlockExplorer> blockExplorers,
    @JsonKey(name: 'ens') @Default(Ens(address: '', blockCreated: 0)) Ens ens,
    @JsonKey(name: 'iconUrl') @Default('') String iconUrl,
    @JsonKey(name: 'id') @Default(0) int chainIdEvm,
    @JsonKey(name: 'isEIP1559') @Default(false) bool isEIP1559,
    @JsonKey(name: 'name') @Default('') String name,
    @JsonKey(name: 'nativeCurrency')
    @Default(NativeCurrency(decimals: 0, name: '', symbol: ''))
    NativeCurrency nativeCurrency,
    @JsonKey(name: 'network') @Default('') String network,
    @JsonKey(name: 'networkType') @Default('') String networkType,
    @JsonKey(name: 'nftEnable') @Default(false) bool nftEnable,
    @JsonKey(name: 'rpcProviders') @Default([]) List<RPCProvider> rpcProviders,
    @JsonKey(name: 'shortName') @Default('') String shortName,
    @JsonKey(name: 'weight') @Default(0) int weight,
    @JsonKey(name: 'testnet') @Default(false) bool testnet,
    @JsonKey(name: 'chainIndexOkx') @Default('') String chainIndexOKX,
  }) = _Network;

  const Network._();

  factory Network.fromJson(Map<String, dynamic> json) => _$NetworkFromJson(json);

  bool get isEthereumNetwork => networkType == 'evm';

  bool get isSolanaNetwork => networkType == 'solana';
}

abstract class IWalletSummary {
  Decimal get totalValueUsd;

  bool get hasEmptyValue;
}

abstract class IToken {
  String get chainName;

  String get chainIdOKX;

  String get symbol;

  String get name;

  String get logo;

  String get address;

  int? get decimals;

  Decimal get realBalance;

  Decimal? get priceUsd;

  Decimal? get valueUsd;

  Map<String, dynamic> toJson();
}

abstract class WalletPortfolio<T extends IToken> {
  const WalletPortfolio({
    required this.summary,
    required this.tokens,
  });

  final IWalletSummary summary;
  final List<T> tokens;

  T? tokenByAddress(String address);
}

@freezed
sealed class TokenAmount with _$TokenAmount {
  const factory TokenAmount({
    required Decimal amount,
    required int decimals,
  }) = _TokenAmount;

  const TokenAmount._();

  bool get isZero => amount == Decimal.zero;

  bool get hasValue => !isZero;

  Decimal get realBalance => amount.shift(-decimals);
}

@freezed
sealed class TokenPrices with _$TokenPrices {
  const factory TokenPrices({
    @JsonKey(name: 'price') @Default(0) double price,
  }) = _TokenPrices;

  factory TokenPrices.fromJson(Map<String, Object?> json) => _$TokenPricesFromJson(json);
}

@JsonEnum(valueField: 'value')
enum MessageType {
  others(''),
  text('TEXT'),
  push('PUSH'),
  sayHi('SAYHI'),
  follow('FOLLOW'),
  airdrop('AIRDROP'),
  integral('INTEGRAL'),
  aiTaskImage('AI_TASK_IMAGE'),
  cosyAiTaskImage('COSY_AI_TASK_IMAGE');

  const MessageType(this.value);

  final String value;
}

final class _MessageTypeConverter implements JsonConverter<MessageType, String> {
  const _MessageTypeConverter();

  @override
  MessageType fromJson(String json) {
    return MessageType.values.firstWhere(
      (e) => e.value == json,
      orElse: () => MessageType.others,
    );
  }

  @override
  String toJson(MessageType object) {
    return object.value;
  }
}

final class _MessageParamsConverter implements JsonConverter<Map<String, dynamic>, String?> {
  const _MessageParamsConverter();

  @override
  Map<String, dynamic> fromJson(String? json) {
    return jsonTryDecode(json);
  }

  @override
  String? toJson(Map<String, dynamic> object) {
    return jsonEncode(object);
  }
}

@freezed
sealed class Message with _$Message {
  const factory Message({
    @JsonKey(name: 'createTime') required DateTime createTime,
    @JsonKey(name: 'id') required int id,
    @JsonKey(name: 'icon') MessageImage? icon,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'message') @Default('') String message,
    @JsonKey(name: 'images') @Default([]) List<MessageImage> images,
    @JsonKey(name: 'params') @_MessageParamsConverter() required Map<String, dynamic> params,
    @JsonKey(name: 'appParams') @_MessageParamsConverter() required Map<String, dynamic> appParams,
    @JsonKey(name: 'messageType') @_MessageTypeConverter() required MessageType messageType,
  }) = _Message;

  const Message._();

  factory Message.fromJson(Map<String, dynamic> json) => _$MessageFromJson(json);

  String? get effectiveTitle => title.orNull(
    switch (messageType) {
      MessageType.sayHi => 'New Connection',
      MessageType.follow => MessageSayHiParams.fromJson(params).name.or('New Connection'),
      MessageType.airdrop || MessageType.integral => 'Task Completed',
      MessageType.aiTaskImage => 'Avatar Generated',
      _ => null,
    },
  );
}

@freezed
sealed class MessageImage with _$MessageImage {
  const factory MessageImage({
    @JsonKey(name: 'url') required String url,
    @JsonKey(name: 'width') double? width,
    @JsonKey(name: 'height') double? height,
    @JsonKey(name: 'fit') BoxFit? fit,
  }) = _MessageImage;

  factory MessageImage.fromJson(Map<String, dynamic> json) => _$MessageImageFromJson(json);
}

@freezed
sealed class MessageSayHiParams with _$MessageSayHiParams implements UserWithAvatar {
  @Implements<UserWithAvatar>()
  const factory MessageSayHiParams({
    @JsonKey(name: 'name') @Default('') String name,
    @JsonKey(name: 'email') @Default('') String email,
    @JsonKey(name: 'avatar') @Default('') String avatar,
    @JsonKey(name: 'dynamicAvatar') @Default('') String dynamicAvatar,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'company') @Default('') String company,
    @JsonKey(name: 'referralCode') @Default('') String referralCode,
  }) = _MessageSayHiParams;

  factory MessageSayHiParams.fromJson(Map<String, dynamic> json) => _$MessageSayHiParamsFromJson(json);
}

@freezed
sealed class IntegralPoint with _$IntegralPoint {
  const factory IntegralPoint({
    @JsonKey(name: 'createTime') required String createTime,
    @JsonKey(name: 'action') @Default('') String action,
    @JsonKey(name: 'description') @Default('') String description,
    @JsonKey(name: 'integral') @Default(0) int integral,
  }) = _IntegralPoint;

  factory IntegralPoint.fromJson(Map<String, dynamic> json) => _$IntegralPointFromJson(json);
}

@freezed
sealed class ReferralLog with _$ReferralLog {
  const factory ReferralLog({
    @JsonKey(name: 'createTime') @Default('') String createTime,
    @JsonKey(name: 'integral') @Default(0) int integral,
    @JsonKey(name: 'inviteeEmail') @Default('') String inviteeEmail,
    @JsonKey(name: 'pendingReason') @Default('') String pendingReason,
  }) = _ReferralLog;

  factory ReferralLog.fromJson(Map<String, dynamic> json) => _$ReferralLogFromJson(json);
}

enum EventItemDisplayEnv {
  production,
  auditing,
}

@JsonEnum(valueField: 'value')
enum EventItemType {
  unknown(0),
  regular(1),
  banner(2);

  const EventItemType(this.value);

  final int value;
}

final class EventItemTypeConverter implements JsonConverter<EventItemType, Object?> {
  const EventItemTypeConverter();

  @override
  EventItemType fromJson(Object? json) {
    json ??= EventItemType.regular.value;
    if (json is String) {
      json = int.tryParse(json);
    }
    return EventItemType.values.firstWhere((e) => e.value == json, orElse: () => EventItemType.unknown);
  }

  @override
  Object? toJson(EventItemType object) {
    return object.value;
  }
}

@freezed
sealed class EventItem with _$EventItem {
  const factory EventItem({
    @JsonKey(name: 'title') required String title,
    @JsonKey(name: 'desc') required String desc,
    @JsonKey(name: 'image') required String image,
    @JsonKey(name: 'link') required String link,
    @JsonKey(name: 'sort') @Default(0) int sort,
    @JsonKey(name: 'eventIds') @Default([]) List<int> eventIds,
    @JsonKey(name: 'category') @Default('') String category,
    @JsonKey(name: 'isNative') @Default(false) bool isNativeLink,
    @JsonKey(name: 'highlighted') @Default(false) bool highlighted,
    @JsonKey(
      name: 'displayEnvs',
      fromJson: EventItem._displayEnvsFromJson,
      toJson: EventItem._displayEnvsToJson,
    )
    @Default([])
    List<EventItemDisplayEnv> displayEnvs,
    @JsonKey(name: 'type') @EventItemTypeConverter() required EventItemType type,
    @JsonKey(name: 'extra') @JsonStringConverter() required Map<String, dynamic> extra,
  }) = _EventItem;

  const EventItem._();

  factory EventItem.fromJson(Map<String, dynamic> json) => _$EventItemFromJson(json);

  static List<EventItemDisplayEnv> _displayEnvsFromJson(String? value) {
    if (value == null || value.isEmpty) {
      return [];
    }
    return value.split(',').map((v) => EventItemDisplayEnv.values.firstWhere((e) => e.name == v)).toList();
  }

  static String _displayEnvsToJson(List<EventItemDisplayEnv> value) {
    return value.map((e) => e.name).join(',');
  }

  String? get buttonText => extra['buttonText'] as String?;
}

@Freezed(fromJson: false, toJson: false)
@freezed
sealed class ImagePickResult with _$ImagePickResult {
  const factory ImagePickResult.file(String path) = ImagePickFileResult;

  const factory ImagePickResult.url(String url) = ImagePickUrlResult;
}

@freezed
sealed class ImageAIStyle with _$ImageAIStyle {
  const factory ImageAIStyle({
    @JsonKey(name: 'id') required int id,
    @JsonKey(name: 'name') required String name,
    @JsonKey(name: 'logo') required String logo,
  }) = _ImageAIStyle;

  factory ImageAIStyle.fromJson(Map<String, dynamic> json) => _$ImageAIStyleFromJson(json);
}

@Freezed(
  fromJson: true,
  toJson: true,
  unionKey: 'status',
  unionValueCase: FreezedUnionCase.snake,
)
sealed class ImageAITask with _$ImageAITask {
  const factory ImageAITask.notFound() = ImageAITaskNotFound;

  const factory ImageAITask.inQueue({
    @JsonKey(name: 'taskId') required String taskId,
    @JsonKey(name: 'queueNumber') required int queueNumber,
    @JsonKey(name: 'queueSeconds') int? queueEstimateSeconds,
  }) = ImageAITaskInQueue;

  const factory ImageAITask.generating({
    @JsonKey(name: 'taskId') required String taskId,
    @JsonKey(name: 'queueNumber') required int queueNumber,
    @JsonKey(name: 'queueSeconds') int? queueEstimateSeconds,
  }) = ImageAITaskGenerating;

  const factory ImageAITask.done({
    @JsonKey(name: 'taskId') required String taskId,
    @JsonKey(name: 'imageUrl') @Default('') String imageUrl,
  }) = ImageAITaskDone;

  const factory ImageAITask.error({
    @JsonKey(name: 'taskId') required String taskId,
    @JsonKey(name: 'error') required String error,
  }) = ImageAITaskError;

  const ImageAITask._();

  factory ImageAITask.fromJson(Map<String, dynamic> json) => _$ImageAITaskFromJson(json);

  bool get running => this is ImageAITaskInQueue || this is ImageAITaskGenerating;

  int? get queueEstimateSeconds => whenOrNull(
    inQueue: (_, _, queueEstimateSeconds) => queueEstimateSeconds,
    generating: (_, _, queueEstimateSeconds) => queueEstimateSeconds,
  );
}
