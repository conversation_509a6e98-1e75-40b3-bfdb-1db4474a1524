// GENERATED CODE - DO NOT MODIFY MANUALLY
// **************************************************************************
// Auto generated by https://github.com/fluttercandies/ff_annotation_route
// **************************************************************************
// fast mode: true
// **************************************************************************
// ignore_for_file: duplicate_import,implementation_imports,library_private_types_in_public_api,multiple_combinators,prefer_const_literals_to_create_immutables,unintended_html_in_doc_comment,unnecessary_import,unused_import,unused_local_variable,unused_shown_name,unnecessary_library_name,unnecessary_library_directive
import 'package:ff_annotation_route_library/ff_annotation_route_library.dart';
import 'package:flutter/widgets.dart';

import '/feat/scan/uni_qr.dart';
import '/models/business.dart';
import '/models/card.dart' show CardInfo, CardType;
import '/models/card.dart' show CardPayStatus, CustomizeCardOrder, PrintType;
import '/models/card.dart' show CustomizeCardOrder;
import '/models/card.dart' show ExtendProfile, Social, SocialPlatform;
import '/models/card.dart' show Social, SocialPlatform;
import '/models/user.dart' show UserInfo;
import '/models/user.dart';
import '../ui/biz/ai/generate_image.dart';
import '../ui/biz/card/activate_result.dart';
import '../ui/biz/customize/customize.dart';
import '../ui/biz/customize/done.dart';
import '../ui/biz/customize/payment.dart';
import '../ui/biz/customize/prints.dart';
import '../ui/biz/fun/points.dart';
import '../ui/biz/fun/referal.dart';
import '../ui/biz/home.dart';
import '../ui/biz/login/index.dart';
import '../ui/biz/login/login_with_email.dart';
import '../ui/biz/other/notification.dart';
import '../ui/biz/other/share.dart';
import '../ui/biz/other/web_view.dart';
import '../ui/biz/scan/scan.dart';
import '../ui/biz/setting/about.dart';
import '../ui/biz/setting/account.dart';
import '../ui/biz/setting/cards.dart';
import '../ui/biz/setting/env.dart';
import '../ui/biz/social/edit.dart';
import '../ui/biz/social/platform.dart';
import '../ui/biz/social/profile.dart';
import '../ui/biz/splash.dart';
import '../ui/biz/wallet/authenticate.dart';
import '../ui/biz/wallet/management.dart';
import '../ui/biz/wallet/portfolio.dart';
import '../ui/biz/wallet/send.dart';
import '../ui/widgets/image_viewer.dart';

/// Get route settings base on route name, auto generated by https://github.com/fluttercandies/ff_annotation_route
FFRouteSettings getRouteSettings({
  required String name,
  Map<String, dynamic>? arguments,
  PageBuilder? notFoundPageBuilder,
}) {
  final Map<String, dynamic> safeArguments =
      arguments ?? const <String, dynamic>{};
  switch (name) {
    case '/':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SplashPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/ai/generate/image':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => AIGenerateImagePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          taskId: asT<String?>(
            safeArguments['taskId'],
          ),
          generatedUrl: asT<String?>(
            safeArguments['generatedUrl'],
          ),
          filePath: asT<String?>(
            safeArguments['filePath'],
          ),
          style: asT<ImageAIStyle?>(
            safeArguments['style'],
          ),
        ),
      );
    case '/card/activate-result':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => CardActivateResultPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          card: asT<CardInfo>(
            safeArguments['card'],
          )!,
          activated: asT<bool>(
            safeArguments['activated'],
          )!,
        ),
      );
    case '/customize':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => CustomizePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          code: asT<String?>(
            safeArguments['code'],
          ),
        ),
      );
    case '/customize/done':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => CustomizeDonePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          order: asT<CustomizeCardOrder>(
            safeArguments['order'],
          )!,
        ),
      );
    case '/customize/payment':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => CustomizePaymentPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          order: asT<CustomizeCardOrder>(
            safeArguments['order'],
          )!,
        ),
      );
    case '/customize/prints':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => CustomizeCardPrintsPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/fun/points':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => PointsPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/fun/referral':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => ReferralPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/home':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => HomePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/image/viewer':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => ImageViewer(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          imageUrl: asT<String>(
            safeArguments['imageUrl'],
          )!,
        ),
        pageRouteType: PageRouteType.transparent,
      );
    case '/login':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => Login(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/login/email':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => LoginWithEmail(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/notification':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => NotificationPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/scan':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => QRScan(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          manager: asT<ScanManager>(
            safeArguments['manager'],
          )!,
        ),
      );
    case '/setting/about':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => AboutPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/setting/account':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => AccountSecurity(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/setting/cards-collection':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => CardsCollectionPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/settings/env':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => UpdateEnvPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/share':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SharePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/social/edit-profile':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SocialEditProfilePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          pendingAvatarUrl: asT<String?>(
            safeArguments['pendingAvatarUrl'],
          ),
        ),
      );
    case '/social/platform':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SocialPlatformPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          platform: asT<SocialPlatform>(
            safeArguments['platform'],
          )!,
          social: asT<Social?>(
            safeArguments['social'],
          ),
          currentHandle: asT<String?>(
            safeArguments['currentHandle'],
          ),
          specialGitHub: asT<bool>(
            safeArguments['specialGitHub'],
            false,
          )!,
        ),
      );
    case '/social/profile':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SocialProfilePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          code: asT<String>(
            safeArguments['code'],
          )!,
          profile: asT<UserInfo?>(
            safeArguments['profile'],
          ),
          avatar: asT<UserWithAvatar?>(
            safeArguments['avatar'],
          ),
        ),
      );
    case '/wallet/authenticate':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => WalletAuthenticatePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/wallet/management':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => WalletManagementPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/wallet/portfolio':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => WalletPortfolioPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/wallet/send':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SendPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          token: asT<IToken>(
            safeArguments['token'],
          )!,
        ),
      );
    case '/webview':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => WebViewPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          url: asT<String>(
            safeArguments['url'],
          )!,
          title: asT<String?>(
            safeArguments['title'],
          ),
        ),
      );
    default:
      return FFRouteSettings(
        name: FFRoute.notFoundName,
        routeName: FFRoute.notFoundRouteName,
        builder: notFoundPageBuilder ?? () => Container(),
      );
  }
}
