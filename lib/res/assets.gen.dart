// dart format width=120

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:lottie/lottie.dart' as _lottie;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/bg_share_card.svg
  SvgGenImage get bgShareCard => const SvgGenImage('assets/icons/bg_share_card.svg');

  /// File path: assets/icons/button_copy.svg
  SvgGenImage get buttonCopy => const SvgGenImage('assets/icons/button_copy.svg');

  /// File path: assets/icons/button_edit.svg
  SvgGenImage get buttonEdit => const SvgGenImage('assets/icons/button_edit.svg');

  /// File path: assets/icons/button_icon_back.svg
  SvgGenImage get buttonIconBack => const SvgGenImage('assets/icons/button_icon_back.svg');

  /// File path: assets/icons/button_icon_camera.svg
  SvgGenImage get buttonIconCamera => const SvgGenImage('assets/icons/button_icon_camera.svg');

  /// File path: assets/icons/button_icon_generate.svg
  SvgGenImage get buttonIconGenerate => const SvgGenImage('assets/icons/button_icon_generate.svg');

  /// File path: assets/icons/button_icon_receive.svg
  SvgGenImage get buttonIconReceive => const SvgGenImage('assets/icons/button_icon_receive.svg');

  /// File path: assets/icons/button_icon_send.svg
  SvgGenImage get buttonIconSend => const SvgGenImage('assets/icons/button_icon_send.svg');

  /// File path: assets/icons/button_icon_swap.svg
  SvgGenImage get buttonIconSwap => const SvgGenImage('assets/icons/button_icon_swap.svg');

  /// File path: assets/icons/button_icon_upload.svg
  SvgGenImage get buttonIconUpload => const SvgGenImage('assets/icons/button_icon_upload.svg');

  /// File path: assets/icons/button_logout.svg
  SvgGenImage get buttonLogout => const SvgGenImage('assets/icons/button_logout.svg');

  /// File path: assets/icons/button_share.svg
  SvgGenImage get buttonShare => const SvgGenImage('assets/icons/button_share.svg');

  /// File path: assets/icons/check.svg
  SvgGenImage get check => const SvgGenImage('assets/icons/check.svg');

  /// File path: assets/icons/drag_handle.svg
  SvgGenImage get dragHandle => const SvgGenImage('assets/icons/drag_handle.svg');

  /// File path: assets/icons/home_card_nfc.svg
  SvgGenImage get homeCardNfc => const SvgGenImage('assets/icons/home_card_nfc.svg');

  /// File path: assets/icons/home_messages.svg
  SvgGenImage get homeMessages => const SvgGenImage('assets/icons/home_messages.svg');

  /// File path: assets/icons/icon_message_ai_image.svg
  SvgGenImage get iconMessageAiImage => const SvgGenImage('assets/icons/icon_message_ai_image.svg');

  /// File path: assets/icons/icon_message_default.svg
  SvgGenImage get iconMessageDefault => const SvgGenImage('assets/icons/icon_message_default.svg');

  /// Directory path: assets/icons/images
  $AssetsIconsImagesGen get images => const $AssetsIconsImagesGen();

  /// File path: assets/icons/logo_dark.svg
  SvgGenImage get logoDark => const SvgGenImage('assets/icons/logo_dark.svg');

  /// File path: assets/icons/logo_light.svg
  SvgGenImage get logoLight => const SvgGenImage('assets/icons/logo_light.svg');

  /// File path: assets/icons/logo_text_dark.svg
  SvgGenImage get logoTextDark => const SvgGenImage('assets/icons/logo_text_dark.svg');

  /// File path: assets/icons/logo_text_light.svg
  SvgGenImage get logoTextLight => const SvgGenImage('assets/icons/logo_text_light.svg');

  /// File path: assets/icons/nav_cards.svg
  SvgGenImage get navCards => const SvgGenImage('assets/icons/nav_cards.svg');

  /// File path: assets/icons/nav_connections.svg
  SvgGenImage get navConnections => const SvgGenImage('assets/icons/nav_connections.svg');

  /// File path: assets/icons/nav_explore.svg
  SvgGenImage get navExplore => const SvgGenImage('assets/icons/nav_explore.svg');

  /// File path: assets/icons/nav_settings.svg
  SvgGenImage get navSettings => const SvgGenImage('assets/icons/nav_settings.svg');

  /// File path: assets/icons/placeholder_broken.svg
  SvgGenImage get placeholderBroken => const SvgGenImage('assets/icons/placeholder_broken.svg');

  /// Directory path: assets/icons/scan
  $AssetsIconsScanGen get scan => const $AssetsIconsScanGen();

  /// Directory path: assets/icons/setting
  $AssetsIconsSettingGen get setting => const $AssetsIconsSettingGen();

  /// Directory path: assets/icons/social
  $AssetsIconsSocialGen get social => const $AssetsIconsSocialGen();

  /// File path: assets/icons/social_verified.svg
  SvgGenImage get socialVerified => const SvgGenImage('assets/icons/social_verified.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    bgShareCard,
    buttonCopy,
    buttonEdit,
    buttonIconBack,
    buttonIconCamera,
    buttonIconGenerate,
    buttonIconReceive,
    buttonIconSend,
    buttonIconSwap,
    buttonIconUpload,
    buttonLogout,
    buttonShare,
    check,
    dragHandle,
    homeCardNfc,
    homeMessages,
    iconMessageAiImage,
    iconMessageDefault,
    logoDark,
    logoLight,
    logoTextDark,
    logoTextLight,
    navCards,
    navConnections,
    navExplore,
    navSettings,
    placeholderBroken,
    socialVerified,
  ];
}

class $AssetsLottieGen {
  const $AssetsLottieGen();

  /// File path: assets/lottie/acard.json
  LottieGenImage get acard => const LottieGenImage('assets/lottie/acard.json');

  /// File path: assets/lottie/loading.json
  LottieGenImage get loading => const LottieGenImage('assets/lottie/loading.json');

  /// File path: assets/lottie/nfc.json
  LottieGenImage get nfc => const LottieGenImage('assets/lottie/nfc.json');

  /// File path: assets/lottie/placeholder_empty_dark.json
  LottieGenImage get placeholderEmptyDark => const LottieGenImage('assets/lottie/placeholder_empty_dark.json');

  /// File path: assets/lottie/placeholder_empty_light.json
  LottieGenImage get placeholderEmptyLight => const LottieGenImage('assets/lottie/placeholder_empty_light.json');

  /// File path: assets/lottie/placeholder_wallet.json
  LottieGenImage get placeholderWallet => const LottieGenImage('assets/lottie/placeholder_wallet.json');

  /// List of all assets
  List<LottieGenImage> get values => [
    acard,
    loading,
    nfc,
    placeholderEmptyDark,
    placeholderEmptyLight,
    placeholderWallet,
  ];
}

class $AssetsMediaGen {
  const $AssetsMediaGen();

  /// File path: assets/media/login.mp4
  String get login => 'assets/media/login.mp4';

  /// File path: assets/media/login_fallback.jpg
  AssetGenImage get loginFallback => const AssetGenImage('assets/media/login_fallback.jpg');

  /// List of all assets
  List<dynamic> get values => [login, loginFallback];
}

class $AssetsIconsImagesGen {
  const $AssetsIconsImagesGen();

  /// File path: assets/icons/images/activate_guide.svg
  SvgGenImage get activateGuide => const SvgGenImage('assets/icons/images/activate_guide.svg');

  /// File path: assets/icons/images/banner2.png
  AssetGenImage get banner2 => const AssetGenImage('assets/icons/images/banner2.png');

  /// File path: assets/icons/images/cover_metal_back.svg
  SvgGenImage get coverMetalBack => const SvgGenImage('assets/icons/images/cover_metal_back.svg');

  /// File path: assets/icons/images/cover_metal_front.svg
  SvgGenImage get coverMetalFront => const SvgGenImage('assets/icons/images/cover_metal_front.svg');

  /// File path: assets/icons/images/cover_normal_back.png
  AssetGenImage get coverNormalBack => const AssetGenImage('assets/icons/images/cover_normal_back.png');

  /// File path: assets/icons/images/cover_normal_front.png
  AssetGenImage get coverNormalFront => const AssetGenImage('assets/icons/images/cover_normal_front.png');

  /// File path: assets/icons/images/cover_sticker.png
  AssetGenImage get coverSticker => const AssetGenImage('assets/icons/images/cover_sticker.png');

  /// File path: assets/icons/images/cover_wristband.png
  AssetGenImage get coverWristband => const AssetGenImage('assets/icons/images/cover_wristband.png');

  /// File path: assets/icons/images/gradient.png
  AssetGenImage get gradient => const AssetGenImage('assets/icons/images/gradient.png');

  /// List of all assets
  List<dynamic> get values => [
    activateGuide,
    banner2,
    coverMetalBack,
    coverMetalFront,
    coverNormalBack,
    coverNormalFront,
    coverSticker,
    coverWristband,
    gradient,
  ];
}

class $AssetsIconsScanGen {
  const $AssetsIconsScanGen();

  /// File path: assets/icons/scan/flashlight_black.svg
  SvgGenImage get flashlightBlack => const SvgGenImage('assets/icons/scan/flashlight_black.svg');

  /// File path: assets/icons/scan/flashlight_white.svg
  SvgGenImage get flashlightWhite => const SvgGenImage('assets/icons/scan/flashlight_white.svg');

  /// File path: assets/icons/scan/gallery.svg
  SvgGenImage get gallery => const SvgGenImage('assets/icons/scan/gallery.svg');

  /// File path: assets/icons/scan/scanner.svg
  SvgGenImage get scanner => const SvgGenImage('assets/icons/scan/scanner.svg');

  /// File path: assets/icons/scan/warning_yellow.svg
  SvgGenImage get warningYellow => const SvgGenImage('assets/icons/scan/warning_yellow.svg');

  /// List of all assets
  List<SvgGenImage> get values => [flashlightBlack, flashlightWhite, gallery, scanner, warningYellow];
}

class $AssetsIconsSettingGen {
  const $AssetsIconsSettingGen();

  /// File path: assets/icons/setting/about.svg
  SvgGenImage get about => const SvgGenImage('assets/icons/setting/about.svg');

  /// File path: assets/icons/setting/bg_card_activated.svg
  SvgGenImage get bgCardActivated => const SvgGenImage('assets/icons/setting/bg_card_activated.svg');

  /// File path: assets/icons/setting/index.svg
  SvgGenImage get index => const SvgGenImage('assets/icons/setting/index.svg');

  /// File path: assets/icons/setting/my_cards.svg
  SvgGenImage get myCards => const SvgGenImage('assets/icons/setting/my_cards.svg');

  /// File path: assets/icons/setting/person_shield.svg
  SvgGenImage get personShield => const SvgGenImage('assets/icons/setting/person_shield.svg');

  /// File path: assets/icons/setting/print_cards.svg
  SvgGenImage get printCards => const SvgGenImage('assets/icons/setting/print_cards.svg');

  /// File path: assets/icons/setting/wallet.svg
  SvgGenImage get wallet => const SvgGenImage('assets/icons/setting/wallet.svg');

  /// List of all assets
  List<SvgGenImage> get values => [about, bgCardActivated, index, myCards, personShield, printCards, wallet];
}

class $AssetsIconsSocialGen {
  const $AssetsIconsSocialGen();

  /// File path: assets/icons/social/calendly.svg
  SvgGenImage get calendly => const SvgGenImage('assets/icons/social/calendly.svg');

  /// Directory path: assets/icons/social/demo
  $AssetsIconsSocialDemoGen get demo => const $AssetsIconsSocialDemoGen();

  /// File path: assets/icons/social/discord.svg
  SvgGenImage get discord => const SvgGenImage('assets/icons/social/discord.svg');

  /// File path: assets/icons/social/email.svg
  SvgGenImage get email => const SvgGenImage('assets/icons/social/email.svg');

  /// File path: assets/icons/social/farcaster.svg
  SvgGenImage get farcaster => const SvgGenImage('assets/icons/social/farcaster.svg');

  /// File path: assets/icons/social/github.svg
  SvgGenImage get github => const SvgGenImage('assets/icons/social/github.svg');

  /// File path: assets/icons/social/instagram.svg
  SvgGenImage get instagram => const SvgGenImage('assets/icons/social/instagram.svg');

  /// File path: assets/icons/social/kakao.svg
  SvgGenImage get kakao => const SvgGenImage('assets/icons/social/kakao.svg');

  /// File path: assets/icons/social/line.svg
  SvgGenImage get line => const SvgGenImage('assets/icons/social/line.svg');

  /// File path: assets/icons/social/link.svg
  SvgGenImage get link => const SvgGenImage('assets/icons/social/link.svg');

  /// File path: assets/icons/social/linkTree.svg
  SvgGenImage get linkTree => const SvgGenImage('assets/icons/social/linkTree.svg');

  /// File path: assets/icons/social/linkedIn.svg
  SvgGenImage get linkedIn => const SvgGenImage('assets/icons/social/linkedIn.svg');

  /// File path: assets/icons/social/memex.svg
  SvgGenImage get memex => const SvgGenImage('assets/icons/social/memex.svg');

  /// File path: assets/icons/social/ordme.svg
  SvgGenImage get ordme => const SvgGenImage('assets/icons/social/ordme.svg');

  /// File path: assets/icons/social/phone.svg
  SvgGenImage get phone => const SvgGenImage('assets/icons/social/phone.svg');

  /// File path: assets/icons/social/telegram.svg
  SvgGenImage get telegram => const SvgGenImage('assets/icons/social/telegram.svg');

  /// File path: assets/icons/social/tiktok.svg
  SvgGenImage get tiktok => const SvgGenImage('assets/icons/social/tiktok.svg');

  /// File path: assets/icons/social/twitter.svg
  SvgGenImage get twitter => const SvgGenImage('assets/icons/social/twitter.svg');

  /// File path: assets/icons/social/wechat.svg
  SvgGenImage get wechat => const SvgGenImage('assets/icons/social/wechat.svg');

  /// File path: assets/icons/social/whatsapp.svg
  SvgGenImage get whatsapp => const SvgGenImage('assets/icons/social/whatsapp.svg');

  /// File path: assets/icons/social/youtube.svg
  SvgGenImage get youtube => const SvgGenImage('assets/icons/social/youtube.svg');

  /// File path: assets/icons/social/zalo.svg
  SvgGenImage get zalo => const SvgGenImage('assets/icons/social/zalo.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    calendly,
    discord,
    email,
    farcaster,
    github,
    instagram,
    kakao,
    line,
    link,
    linkTree,
    linkedIn,
    memex,
    ordme,
    phone,
    telegram,
    tiktok,
    twitter,
    wechat,
    whatsapp,
    youtube,
    zalo,
  ];
}

class $AssetsIconsSocialDemoGen {
  const $AssetsIconsSocialDemoGen();

  /// File path: assets/icons/social/demo/Farcaster.png
  AssetGenImage get farcaster => const AssetGenImage('assets/icons/social/demo/Farcaster.png');

  /// File path: assets/icons/social/demo/GitHub.png
  AssetGenImage get gitHub => const AssetGenImage('assets/icons/social/demo/GitHub.png');

  /// File path: assets/icons/social/demo/Instagram.png
  AssetGenImage get instagram => const AssetGenImage('assets/icons/social/demo/Instagram.png');

  /// File path: assets/icons/social/demo/LinkedIn.png
  AssetGenImage get linkedIn => const AssetGenImage('assets/icons/social/demo/LinkedIn.png');

  /// File path: assets/icons/social/demo/Linktree.png
  AssetGenImage get linktree => const AssetGenImage('assets/icons/social/demo/Linktree.png');

  /// File path: assets/icons/social/demo/MemeX.png
  AssetGenImage get memeX => const AssetGenImage('assets/icons/social/demo/MemeX.png');

  /// File path: assets/icons/social/demo/Telegram.png
  AssetGenImage get telegram => const AssetGenImage('assets/icons/social/demo/Telegram.png');

  /// File path: assets/icons/social/demo/TikTok.png
  AssetGenImage get tikTok => const AssetGenImage('assets/icons/social/demo/TikTok.png');

  /// File path: assets/icons/social/demo/X-Twitter.png
  AssetGenImage get xTwitter => const AssetGenImage('assets/icons/social/demo/X-Twitter.png');

  /// File path: assets/icons/social/demo/YouTube.png
  AssetGenImage get youTube => const AssetGenImage('assets/icons/social/demo/YouTube.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    farcaster,
    gitHub,
    instagram,
    linkedIn,
    linktree,
    memeX,
    telegram,
    tikTok,
    xTwitter,
    youTube,
  ];
}

class Assets {
  const Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsLottieGen lottie = $AssetsLottieGen();
  static const $AssetsMediaGen media = $AssetsMediaGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}, this.animation});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({required this.isAnimation, required this.duration, required this.frames});

  final bool isAnimation;
  final Duration duration;
  final int frames;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(_assetName, assetBundle: bundle, packageName: package);
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ?? (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(this._assetName, {this.flavors = const {}});

  final String _assetName;
  final Set<String> flavors;

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(BuildContext, Widget, _lottie.LottieComposition?)? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
    _lottie.LottieDecoder? decoder,
    _lottie.RenderCache? renderCache,
    bool? backgroundLoading,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
      decoder: decoder,
      renderCache: renderCache,
      backgroundLoading: backgroundLoading,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
