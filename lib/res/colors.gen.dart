// dart format width=120
/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/painting.dart';
import 'package:flutter/material.dart';

class ColorName {
  ColorName._();

  /// Color: #0b0b0f
  static const Color backgroundColorDark = Color(0xFF0B0B0F);

  /// Color: #f1f1f1
  static const Color backgroundColorLight = Color(0xFFF1F1F1);

  /// Color: #6d7793
  static const Color blueGreyIconColorDark = Color(0xFF6D7793);

  /// Color: #b3b7c1
  static const Color blueGreyIconColorLight = Color(0xFFB3B7C1);

  /// Color: #9c9ca4
  static const Color captionTextColorDark = Color(0xFF9C9CA4);

  /// Color: #9c9ca4
  static const Color captionTextColorLight = Color(0xFF9C9CA4);

  /// Color: #39394A
  static const Color cardColorDark = Color(0xFF39394A);

  /// Color: #ffffff
  static const Color cardColorLight = Color(0xFFFFFFFF);

  /// Color: #505067
  static const Color dividerColorDark = Color(0xFF505067);

  /// Color: #e8eaf0
  static const Color dividerColorLight = Color(0xFFE8EAF0);

  /// Color: #FE4C25
  static const Color failingColor = Color(0xFFFE4C25);

  /// Color: #a2a2b4
  static const Color iconColorDark = Color(0xFFA2A2B4);

  /// Color: #c0c0c4
  static const Color iconColorLight = Color(0xFFC0C0C4);

  /// Color: #22222c
  static const Color listColorDark = Color(0xFF22222C);

  /// Color: #f2f2f2
  static const Color listColorLight = Color(0xFFF2F2F2);

  /// Color: #FFDE4F
  static const Color notificationColor = Color(0xFFFFDE4F);

  /// Color: #ffffff
  static const Color primaryTextColorDark = Color(0xFFFFFFFF);

  /// Color: #3a3a49
  static const Color primaryTextColorLight = Color(0xFF3A3A49);

  /// Color: #56566b
  static const Color shimmerHighlightColorDark = Color(0xFF56566B);

  /// Color: #dcdee4
  static const Color shimmerHighlightColorLight = Color(0xFFDCDEE4);

  /// Color: #3EDD40
  static const Color successColor = Color(0xFF3EDD40);

  /// Color: #8858FF
  static const Color themeColorDark = Color(0xFF8858FF);

  /// Color: #8858FF
  static const Color themeColorLight = Color(0xFF8858FF);
}
