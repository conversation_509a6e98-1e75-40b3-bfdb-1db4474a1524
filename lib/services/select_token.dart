import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_ui/me_ui.dart' show MEImage, RippleTap, Gap;

import '/models/business.dart';
import '/routes/card3_routes.dart' show Routes;
import '/ui/widgets/dialog/scrollable_bottom_sheet.dart';
import '/ui/widgets/toast.dart' show Card3ToastUtil;

Future<void> showTokenSelection(
  BuildContext context,
  List<IToken>? tokens,
  void Function(IToken) callback,
) async {
  if (tokens == null) {
    return;
  }

  if (tokens.isEmpty) {
    Card3ToastUtil.showToast(message: 'No available tokens');
    return;
  }

  final selectedToken = await ScrollableBottomSheet.show<IToken>(
    context: context,
    builder: (context) => ScrollableBottomSheet(
      title: 'Select Token',
      sliversBuilder: (context) => [
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          sliver: SliverList.separated(
            separatorBuilder: (_, _) => const Gap.v(10.0),
            itemCount: tokens.length,
            itemBuilder: (context, index) {
              final token = tokens[index];
              return ProviderScope(
                overrides: [_tokenItemProvider.overrideWithValue(token)],
                child: const _TokenItem(),
              );
            },
          ),
        ),
      ],
    ),
  );

  if (selectedToken is IToken) {
    callback(selectedToken);
  }
}

final _tokenItemProvider = Provider.autoDispose<IToken>(
  (ref) => throw UnimplementedError(),
);

class _TokenItem extends ConsumerWidget {
  const _TokenItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final token = ref.watch(_tokenItemProvider);
    final price = token.priceUsd;
    final valueUsd = switch (price) {
      final price? => price * token.realBalance,
      _ => null,
    };
    return RippleTap(
      onTap: () => Navigator.of(context).pushNamed(
        Routes.walletSend.name,
        arguments: Routes.walletSend.d(token: token),
      ),
      padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 24, 16),
      borderRadius: RadiusConstants.max,
      color: context.theme.colorScheme.surface,
      child: Row(
        spacing: 12.0,
        children: [
          MEImage(
            token.logo,
            width: 46.0,
            height: 46.0,
            clipOval: true,
            fit: BoxFit.cover,
            alternativeSVG: true,
            emptyBuilder: (context) => Container(
              width: 46.0,
              height: 46.0,
              decoration: BoxDecoration(
                color: context.themeColor,
                shape: BoxShape.circle,
              ),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  token.symbol.split('').elementAtOrNull(0)?.toUpperCase().or('?') ?? '?',
                  style: const TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
          Expanded(
            child: Text(
              token.symbol,
              style: const TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 代币数量及价值
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                token.realBalance.toNumerical(fractionDigits: 4),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text.rich(
                TextSpan(
                  children: [
                    const TextSpan(text: r'$'),
                    TextSpan(text: valueUsd?.toNumerical(fractionDigits: 4) ?? '---'),
                  ],
                ),
                style: context.textTheme.bodySmall?.copyWith(fontSize: 14),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
