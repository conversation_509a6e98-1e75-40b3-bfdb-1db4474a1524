import 'dart:async' show Completer;
import 'dart:convert' show base64;

import 'package:me_extensions/me_extensions.dart' show MEObjectExtension;
import 'package:me_utils/me_utils.dart' show LogUtil;
import 'package:privy_flutter/privy_flutter.dart' as privy show EmbeddedSolanaWallet, PrivyUser, Result;
import 'package:solana/dto.dart' hide Instruction;
import 'package:solana/encoder.dart' show SignedTx, CompiledMessage, Instruction;
import 'package:solana/solana.dart';

import '/constants/constants.dart' show tokenSOLAddress, tokenSOLWrappedAddress;
import '/internals/methods.dart' show handleExceptions;
import '/models/business.dart' show IToken;
import 'service.dart';

class SolanaService implements ChainService<privy.EmbeddedSolanaWallet> {
  static const _tag = '💸 SolanaService';

  SolanaClient get client => _client!;
  SolanaClient? _client;

  RpcClient get rpcClient => client.rpcClient;

  late Network _network;

  @override
  String? get walletAddress => _walletAddress;
  String? _walletAddress;

  @override
  privy.PrivyUser? get user => _user;
  privy.PrivyUser? _user;

  @override
  void initialize({
    required Network network,
    required privy.PrivyUser user,
  }) {
    final rpc = network.rpcProviders.first;

    _network = network;
    _client = SolanaClient(
      rpcUrl: Uri.parse(rpc.url),
      websocketUrl: rpc.websocket?.run(Uri.parse) ?? Uri.parse(rpc.url).replace(scheme: 'wss'),
    );
    _walletAddress = user.embeddedSolanaWallets.firstOrNull?.address;
    _user = user;

    LogUtil.d(
      'SolanaService Initialized: rpcUrl=${rpc.url}, walletAddress=$_walletAddress',
      tag: _tag,
      tagWithTrace: false,
    );
  }

  @override
  void updatePrivyUser(privy.PrivyUser user) {
    initialize(network: _network, user: user);
  }

  @override
  void dispose() {
    _client = null;
  }

  @override
  Future<BigInt> getNativeTokenBalance(String walletAddress) async {
    try {
      final pubKey = Ed25519HDPublicKey.fromBase58(walletAddress);
      final balance = await rpcClient.getBalance(pubKey.toBase58());
      return BigInt.parse(balance.value.toString());
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return BigInt.zero;
    }
  }

  @override
  Future<(BigInt balance, int decimals)> getTokenBalance(
    String tokenAddress,
    String walletAddress,
  ) async {
    LogUtil.d(
      'Getting SPL balance:'
      '\n|-- tokenAddress=$tokenAddress'
      '\n|-- walletAddress=$walletAddress',
      tag: _tag,
      tagWithTrace: false,
    );

    // 检查钱包地址是否为空
    if (walletAddress.isEmpty) {
      return (BigInt.zero, 1);
    }

    late final BigInt balance;
    late final int decimals;
    await Future.wait([
      Future<BigInt>(() async {
        // 使用 getTokenAccountsByOwner 方法获取代币账户，使用 Base64 编码
        final accounts = await rpcClient.getTokenAccountsByOwner(
          walletAddress,
          TokenAccountsFilter.byMint(tokenAddress),
          encoding: Encoding.base64,
        );

        if (accounts.value.isEmpty) {
          return BigInt.zero;
        }

        final balance = await rpcClient.getTokenAccountBalance(accounts.value.first.pubkey);
        return BigInt.parse(balance.value.amount);
      }).then((r) => balance = r),
      Future<int>(() async {
        final mint = await client.getMint(address: Ed25519HDPublicKey.fromBase58(tokenAddress));
        return mint.decimals;
      }).then((r) => decimals = r),
    ]);

    return (balance, decimals);
  }

  @override
  Future<int> getTokenDecimals(IToken token) async {
    if (token.address.isEmpty) {
      if (token.symbol == _network.nativeCurrency.symbol) {
        return solDecimalPlaces;
      }
      throw UnsupportedError('${token.symbol} is not supported to query decimals because it has no contract address');
    }

    if (token.address == tokenSOLAddress || token.address == tokenSOLWrappedAddress) {
      return solDecimalPlaces;
    }

    final mint = await client.getMint(
      address: Ed25519HDPublicKey.fromBase58(token.address),
    );
    return mint.decimals;
  }

  @override
  privy.EmbeddedSolanaWallet getWallet() {
    final wallet = _user?.embeddedSolanaWallets.firstOrNull;
    if (wallet == null) {
      throw StateError('No Solana wallet found');
    }
    return wallet;
  }

  @override
  Future<String> sendNativeToken(
    String to,
    BigInt value, {
    BigInt? gasLimit,
  }) async {
    LogUtil.d(
      'Sending SOL:'
      '\n|-- to=$to'
      '\n|-- value=$value',
      tag: _tag,
      tagWithTrace: false,
    );

    final from = _walletAddress;
    if (from == null || from.isEmpty) {
      throw StateError('Empty wallet address');
    }

    final solanaWallet = getWallet();

    final fromPublicKey = Ed25519HDPublicKey.fromBase58(from);
    final toPublicKey = Ed25519HDPublicKey.fromBase58(to);

    final instruction = SystemInstruction.transfer(
      fundingAccount: fromPublicKey,
      recipientAccount: toPublicKey,
      lamports: value.toInt(),
    );

    final (signedMessage, compiledMessage) = await _signTransaction(
      wallet: solanaWallet,
      instruction: instruction,
      fromPublicKey: fromPublicKey,
    );

    final completer = Completer<String>();
    signedMessage.fold(
      onSuccess: (signature) => _sendTransaction(
        completer: completer,
        signature: signature,
        compiledMessage: compiledMessage,
        fromPublicKey: fromPublicKey,
      ),
      onFailure: (error) {
        completer.completeError(error);
      },
    );
    return completer.future;
  }

  // 发送 SPL 代币交易
  @override
  Future<String> sendToken(
    String tokenAddress,
    String to,
    BigInt value, {
    BigInt? gasLimit,
  }) async {
    LogUtil.d(
      'Sending SPL:'
      '\n|-- token=$tokenAddress'
      '\n|-- to=$to'
      '\n|-- value=$value',
      tag: _tag,
      tagWithTrace: false,
    );

    final from = _walletAddress;
    if (from == null || from.isEmpty) {
      throw StateError('Empty wallet address');
    }

    final solanaWallet = getWallet();

    final fromPublicKey = Ed25519HDPublicKey.fromBase58(from);
    final toPublicKey = Ed25519HDPublicKey.fromBase58(to);
    final tokenMint = Ed25519HDPublicKey.fromBase58(tokenAddress);

    final commitment = Commitment.finalized;

    final fromATA = await client.getAssociatedTokenAccount(
      owner: fromPublicKey,
      mint: tokenMint,
      commitment: commitment,
    );
    if (fromATA == null) {
      throw NoAssociatedTokenAccountException(from, tokenMint.toBase58());
    }

    final toATA = await client.getAssociatedTokenAccount(
      owner: toPublicKey,
      mint: tokenMint,
      commitment: commitment,
    );
    if (toATA == null) {
      throw NoAssociatedTokenAccountException(to, tokenMint.toBase58());
    }

    final instruction = TokenInstruction.transfer(
      source: Ed25519HDPublicKey.fromBase58(fromATA.pubkey),
      destination: Ed25519HDPublicKey.fromBase58(toATA.pubkey),
      owner: fromPublicKey,
      amount: value.toInt(),
      tokenProgram: TokenProgramType.tokenProgram,
    );

    final (signedMessage, compiledMessage) = await _signTransaction(
      wallet: solanaWallet,
      instruction: instruction,
      fromPublicKey: fromPublicKey,
    );

    final completer = Completer<String>();
    signedMessage.fold(
      onSuccess: (signature) => _sendTransaction(
        completer: completer,
        signature: signature,
        commitment: commitment,
        compiledMessage: compiledMessage,
        fromPublicKey: fromPublicKey,
      ),
      onFailure: (error) {
        completer.completeError(error);
      },
    );
    return completer.future;
  }

  Future<(privy.Result<String> signedMessage, CompiledMessage compiledMessage)> _signTransaction({
    required privy.EmbeddedSolanaWallet wallet,
    required Instruction instruction,
    required Ed25519HDPublicKey fromPublicKey,
  }) async {
    final recentBlockhash = await rpcClient.getLatestBlockhash();
    final message = Message.only(instruction);
    final compiledMessage = message.compile(
      recentBlockhash: recentBlockhash.value.blockhash,
      feePayer: fromPublicKey,
    );
    final serializedMessage = compiledMessage.toByteArray().toList();
    final serializedTransaction = base64.encode(serializedMessage);
    final signedMessage = await wallet.provider.signMessage(serializedTransaction);
    return (signedMessage, compiledMessage);
  }

  Future<void> _sendTransaction({
    required Completer<String> completer,
    required String signature,
    required CompiledMessage compiledMessage,
    required Ed25519HDPublicKey fromPublicKey,
    Commitment commitment = Commitment.finalized,
  }) async {
    try {
      final decodedSignature = base64.decode(signature);
      final deserializedSignature = Signature(decodedSignature, publicKey: fromPublicKey);
      final tx = SignedTx(compiledMessage: compiledMessage, signatures: [deserializedSignature]);
      final txHash = await rpcClient.sendTransaction(tx.encode(), preflightCommitment: commitment);
      await client.waitForSignatureStatus(txHash, status: commitment);
      completer.complete(txHash);
    } catch (e, s) {
      completer.completeError(e, s);
    }
  }
}
