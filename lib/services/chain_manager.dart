import 'dart:async' show Timer, Completer;

import 'package:solana/solana.dart' show solDecimalPlaces;

import '/api/okx.dart' show OKXWalletProfileToken;
import '/internals/box.dart' show BoxService;
import '/internals/methods.dart' show handleExceptions;
import '/internals/privy.dart';
import '/internals/riverpod.dart' show globalContainer;
import '/models/business.dart';
import '/provider/settings.dart' show settingsProvider;
import 'evm.dart';
import 'service.dart';
import 'solana.dart';

class ChainManager {
  ChainManager._();

  static ChainManager? _instance;

  static ChainManager get instance {
    _instance ??= ChainManager._();
    return _instance!;
  }

  Network get _currentNetwork => BoxService.getNetworkSelected();

  Timer? _privyAccessTokenTimer;

  bool get initialized => _chainService != null;
  ChainService? _chainService;

  String? get walletAddress => _chainService?.walletAddress;

  bool get initializing => _initializeLock != null;
  Completer<void>? _initializeLock;

  Future<void> initialize() {
    if (_initializeLock case final lock?) {
      return lock.future;
    }
    final lock = _initializeLock = Completer<void>();
    Future<void>(() async {
      await _initializeClient(_currentNetwork);
      _initializePrivyAccessTokenTimer();
    }).then(lock.complete, onError: lock.completeError).whenComplete(() {
      _initializeLock = null;
    });
    return lock.future;
  }

  Future<void> switchNetwork(Network network) async {
    final current = _currentNetwork;
    try {
      await _initializeClient(network);
      globalContainer.read(settingsProvider).selectedNetwork = network;
    } catch (e) {
      _initializeClient(current);
      globalContainer.read(settingsProvider).selectedNetwork = current;
      rethrow;
    }
  }

  Future<void> _initializeClient(Network network) async {
    await _chainService?.dispose();
    _chainService = null;

    final state = await privyClient.getAuthState();
    final user = state.user;
    if (user == null) {
      return;
    }

    final ChainService service;
    if (network.isSolanaNetwork) {
      service = SolanaService();
    } else {
      service = EvmService();
    }
    service.initialize(
      network: network,
      user: user,
    );
    _chainService = service;
  }

  Future<void> _initializePrivyAccessTokenTimer() async {
    _privyAccessTokenTimer?.cancel();
    _privyAccessTokenTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      final state = await privyClient.getAuthState();
      final user = state.user;
      if (user == null) {
        return;
      }

      final tokenResult = await user.getAccessToken();
      tokenResult.fold(
        onSuccess: (_) {},
        onFailure: (error) {
          throw error;
        },
      );

      final userResult = await user.refresh();
      userResult.fold(
        onSuccess: (_) {},
        onFailure: (error) {
          throw error;
        },
      );

      _chainService?.updatePrivyUser(user);
    });
  }

  // 获取代币余额
  Future<(BigInt balance, int decimals)> getTokenBalance(String tokenAddress, String? walletAddress) async {
    if (walletAddress == null || walletAddress.isEmpty) {
      return (BigInt.zero, 1);
    }

    try {
      return await _chainService!.getTokenBalance(tokenAddress, walletAddress);
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return (BigInt.zero, 1);
    }
  }

  // 获取原生代币余额
  Future<(BigInt balance, int decimals)> getNativeBalance(String? address) async {
    if (address == null || address.isEmpty) {
      return (BigInt.zero, 1);
    }

    try {
      final BigInt balance = await _chainService!.getNativeTokenBalance(address);
      final int decimals;
      if (_currentNetwork.isSolanaNetwork) {
        decimals = solDecimalPlaces;
      } else {
        decimals = 18;
      }

      return (balance, decimals);
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return (BigInt.zero, 1);
    }
  }

  // 获取代币 decimals
  Future<int> getTokenDecimals(IToken token) {
    return _chainService!.getTokenDecimals(token);
  }

  // 发送原生代币交易
  Future<String> sendNativeTransaction({
    required String to,
    required BigInt value,
    BigInt? gasLimit,
  }) {
    return _chainService!.sendNativeToken(to, value, gasLimit: gasLimit);
  }

  // 发送代币交易
  Future<String> sendTokenTransaction({
    required String tokenAddress,
    required String to,
    required BigInt value,
    BigInt? gasLimit,
  }) {
    return _chainService!.sendToken(tokenAddress, to, value, gasLimit: gasLimit);
  }

  // 向后兼容的通用交易发送方法
  Future<String> sendTransaction({
    required IToken token,
    required String to,
    required BigInt value,
  }) async {
    final Network network;
    if (token is OKXWalletProfileToken) {
      network = BoxService.getNetworkByIdOKX(token.chainIdOKX);
    } else {
      network = BoxService.getNetworkByName(token.chainName);
    }

    final isNative = network.nativeCurrency.symbol == token.symbol;
    if (isNative) {
      return sendNativeTransaction(to: to, value: value);
    } else {
      return sendTokenTransaction(tokenAddress: token.address, to: to, value: value);
    }
  }

  // 关闭连接并清理状态
  Future<void> dispose() async {
    _privyAccessTokenTimer?.cancel();
    _chainService?.dispose();
    _chainService = null;
  }
}
