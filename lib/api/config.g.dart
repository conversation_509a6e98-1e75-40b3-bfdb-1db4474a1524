// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Config _$ConfigFromJson(Map json) => _Config(
  chainTokenSymbolFilters: (json['chainTokenFilters'] as Map).map(
    (k, e) => MapEntry(
      k as String,
      (e as List<dynamic>).map((e) => e as String).toList(),
    ),
  ),
  chainFilters: (json['chainFilters'] as List<dynamic>)
      .map((e) => (e as num).toInt())
      .toList(),
  activationGuide: json['activationGuide'] as bool? ?? false,
  events: (json['events'] as List<dynamic>)
      .map((e) => EventItem.fromJson(Map<String, dynamic>.from(e as Map)))
      .toList(),
  useOkxApi: json['okx'] as bool? ?? true,
);

Map<String, dynamic> _$ConfigToJson(_Config instance) => <String, dynamic>{
  'chainTokenFilters': instance.chainTokenSymbolFilters,
  'chainFilters': instance.chainFilters,
  'activationGuide': instance.activationGuide,
  'events': instance.events.map((e) => e.toJson()).toList(),
  'okx': instance.useOkxApi,
};
