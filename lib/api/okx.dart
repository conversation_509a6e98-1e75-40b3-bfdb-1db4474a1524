import 'dart:async';
import 'dart:io' as io show HttpHeaders;

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart' show MENullableObjectExtension;
import 'package:uuid/uuid.dart';

import '/constants/constants.dart' show tokenSOLAddress;
import '/internals/box.dart' show BoxService;
import '/internals/methods.dart' show handleExceptions;
import '/models/_shared.dart' show NumberDecimalRequiredConverter;
import '/models/business.dart' show IToken, IWalletSummary, WalletPortfolio;
import '/provider/api.dart' hide apiOKXProvider;

part 'okx.freezed.dart';

part 'okx.g.dart';

const _baseUrl = 'https://web3.okx.com';
const _succeedCodeInt = 0;
const _chainIndexSOL = 501;
const _userAgent =
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
    'AppleWebKit/537.36 (KHTML, like Gecko) '
    'Chrome/********* Safari/537.36';

abstract interface class _Paths {
  static String get walletProfile => '/priapi/v2/wallet/asset/profile/all/explorer';
}

class OKXApi {
  OKXApi(this.ref);

  final Ref ref;

  late final _http = ref
      .read(httpProvider)
      .clone(
        interceptors: Interceptors()..addAll([_OkxInterceptor(), ApiCodeInterceptor(), ApiLogInterceptor()]),
        options: BaseOptions(
          baseUrl: _baseUrl,
          extra: <String, Object>{httpExtraKeySucceedCode: _succeedCodeInt},
        ),
      );

  Future<(OKXWalletProfileSummary, List<OKXWalletProfileToken>)> getWalletProfile({
    required String address,
    required String chainIdOKX,
    bool excludeRiskType = true,
    CancelToken? cancelToken,
  }) async {
    Future<OKXWalletProfile> request(int page) async {
      final res = await _http
          .post(
            _Paths.walletProfile,
            data: <String, dynamic>{
              'hideValueless': false,
              'address': address,
              'forceRefresh': true,
              'page': page,
              'limit': 100,
              'chainIndexes': [chainIdOKX],
            },
            cancelToken: cancelToken,
          )
          .retry();
      final rep = Rep.fromJson(
        res.data,
        (v) => OKXWalletProfile.fromJson(v.asJson()),
        succeedCode: _succeedCodeInt,
      );
      return rep.data;
    }

    OKXWalletProfileSummary summary;
    final list = <OKXWalletProfileToken>[];

    int total;
    int page = 1;
    while (true) {
      final result = await request(page);
      summary = result.summary ?? OKXWalletProfileSummary.empty;
      total = result.tokens.total;

      if (excludeRiskType) {
        for (final token in result.tokens.tokenlist) {
          // Kick out risk type tokens.
          if (token.coinBalanceDetail.isRiskType) {
            total--;
            continue;
          }
          list.add(token);
        }
      } else {
        list.addAll(result.tokens.tokenlist);
      }

      if (list.length >= total) {
        break;
      }
      page++;
    }
    return (summary, list);
  }
}

final class _OkxInterceptor extends QueuedInterceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    try {
      final uuid = const Uuid().v4();
      options.headers.addAll(
        <String, String>{
          'app-type': 'web',
          'device-token': uuid,
          'devid': uuid,
          'origin': _baseUrl,
          'platform': 'web',
          'x-cdn': _baseUrl,
          io.HttpHeaders.userAgentHeader: _userAgent,
        },
      );
      options.queryParameters = {
        ...options.uri.queryParameters,
        't': '${DateTime.now().millisecondsSinceEpoch}000',
      };
      if (options.path.contains(_Paths.walletProfile)) {
        options.data['userUniqueId'] = uuid;
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
    }
    handler.next(options);
  }
}

@freezed
sealed class OKXWalletProfile with _$OKXWalletProfile {
  const factory OKXWalletProfile({
    @JsonKey(name: 'walletAssetSummary') OKXWalletProfileSummary? summary,
    @JsonKey(name: 'tokens') required OKXWalletProfileTokens tokens,
  }) = _OKXWalletProfile;

  factory OKXWalletProfile.fromJson(Map<String, dynamic> json) => _$OKXWalletProfileFromJson(json);
}

@freezed
sealed class OKXWalletProfileSummary with _$OKXWalletProfileSummary implements IWalletSummary {
  @Implements<IWalletSummary>()
  const factory OKXWalletProfileSummary({
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'tokenTotalCurrencyAmount')
    required Decimal tokenTotalCurrencyAmount,
  }) = _OKXWalletProfileSummary;

  const OKXWalletProfileSummary._();

  factory OKXWalletProfileSummary.fromJson(Map<String, dynamic> json) => _$OKXWalletProfileSummaryFromJson(json);

  static final empty = OKXWalletProfileSummary(
    tokenTotalCurrencyAmount: Decimal.zero,
  );

  @override
  Decimal get totalValueUsd => tokenTotalCurrencyAmount;

  @override
  bool get hasEmptyValue => false;
}

@freezed
sealed class OKXWalletProfileTokens with _$OKXWalletProfileTokens {
  const factory OKXWalletProfileTokens({
    @JsonKey(name: 'total') required int total,
    @JsonKey(name: 'pageSize') required int pageSize,
    @JsonKey(name: 'currentPage') required int currentPage,
    @JsonKey(name: 'isOverExplorerLimit') required bool isOverExplorerLimit,
    @JsonKey(name: 'tokenlist') required List<OKXWalletProfileToken> tokenlist,
  }) = _OKXWalletProfileTokens;

  factory OKXWalletProfileTokens.fromJson(Map<String, dynamic> json) => _$OKXWalletProfileTokensFromJson(json);
}

@freezed
abstract class OKXWalletProfileToken with _$OKXWalletProfileToken implements IToken {
  const factory OKXWalletProfileToken({
    @NumberDecimalRequiredConverter() @JsonKey(name: 'coinAmount') required Decimal realBalance,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'currencyAmount') required Decimal valueUsd,
    @JsonKey(name: 'symbol') required String symbol,
    @JsonKey(name: 'imageUrl') required String logo,
    @JsonKey(name: 'name') required String name,
    @JsonKey(name: 'coinBalanceDetails') required List<OKXWalletProfileTokenDetail> coinBalanceDetails,
    @JsonKey(name: 'coinPriceInfo') required OKXWalletProfileTokenPrice coinPriceInfo,
  }) = _OKXWalletProfileToken;

  const OKXWalletProfileToken._();

  factory OKXWalletProfileToken.fromJson(Map<String, dynamic> json) => _$OKXWalletProfileTokenFromJson(json);

  OKXWalletProfileTokenDetail get coinBalanceDetail => coinBalanceDetails.first;

  @override
  String get name => coinBalanceDetail.name;

  @override
  String get symbol => coinBalanceDetail.symbol;

  @override
  String get logo => coinBalanceDetail.logo;

  @override
  String get address {
    if (coinBalanceDetail.address case final address?) {
      return address;
    }
    if (coinBalanceDetail.chainIndex == _chainIndexSOL && symbol == 'SOL') {
      return tokenSOLAddress;
    }
    return '';
  }

  @override
  int? get decimals => null;

  @override
  String get chainName =>
      BoxService.getNetworksFromLocal().firstWhereOrNull((e) => e.chainIndexOKX == chainIdOKX)?.network ?? '';

  @override
  String get chainIdOKX => coinBalanceDetail.chainId.toString();

  @override
  Decimal get priceUsd => coinBalanceDetail.coinPriceVo.price;

  @override
  Decimal get valueUsd => priceUsd * realBalance;
}

@freezed
sealed class OKXWalletProfileTokenDetail with _$OKXWalletProfileTokenDetail {
  const factory OKXWalletProfileTokenDetail({
    @JsonKey(name: 'name') required String name,
    @JsonKey(name: 'symbol') required String symbol,
    @JsonKey(name: 'address') String? address,
    @JsonKey(name: 'imageUrl') required String logo,
    @JsonKey(name: 'chainName') required String chainName,
    @JsonKey(name: 'chainId') required int chainId,
    @JsonKey(name: 'chainIndex') required int chainIndex,
    @JsonKey(name: 'systemToken') required bool systemToken,
    @JsonKey(name: 'chainImageUrl') required String chainImageUrl,
    @JsonKey(name: 'coinPriceVo') required OKXWalletProfileTokenPrice coinPriceVo,
    @JsonKey(name: 'isRiskType') required bool isRiskType,
    @JsonKey(name: 'userAddress') required String userAddress,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'coinAmount') required Decimal realBalance,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'currencyAmount') required Decimal valueUsd,
  }) = _OKXWalletProfileTokenDetail;

  const OKXWalletProfileTokenDetail._();

  factory OKXWalletProfileTokenDetail.fromJson(Map<String, dynamic> json) =>
      _$OKXWalletProfileTokenDetailFromJson(json);
}

@freezed
sealed class OKXWalletProfileTokenPrice with _$OKXWalletProfileTokenPrice {
  const factory OKXWalletProfileTokenPrice({
    @JsonKey(name: 'address') String? address,
    @JsonKey(name: 'hasPrice') required bool hasPrice,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'price') required Decimal price,
    @JsonKey(name: 'hasPercent') required bool hasPercent,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'priceChangePercent24h') required Decimal priceChangePercent24h,
    @JsonKey(name: 'chainId') required int chainId,
  }) = _OKXWalletProfileTokenPrice;

  factory OKXWalletProfileTokenPrice.fromJson(Map<String, dynamic> json) => _$OKXWalletProfileTokenPriceFromJson(json);
}

@freezed
sealed class WalletPortfolioOKX2 with _$WalletPortfolioOKX2 implements WalletPortfolio<OKXWalletProfileToken> {
  @Implements<WalletPortfolio<OKXWalletProfileToken>>()
  const factory WalletPortfolioOKX2({
    required OKXWalletProfileSummary summary,
    required List<OKXWalletProfileToken> tokens,
  }) = _WalletPortfolioOKX2;

  const WalletPortfolioOKX2._();

  factory WalletPortfolioOKX2.fromJson(Map<String, dynamic> json) => _$WalletPortfolioOKX2FromJson(json);

  static final empty = WalletPortfolioOKX2(
    summary: OKXWalletProfileSummary.empty,
    tokens: <OKXWalletProfileToken>[],
  );

  @override
  OKXWalletProfileToken? tokenByAddress(
    String address,
  ) => tokens.firstWhereOrNull((e) => e.address == address);
}
