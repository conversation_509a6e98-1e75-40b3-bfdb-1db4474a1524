import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '/constants/envs.dart' show env, envUrlConfig;
import '/models/business.dart' show EventItem, Network;
import '/provider/api.dart' hide apiConfigProvider;

part 'config.freezed.dart';

part 'config.g.dart';

final class ConfigApi {
  ConfigApi(this.ref);

  final Ref ref;

  late final _http = ref
      .read(httpProvider)
      .clone(
        interceptors: Interceptors()..add(ApiLogInterceptor()),
        options: BaseOptions(baseUrl: envUrlConfig),
      );

  Future<Config> getConfig({CancelToken? cancelToken}) async {
    final res = await _http.get('/config_${env.env}.json', cancelToken: cancelToken).retry();
    final data = Config.fromJson(res.data);
    return data;
  }

  Future<TokenSymbolsMapping> getTokenSymbolsMapping({CancelToken? cancelToken}) async {
    final res = await _http.get('/token_symbols_mapping.json', cancelToken: cancelToken).retry();
    final json = res.data as Map<String, dynamic>;
    final data = <TokenSymbolProvider, Map<String, String>>{
      for (final entry in json.entries)
        TokenSymbolProvider.values.firstWhere((e) => e.name == entry.key): entry.value.cast<String, String>(),
    };
    return data;
  }
}

@freezed
sealed class Config with _$Config {
  const factory Config({
    /// chainTokenFilters 示例:
    /// {
    ///   "1": ["ETH", "USDC", "USDT"],
    ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
    ///   ...
    /// }
    @JsonKey(name: 'chainTokenFilters') required Map<String, List<String>> chainTokenSymbolFilters,

    /// 不支持的链ID列表
    /// 例如: [10, 324, 59144]
    @JsonKey(name: 'chainFilters') required List<int> chainFilters,

    /// 是否开启引导
    @JsonKey(name: 'activationGuide') @Default(false) bool activationGuide,

    /// 事件列表
    @JsonKey(name: 'events') required List<EventItem> events,

    /// 是否启用全局 OKX 接口查询
    @JsonKey(name: 'okx') @Default(true) bool useOkxApi,
  }) = _Config;

  const Config._();

  factory Config.fromJson(Map<String, dynamic> json) => _$ConfigFromJson(json);

  bool isNetworkUnsupported(Network network) => chainFilters.contains(network.chainIdEvm);
}

enum TokenSymbolProvider {
  okx,
}

typedef TokenSymbolsMapping = Map<TokenSymbolProvider, Map<String, String>>;
