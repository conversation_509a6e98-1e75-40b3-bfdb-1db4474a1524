import 'dart:convert' show jsonEncode, jsonDecode;
import 'dart:io' as io show HttpHeaders;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_utils/me_utils.dart' show LogUtil;
import 'package:mime/mime.dart' show lookupMimeType;
import 'package:path/path.dart' as p;

import '/constants/envs.dart' show envApiUrlService;
import '/internals/box.dart' show BoxService;
import '/models/business.dart';
import '/models/card.dart';
import '/models/user.dart';
import '/provider/api.dart' hide apiServiceProvider;
import '/routes/card3_routes.dart' show Routes;
import '_path.dart';

final class ServiceApi {
  ServiceApi(this.ref);

  final Ref ref;

  late final http = ref
      .read(httpProvider)
      .clone(
        options: BaseOptions(
          baseUrl: '$envApiUrlService/active',
        ),
      );

  Future<List<AppConfig>> getAppConfigs({CancelToken? cancelToken}) async {
    final res = await http
        .get(
          ApiPath.service.config.configs,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) {
      final setting = (v as Map<String, dynamic>?)?['setting'] ?? '[]';
      final decoded = jsonDecode(setting) as List? ?? [];
      final list = decoded.cast<Map>().map((e) => AppConfig.fromJson(e.cast())).toList();
      return list;
    });
    return rep.data;
  }

  Future<String> getRefreshJWT({CancelToken? cancelToken}) async {
    final res = await http
        .get(
          ApiPath.service.user.refreshToken,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<void> emailSendCode({
    required String email,
    String? referral,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.email.sendCode,
          data: <String, dynamic>{
            'email': email,
            'referralCode': referral,
          },
          options: Options(
            headers: <String, dynamic>{
              io.HttpHeaders.authorizationHeader: '', // Anonymous.
            },
          ),
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<String> emailLogin({
    required String email,
    required String code,
    required UserReferralRequest referralRequest,
    CancelToken? cancelToken,
  }) async {
    final data = <String, dynamic>{
      'email': email,
      'code': code,
      ...referralRequest.toRequestData(),
    };
    final res = await http
        .post(
          ApiPath.service.email.login,
          data: data,
          options: Options(
            headers: <String, dynamic>{
              io.HttpHeaders.authorizationHeader: '', // Anonymous.
            },
          ),
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<String> privyLogin({
    required String token,
    String? referral,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.privy.login,
          data: <String, dynamic>{
            'token': token,
            'referralCode': referral,
          },
          options: Options(
            headers: <String, dynamic>{
              io.HttpHeaders.authorizationHeader: '', // Anonymous.
            },
          ),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<UserInfo> getSelfUserInfo({
    String? token,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.info,
          cancelToken: cancelToken,
          options: Options(
            headers: {
              if (token != null) io.HttpHeaders.authorizationHeader: 'Bearer $token',
            },
          ),
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => UserInfo.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<Map<String, dynamic>> updateUserInfo({
    String? avatar,
    String? dynamicAvatar,
    String? name,
    String? title,
    String? company,
    String? handle,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .put(
          ApiPath.service.user.info,
          data: <String, Object>{
            'avatar': ?avatar,
            'dynamicAvatar': ?dynamicAvatar,
            'name': ?name,
            'title': ?title,
            'company': ?company,
            'handle': ?handle,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v.asJson());
    return rep.data;
  }

  Future<String> deleteUser({
    CancelToken? cancelToken,
  }) async {
    final res = await http.delete(
      ApiPath.service.user.delete,
      cancelToken: cancelToken,
    );
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<UserSettingsRequest> getUserSettings({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.settings,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => UserSettingsRequest.fromJson(v.asJson()));
    return rep.data;
  }

  Future<UserSettingsRequest> updateSettings(
    UserSettingsRequest request, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        ApiPath.service.user.settings,
        data: request.toJson(),
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => UserSettingsRequest.fromJson(v.asJson()));
    return rep.data;
  }

  Future<List<CardInfo>> getMyCards({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.cards,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(
      res.data,
      (v) => CardInfo.fromJson(v.asJson()),
    );
    return rep.list;
  }

  Future<CardInfo> getCard({
    required String cardCode,
    String? ctr,
    String? cmac,
    String? activateCode,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.wallet.getCard,
          data: <String, dynamic>{
            'cardCode': cardCode,
            'ctr': ?ctr,
            'cmac': ?cmac,
            'activeCode': ?activateCode,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => CardInfo.fromJson(v.asJson()));
    return rep.data;
  }

  Future<CardInfoBasic> getCardInfoBasic({
    required Uri formalizedUri,
    CancelToken? cancelToken,
  }) async {
    final res = await ref.read(httpProvider).getUri(formalizedUri).retry(retryTimes: 2);
    if (res.data case final data when data is! Map<String, Object?>) {
      LogUtil.e(
        'Invalid card info basic response: $data\n[URI]: $formalizedUri',
        stackTrace: StackTrace.current,
      );
    }
    final rep = Rep.fromJson(
      res.data,
      (v) => CardInfoBasic.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Map<String, dynamic> _getCardActivationData({
    required String uid,
    String? ctr,
    String? cmac,
    String? activateCode,
  }) {
    final data = <String, dynamic>{};

    // 如果有ctr和cmac，使用NFC方式激活
    if (ctr != null && ctr.isNotEmpty && cmac != null && cmac.isNotEmpty) {
      data['uid'] = uid;
      data['ctr'] = ctr;
      data['cmac'] = cmac;
    }
    // 否则使用cardCode方式激活
    else {
      data['cardCode'] = uid;
      if (activateCode case final code? when code.isNotEmpty) {
        data['activeCode'] = code;
      }
    }

    return data;
  }

  Future<String> activateCardByParams({
    required String uid,
    String? ctr,
    String? cmac,
    String? activateCode,
    CancelToken? cancelToken,
  }) async {
    final data = _getCardActivationData(
      uid: uid,
      ctr: ctr,
      cmac: cmac,
      activateCode: activateCode,
    );
    final res = await http
        .post(
          ApiPath.service.user.activateCard,
          data: data,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<void> deactivateCardByParams({
    required String uid,
    String? ctr,
    String? cmac,
    String? activateCode,
    CancelToken? cancelToken,
  }) async {
    final data = _getCardActivationData(
      uid: uid,
      ctr: ctr,
      cmac: cmac,
      activateCode: activateCode,
    );
    final res = await http
        .post(
          ApiPath.service.user.deactivateCard,
          data: data,
          cancelToken: cancelToken,
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<List<Social>> socialQuery({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.socials,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.list;
  }

  Future<Social> socialAdd({
    required String handleName,
    required String platformName,
    String? platformUrl,
    String? imageUrl,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.user.socials,
          data: <String, dynamic>{
            'handleName': handleName,
            'platformName': platformName,
            'platformUrl': platformUrl,
            'qrcode': ?imageUrl,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Social> socialUpdate({
    required String socialId,
    required String handleName,
    required String platformName,
    String? platformUrl,
    String? imageUrl,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .put(
          ApiPath.service.user.socialUpdate(socialId: socialId),
          data: <String, dynamic>{
            'handleName': handleName.trim(),
            'platformName': platformName.trim(),
            'platformUrl': platformUrl?.trim(),
            'qrcode': ?imageUrl,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.data;
  }

  Future<String> socialDelete({
    required int socialId,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .delete(
          ApiPath.service.user.socialDel(socialId: socialId.toString()),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<String> socialsReorder({
    required Map<String, int> idInOrders,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .put(
          ApiPath.service.user.socialSort,
          data: <String, dynamic>{
            'sorts': idInOrders,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<Paged<Message>> listMessages({
    required int page,
    int size = 20,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.listMessages,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': 20,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => Message.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<IntegralPoint>> getPoints({
    required int pageNum,
    required int pageSize,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.points,
          queryParameters: <String, dynamic>{
            'pageNum': pageNum,
            'pageSize': pageSize,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => IntegralPoint.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<CoverInfo> getCoverInfo({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.cover.getInfo,
          queryParameters: <String, String>{'code': code},
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => CoverInfo.fromJson(v.asJson()));
    return rep.data;
  }

  Future<CustomizeCardOrder> createCardCover({
    required String code,
    required String username,
    String? fileContent,
    String? title,
    String? company,
    String? eventId,
    String? previewContent,
    CancelToken? cancelToken,
  }) async {
    final res = await http.post(
      ApiPath.service.cover.create,
      data: <String, dynamic>{
        'code': code,
        'username': username,
        'fileContent': fileContent,
        'title': title,
        'company': company,
        'eventId': eventId,
        'previewContent': previewContent,
      },
      cancelToken: cancelToken,
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => CustomizeCardOrder.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<CustomizeCardOrder> getCustomizeCardOrderByCode({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.cover.orderByCode(code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => CustomizeCardOrder.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Paged<CustomizeCardOrder>> getCustomizeCardOrders({
    required int page,
    required int size,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.cover.orders,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': size,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => CustomizeCardOrder.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<ReferralLog>> getReferralLogs({
    required int pageNum,
    required int pageSize,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.referralLogs,
          queryParameters: <String, dynamic>{
            'pageNum': pageNum,
            'pageSize': pageSize,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => ReferralLog.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<String> uploadAvatar({
    required String fileContent,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.user.uploadAvatar,
          data: <String, dynamic>{
            'fileContent': fileContent,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<TokenPrices> getTokenPrices({
    required String symbol,
    required String chainName,
    required String ucid,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.price.latestPrice,
          queryParameters: <String, dynamic>{
            if (symbol.trim() case final symbol when symbol.isNotEmpty) 'symbol': symbol,
            if (chainName.trim() case final chainName when chainName.isNotEmpty) 'chainName': chainName,
            if (ucid.trim() case final ucid when ucid.isNotEmpty) 'ucid': ucid,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => TokenPrices.fromJson(v.asJson()));
    return rep.data;
  }

  Future<QRCodeDynamicResult> getUserQRCodeDynamic({CancelToken? cancelToken}) async {
    final res = await http
        .get(
          ApiPath.service.user.qrCode,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => QRCodeDynamicResult.fromJson(v.asJson()));
    return rep.data;
  }

  Future<UserInfo> getPublicProfile({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.profile.queryPub(code: code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => UserInfo.fromJson(v.asJson()));
    return rep.data;
  }

  Future<String?> getReferralCodeFromHandle(String handle, {CancelToken? cancelToken}) async {
    final res = await http
        .get(
          ApiPath.service.profile.handleGetReferralCode(handle: handle),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String?);
    return rep.data;
  }

  Future<List<Social>> getPublicSocials({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.profile.socialGetPub(code: code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.list;
  }

  Future<List<String>> getExtendProfileTopicsAll({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.profile.topicsList,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => (v as List).map((item) => item.toString()).toList(),
    );
    return rep.data;
  }

  Future<List<String>> getExtendProfileRolesAll({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.profile.rolesList,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => (v as List).map((item) => item.toString()).toList(),
    );
    return rep.data;
  }

  Future<ExtendProfile> getExtendProfile({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.profileExtend,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => ExtendProfile.fromJson(v?.asJson() ?? {}),
    );
    return rep.data;
  }

  Future<ExtendProfile> getExtendProfilePublic({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.profile.extendPub(code: code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => ExtendProfile.fromJson(v?.asJson() ?? {}),
    );
    return rep.data;
  }

  Future<void> updateExtendTopics({
    required String topics,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.user.userTopics,
          data: <String, dynamic>{
            'topics': topics,
          },
          cancelToken: cancelToken,
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> updateExtendRoles({
    required String roles,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.user.userRoles,
          data: <String, dynamic>{
            'roles': roles,
          },
          cancelToken: cancelToken,
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> updateExtendGithubHandle({
    required String githubHandle,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.user.userGitHubHandle,
          data: <String, dynamic>{
            'githubHandle': githubHandle,
          },
          cancelToken: cancelToken,
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<UserRelation> toggleUserFollow({
    required String referralCode,
    required bool follow,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          follow
              ? ApiPath.service.user.follow(referralCode: referralCode)
              : ApiPath.service.user.unfollow(referralCode: referralCode),
          cancelToken: cancelToken,
        )
        .retry();
    UserRelation? relation;
    final rep = Rep.fromJson(
      res.data,
      (v) => v == null ? null : UserRelation.fromJson(v.asJson()),
    );
    relation = rep.data;
    relation ??= await getUserRelation(referralCode: referralCode, cancelToken: cancelToken);
    return relation;
  }

  Future<UserRelation> getUserRelation({
    required String referralCode,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.relation(referralCode: referralCode),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => UserRelation.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Paged<UserFromRelation>> getFollowingList({
    required int page,
    int size = 20,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.followingList,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': size,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => UserFromRelation.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<UserFromRelation>> getFollowerList({
    required int page,
    int size = 20,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.followerList,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': size,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => UserFromRelation.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<List<EventItem>> getDiscoveryEvents({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.discovery.configList,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => EventItem.fromJson(v.asJson()));
    return rep.list;
  }

  Future<String> uploadImage({required String imageFilePath, CancelToken? cancelToken}) async {
    final filename = p.basename(imageFilePath);
    final file = await MultipartFile.fromFile(
      imageFilePath,
      filename: filename,
      contentType: lookupMimeType(filename)?.run(DioMediaType.parse),
    );
    final res = await http
        .post(
          ApiPath.service.file.create,
          data: FormData.fromMap({'file': file}),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<List<ImageAIStyle>> getImageStyles({CancelToken? cancelToken}) async {
    final res = await http
        .get(
          ApiPath.service.image.styles,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => ImageAIStyle.fromJson(v.asJson()));
    return rep.list;
  }

  Future<int> getImageTaskQuota({CancelToken? cancelToken}) async {
    final res = await http
        .get(
          ApiPath.service.image.quota,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as int);
    return rep.data;
  }

  Future<ImageAITaskInQueue> createImageTask({
    required String imageFilePath,
    required ImageAIStyle style,
    CancelToken? cancelToken,
    ProgressCallback? onProgress,
  }) async {
    final filename = p.basename(imageFilePath);
    final extension = p.extension(imageFilePath).removeFirst('.');
    final file = await MultipartFile.fromFile(
      imageFilePath,
      filename: filename,
      contentType: lookupMimeType(filename)?.run(DioMediaType.parse),
    );
    final imageVariant = MultipartFile.fromString(
      jsonEncode({
        'ext': extension,
        'styleId': style.id,
      }),
      contentType: DioMediaType.parse(Headers.jsonContentType),
    );
    final res = await http.post(
      ApiPath.service.image.create,
      data: FormData.fromMap({
        'file': file,
        'imageVariant': imageVariant,
      }),
      cancelToken: cancelToken,
      onSendProgress: onProgress,
    );
    final rep = Rep.fromJson(res.data, (v) => ImageAITaskInQueue.fromJson(v.asJson()));
    return rep.data;
  }

  Future<ImageAITask> getImageTaskResult({
    required String taskId,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.image.taskResult(taskId: taskId),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => ImageAITask.fromJson(v.asJson()));
    return rep.data;
  }
}

class ApiServiceUnauthorizedInterceptor extends QueuedInterceptor {
  static const _tag = '🔒 UnauthorizedInterceptor';

  bool _unauthorizeRedirecting = false;

  @override
  void onResponse(response, handler) {
    // Skip when the request is not a service api request.
    if (!response.requestOptions.uri.toString().startsWith(envApiUrlService)) {
      handler.next(response);
      return;
    }

    try {
      if (response.data case {
        'code': final int code,
      } when code == 401) {
        if (_unauthorizeRedirecting) {
          return;
        }
        _unauthorizeRedirecting = true;
        Future<void>(() async {
          await meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.login.name,
            predicate: (_) => false,
          );
          await BoxService.clearUserBox();
        }).whenComplete(() {
          _unauthorizeRedirecting = false;
        });
      }
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s, tag: _tag);
    }

    handler.next(response);
  }
}
