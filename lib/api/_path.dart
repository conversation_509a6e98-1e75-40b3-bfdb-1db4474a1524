abstract final class ApiPath {
  static const op = ApiPathOp._();
  static const service = ApiPathService._();
}

final class ApiPathOp {
  const ApiPathOp._();

  String get tokenList => '/api/queryTokenList';

  String get networkConfig => '/multichain/networkConfig';
}

final class ApiPathService {
  const ApiPathService._();

  final config = const ServicePathConfig._();
  final email = const ServicePathEmail._();
  final file = const ServicePathFile._();
  final privy = const ServicePathPrivy._();
  final user = const ServicePathUser._();
  final wallet = const ServicePathWallet._();
  final price = const ServicePathPrice._();
  final profile = const ServicePathProfile._();
  final cover = const ServicePathCover._();
  final discovery = const ServicePathDiscovery._();
  final image = const ServicePathImage._();
}

final class ServicePathConfig {
  const ServicePathConfig._();

  String get configs => '/app_config/';
}

final class ServicePathEmail {
  const ServicePathEmail._();

  String get sendCode => '/email/sendCode';

  String get login => '/email/login';
}

final class ServicePathFile {
  const ServicePathFile._();

  String get create => '/file/create';
}

final class ServicePathPrivy {
  const ServicePathPrivy._();

  String get login => '/privy/login';
}

final class ServicePathUser {
  const ServicePathUser._();

  String get refreshToken => '/user/refresh_token';

  String get info => '/user/info';

  String get delete => '/user/delete';

  String get settings => '/user/setting';

  String get cards => '/user/myCards';

  String get activateCard => '/user/active_card';

  String get deactivateCard => '/user/inactive_card';

  String get referralLogs => '/user/myReferralLogs';

  String get points => '/user/myIntegralLogs';

  String get listMessages => '/user/listMessages';

  String get uploadAvatar => '/user/uploadAvatar';

  String get socials => '/user/socials';

  String socialUpdate({
    required String socialId,
  }) => '/user/socials/$socialId';

  String socialDel({
    required String socialId,
  }) => '/user/socials/$socialId';

  String get socialSort => '/user/socials_sort';

  String follow({
    required String referralCode,
  }) => '/user/follow?referralCode=$referralCode';

  String unfollow({
    required String referralCode,
  }) => '/user/unfollow?referralCode=$referralCode';

  String relation({
    required String referralCode,
  }) => '/user/relation?referralCode=$referralCode';

  String get followingList => '/user/followings';

  String get followerList => '/user/followers';

  String get qrCode => '/user/qrcode';

  String get profileExtend => '/user/profile';

  String get userTopics => '/user/topics';

  String get userRoles => '/user/roles';

  String get userGitHubHandle => '/user/github';
}

final class ServicePathWallet {
  const ServicePathWallet._();

  String get getCard => '/wallet/getCard';
}

final class ServicePathPrice {
  const ServicePathPrice._();

  String get latestPrice => '/price/latestPrice';
}

final class ServicePathProfile {
  const ServicePathProfile._();

  String queryPub({
    required String code,
  }) => '/profile/pub/$code';

  String socialGetPub({
    required String code,
  }) => '/profile/pub/$code/socials';

  String extendPub({
    required String code,
  }) => '/profile/pub/$code/extend';

  String get topicsList => '/profile/topics';

  String get rolesList => '/profile/roles';

  String handleGetReferralCode({required String handle}) => '/profile/handle/$handle';
}

final class ServicePathCover {
  const ServicePathCover._();

  String get getInfo => '/cover/getInfo';

  String get create => '/cover/create';

  String get orders => '/cover/order';

  String orderByCode(String code) => '/cover/order/$code';
}

final class ServicePathDiscovery {
  const ServicePathDiscovery._();

  String get configList => '/discovery_config/list';
}

final class ServicePathImage {
  const ServicePathImage._();

  String get styles => '/image/styles';

  String get quota => '/image/quota';

  String get create => '/image/create';

  String taskResult({required String taskId}) => '/image/$taskId';
}
