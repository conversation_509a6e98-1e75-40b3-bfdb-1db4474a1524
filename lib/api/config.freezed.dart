// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Config {
  /// chainTokenFilters 示例:
  /// {
  ///   "1": ["ETH", "USDC", "USDT"],
  ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
  ///   ...
  /// }
  @JsonKey(name: 'chainTokenFilters')
  Map<String, List<String>> get chainTokenSymbolFilters;

  /// 不支持的链ID列表
  /// 例如: [10, 324, 59144]
  @JsonKey(name: 'chainFilters')
  List<int> get chainFilters;

  /// 是否开启引导
  @JsonKey(name: 'activationGuide')
  bool get activationGuide;

  /// 事件列表
  @JsonKey(name: 'events')
  List<EventItem> get events;

  /// 是否启用全局 OKX 接口查询
  @JsonKey(name: 'okx')
  bool get useOkxApi;

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConfigCopyWith<Config> get copyWith =>
      _$ConfigCopyWithImpl<Config>(this as Config, _$identity);

  /// Serializes this Config to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Config &&
            const DeepCollectionEquality().equals(
              other.chainTokenSymbolFilters,
              chainTokenSymbolFilters,
            ) &&
            const DeepCollectionEquality().equals(
              other.chainFilters,
              chainFilters,
            ) &&
            (identical(other.activationGuide, activationGuide) ||
                other.activationGuide == activationGuide) &&
            const DeepCollectionEquality().equals(other.events, events) &&
            (identical(other.useOkxApi, useOkxApi) ||
                other.useOkxApi == useOkxApi));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(chainTokenSymbolFilters),
    const DeepCollectionEquality().hash(chainFilters),
    activationGuide,
    const DeepCollectionEquality().hash(events),
    useOkxApi,
  );

  @override
  String toString() {
    return 'Config(chainTokenSymbolFilters: $chainTokenSymbolFilters, chainFilters: $chainFilters, activationGuide: $activationGuide, events: $events, useOkxApi: $useOkxApi)';
  }
}

/// @nodoc
abstract mixin class $ConfigCopyWith<$Res> {
  factory $ConfigCopyWith(Config value, $Res Function(Config) _then) =
      _$ConfigCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'chainTokenFilters')
    Map<String, List<String>> chainTokenSymbolFilters,
    @JsonKey(name: 'chainFilters') List<int> chainFilters,
    @JsonKey(name: 'activationGuide') bool activationGuide,
    @JsonKey(name: 'events') List<EventItem> events,
    @JsonKey(name: 'okx') bool useOkxApi,
  });
}

/// @nodoc
class _$ConfigCopyWithImpl<$Res> implements $ConfigCopyWith<$Res> {
  _$ConfigCopyWithImpl(this._self, this._then);

  final Config _self;
  final $Res Function(Config) _then;

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chainTokenSymbolFilters = null,
    Object? chainFilters = null,
    Object? activationGuide = null,
    Object? events = null,
    Object? useOkxApi = null,
  }) {
    return _then(
      _self.copyWith(
        chainTokenSymbolFilters: null == chainTokenSymbolFilters
            ? _self.chainTokenSymbolFilters
            : chainTokenSymbolFilters // ignore: cast_nullable_to_non_nullable
                  as Map<String, List<String>>,
        chainFilters: null == chainFilters
            ? _self.chainFilters
            : chainFilters // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        activationGuide: null == activationGuide
            ? _self.activationGuide
            : activationGuide // ignore: cast_nullable_to_non_nullable
                  as bool,
        events: null == events
            ? _self.events
            : events // ignore: cast_nullable_to_non_nullable
                  as List<EventItem>,
        useOkxApi: null == useOkxApi
            ? _self.useOkxApi
            : useOkxApi // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [Config].
extension ConfigPatterns on Config {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Config value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Config() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Config value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Config():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Config value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Config() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'chainTokenFilters')
      Map<String, List<String>> chainTokenSymbolFilters,
      @JsonKey(name: 'chainFilters') List<int> chainFilters,
      @JsonKey(name: 'activationGuide') bool activationGuide,
      @JsonKey(name: 'events') List<EventItem> events,
      @JsonKey(name: 'okx') bool useOkxApi,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Config() when $default != null:
        return $default(
          _that.chainTokenSymbolFilters,
          _that.chainFilters,
          _that.activationGuide,
          _that.events,
          _that.useOkxApi,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'chainTokenFilters')
      Map<String, List<String>> chainTokenSymbolFilters,
      @JsonKey(name: 'chainFilters') List<int> chainFilters,
      @JsonKey(name: 'activationGuide') bool activationGuide,
      @JsonKey(name: 'events') List<EventItem> events,
      @JsonKey(name: 'okx') bool useOkxApi,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Config():
        return $default(
          _that.chainTokenSymbolFilters,
          _that.chainFilters,
          _that.activationGuide,
          _that.events,
          _that.useOkxApi,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'chainTokenFilters')
      Map<String, List<String>> chainTokenSymbolFilters,
      @JsonKey(name: 'chainFilters') List<int> chainFilters,
      @JsonKey(name: 'activationGuide') bool activationGuide,
      @JsonKey(name: 'events') List<EventItem> events,
      @JsonKey(name: 'okx') bool useOkxApi,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Config() when $default != null:
        return $default(
          _that.chainTokenSymbolFilters,
          _that.chainFilters,
          _that.activationGuide,
          _that.events,
          _that.useOkxApi,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _Config extends Config {
  const _Config({
    @JsonKey(name: 'chainTokenFilters')
    required final Map<String, List<String>> chainTokenSymbolFilters,
    @JsonKey(name: 'chainFilters') required final List<int> chainFilters,
    @JsonKey(name: 'activationGuide') this.activationGuide = false,
    @JsonKey(name: 'events') required final List<EventItem> events,
    @JsonKey(name: 'okx') this.useOkxApi = true,
  }) : _chainTokenSymbolFilters = chainTokenSymbolFilters,
       _chainFilters = chainFilters,
       _events = events,
       super._();
  factory _Config.fromJson(Map<String, dynamic> json) => _$ConfigFromJson(json);

  /// chainTokenFilters 示例:
  /// {
  ///   "1": ["ETH", "USDC", "USDT"],
  ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
  ///   ...
  /// }
  final Map<String, List<String>> _chainTokenSymbolFilters;

  /// chainTokenFilters 示例:
  /// {
  ///   "1": ["ETH", "USDC", "USDT"],
  ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
  ///   ...
  /// }
  @override
  @JsonKey(name: 'chainTokenFilters')
  Map<String, List<String>> get chainTokenSymbolFilters {
    if (_chainTokenSymbolFilters is EqualUnmodifiableMapView)
      return _chainTokenSymbolFilters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_chainTokenSymbolFilters);
  }

  /// 不支持的链ID列表
  /// 例如: [10, 324, 59144]
  final List<int> _chainFilters;

  /// 不支持的链ID列表
  /// 例如: [10, 324, 59144]
  @override
  @JsonKey(name: 'chainFilters')
  List<int> get chainFilters {
    if (_chainFilters is EqualUnmodifiableListView) return _chainFilters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_chainFilters);
  }

  /// 是否开启引导
  @override
  @JsonKey(name: 'activationGuide')
  final bool activationGuide;

  /// 事件列表
  final List<EventItem> _events;

  /// 事件列表
  @override
  @JsonKey(name: 'events')
  List<EventItem> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  /// 是否启用全局 OKX 接口查询
  @override
  @JsonKey(name: 'okx')
  final bool useOkxApi;

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ConfigCopyWith<_Config> get copyWith =>
      __$ConfigCopyWithImpl<_Config>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ConfigToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Config &&
            const DeepCollectionEquality().equals(
              other._chainTokenSymbolFilters,
              _chainTokenSymbolFilters,
            ) &&
            const DeepCollectionEquality().equals(
              other._chainFilters,
              _chainFilters,
            ) &&
            (identical(other.activationGuide, activationGuide) ||
                other.activationGuide == activationGuide) &&
            const DeepCollectionEquality().equals(other._events, _events) &&
            (identical(other.useOkxApi, useOkxApi) ||
                other.useOkxApi == useOkxApi));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_chainTokenSymbolFilters),
    const DeepCollectionEquality().hash(_chainFilters),
    activationGuide,
    const DeepCollectionEquality().hash(_events),
    useOkxApi,
  );

  @override
  String toString() {
    return 'Config(chainTokenSymbolFilters: $chainTokenSymbolFilters, chainFilters: $chainFilters, activationGuide: $activationGuide, events: $events, useOkxApi: $useOkxApi)';
  }
}

/// @nodoc
abstract mixin class _$ConfigCopyWith<$Res> implements $ConfigCopyWith<$Res> {
  factory _$ConfigCopyWith(_Config value, $Res Function(_Config) _then) =
      __$ConfigCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'chainTokenFilters')
    Map<String, List<String>> chainTokenSymbolFilters,
    @JsonKey(name: 'chainFilters') List<int> chainFilters,
    @JsonKey(name: 'activationGuide') bool activationGuide,
    @JsonKey(name: 'events') List<EventItem> events,
    @JsonKey(name: 'okx') bool useOkxApi,
  });
}

/// @nodoc
class __$ConfigCopyWithImpl<$Res> implements _$ConfigCopyWith<$Res> {
  __$ConfigCopyWithImpl(this._self, this._then);

  final _Config _self;
  final $Res Function(_Config) _then;

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? chainTokenSymbolFilters = null,
    Object? chainFilters = null,
    Object? activationGuide = null,
    Object? events = null,
    Object? useOkxApi = null,
  }) {
    return _then(
      _Config(
        chainTokenSymbolFilters: null == chainTokenSymbolFilters
            ? _self._chainTokenSymbolFilters
            : chainTokenSymbolFilters // ignore: cast_nullable_to_non_nullable
                  as Map<String, List<String>>,
        chainFilters: null == chainFilters
            ? _self._chainFilters
            : chainFilters // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        activationGuide: null == activationGuide
            ? _self.activationGuide
            : activationGuide // ignore: cast_nullable_to_non_nullable
                  as bool,
        events: null == events
            ? _self._events
            : events // ignore: cast_nullable_to_non_nullable
                  as List<EventItem>,
        useOkxApi: null == useOkxApi
            ? _self.useOkxApi
            : useOkxApi // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}
