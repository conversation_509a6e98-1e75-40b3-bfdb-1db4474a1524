// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'astrox.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AstroxPaged<T> {
  @JsonKey(name: 'pageNum')
  int get page;
  @JsonKey(name: 'pageSize')
  int get size;
  @JsonKey(name: 'totalPage')
  int get pages;
  @JsonKey(name: 'total')
  int get total;
  @JsonKey(name: 'list')
  List<T> get list;

  /// Create a copy of AstroxPaged
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AstroxPagedCopyWith<T, AstroxPaged<T>> get copyWith =>
      _$AstroxPagedCopyWithImpl<T, AstroxPaged<T>>(
        this as AstroxPaged<T>,
        _$identity,
      );

  /// Serializes this AstroxPaged to a JSON map.
  Map<String, dynamic> toJson(Object? Function(T) toJsonT);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AstroxPaged<T> &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other.list, list));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    page,
    size,
    pages,
    total,
    const DeepCollectionEquality().hash(list),
  );

  @override
  String toString() {
    return 'AstroxPaged<$T>(page: $page, size: $size, pages: $pages, total: $total, list: $list)';
  }
}

/// @nodoc
abstract mixin class $AstroxPagedCopyWith<T, $Res> {
  factory $AstroxPagedCopyWith(
    AstroxPaged<T> value,
    $Res Function(AstroxPaged<T>) _then,
  ) = _$AstroxPagedCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'pageNum') int page,
    @JsonKey(name: 'pageSize') int size,
    @JsonKey(name: 'totalPage') int pages,
    @JsonKey(name: 'total') int total,
    @JsonKey(name: 'list') List<T> list,
  });
}

/// @nodoc
class _$AstroxPagedCopyWithImpl<T, $Res>
    implements $AstroxPagedCopyWith<T, $Res> {
  _$AstroxPagedCopyWithImpl(this._self, this._then);

  final AstroxPaged<T> _self;
  final $Res Function(AstroxPaged<T>) _then;

  /// Create a copy of AstroxPaged
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? size = null,
    Object? pages = null,
    Object? total = null,
    Object? list = null,
  }) {
    return _then(
      _self.copyWith(
        page: null == page
            ? _self.page
            : page // ignore: cast_nullable_to_non_nullable
                  as int,
        size: null == size
            ? _self.size
            : size // ignore: cast_nullable_to_non_nullable
                  as int,
        pages: null == pages
            ? _self.pages
            : pages // ignore: cast_nullable_to_non_nullable
                  as int,
        total: null == total
            ? _self.total
            : total // ignore: cast_nullable_to_non_nullable
                  as int,
        list: null == list
            ? _self.list
            : list // ignore: cast_nullable_to_non_nullable
                  as List<T>,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [AstroxPaged].
extension AstroxPagedPatterns<T> on AstroxPaged<T> {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AstroxPaged<T> value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstroxPaged() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AstroxPaged<T> value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxPaged():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AstroxPaged<T> value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxPaged() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'pageNum') int page,
      @JsonKey(name: 'pageSize') int size,
      @JsonKey(name: 'totalPage') int pages,
      @JsonKey(name: 'total') int total,
      @JsonKey(name: 'list') List<T> list,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstroxPaged() when $default != null:
        return $default(
          _that.page,
          _that.size,
          _that.pages,
          _that.total,
          _that.list,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'pageNum') int page,
      @JsonKey(name: 'pageSize') int size,
      @JsonKey(name: 'totalPage') int pages,
      @JsonKey(name: 'total') int total,
      @JsonKey(name: 'list') List<T> list,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxPaged():
        return $default(
          _that.page,
          _that.size,
          _that.pages,
          _that.total,
          _that.list,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'pageNum') int page,
      @JsonKey(name: 'pageSize') int size,
      @JsonKey(name: 'totalPage') int pages,
      @JsonKey(name: 'total') int total,
      @JsonKey(name: 'list') List<T> list,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxPaged() when $default != null:
        return $default(
          _that.page,
          _that.size,
          _that.pages,
          _that.total,
          _that.list,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable(genericArgumentFactories: true)
class _AstroxPaged<T> implements AstroxPaged<T> {
  const _AstroxPaged({
    @JsonKey(name: 'pageNum') required this.page,
    @JsonKey(name: 'pageSize') required this.size,
    @JsonKey(name: 'totalPage') required this.pages,
    @JsonKey(name: 'total') this.total = 0,
    @JsonKey(name: 'list') final List<T> list = const [],
  }) : _list = list;
  factory _AstroxPaged.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) => _$AstroxPagedFromJson(json, fromJsonT);

  @override
  @JsonKey(name: 'pageNum')
  final int page;
  @override
  @JsonKey(name: 'pageSize')
  final int size;
  @override
  @JsonKey(name: 'totalPage')
  final int pages;
  @override
  @JsonKey(name: 'total')
  final int total;
  final List<T> _list;
  @override
  @JsonKey(name: 'list')
  List<T> get list {
    if (_list is EqualUnmodifiableListView) return _list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_list);
  }

  /// Create a copy of AstroxPaged
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AstroxPagedCopyWith<T, _AstroxPaged<T>> get copyWith =>
      __$AstroxPagedCopyWithImpl<T, _AstroxPaged<T>>(this, _$identity);

  @override
  Map<String, dynamic> toJson(Object? Function(T) toJsonT) {
    return _$AstroxPagedToJson<T>(this, toJsonT);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AstroxPaged<T> &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._list, _list));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    page,
    size,
    pages,
    total,
    const DeepCollectionEquality().hash(_list),
  );

  @override
  String toString() {
    return 'AstroxPaged<$T>(page: $page, size: $size, pages: $pages, total: $total, list: $list)';
  }
}

/// @nodoc
abstract mixin class _$AstroxPagedCopyWith<T, $Res>
    implements $AstroxPagedCopyWith<T, $Res> {
  factory _$AstroxPagedCopyWith(
    _AstroxPaged<T> value,
    $Res Function(_AstroxPaged<T>) _then,
  ) = __$AstroxPagedCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'pageNum') int page,
    @JsonKey(name: 'pageSize') int size,
    @JsonKey(name: 'totalPage') int pages,
    @JsonKey(name: 'total') int total,
    @JsonKey(name: 'list') List<T> list,
  });
}

/// @nodoc
class __$AstroxPagedCopyWithImpl<T, $Res>
    implements _$AstroxPagedCopyWith<T, $Res> {
  __$AstroxPagedCopyWithImpl(this._self, this._then);

  final _AstroxPaged<T> _self;
  final $Res Function(_AstroxPaged<T>) _then;

  /// Create a copy of AstroxPaged
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? page = null,
    Object? size = null,
    Object? pages = null,
    Object? total = null,
    Object? list = null,
  }) {
    return _then(
      _AstroxPaged<T>(
        page: null == page
            ? _self.page
            : page // ignore: cast_nullable_to_non_nullable
                  as int,
        size: null == size
            ? _self.size
            : size // ignore: cast_nullable_to_non_nullable
                  as int,
        pages: null == pages
            ? _self.pages
            : pages // ignore: cast_nullable_to_non_nullable
                  as int,
        total: null == total
            ? _self.total
            : total // ignore: cast_nullable_to_non_nullable
                  as int,
        list: null == list
            ? _self._list
            : list // ignore: cast_nullable_to_non_nullable
                  as List<T>,
      ),
    );
  }
}

/// @nodoc
mixin _$AstroxTokenConfig {
  @JsonKey(name: 'blockChain')
  String get blockChain;
  @JsonKey(name: 'contractAddress')
  String get contractAddress;
  @JsonKey(name: 'createTime')
  String get createTime;
  @JsonKey(name: 'desc')
  String get desc;
  @JsonKey(name: 'digits')
  int get digits;
  @JsonKey(name: 'icon')
  String get icon;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'serviceCharge')
  int get serviceCharge;
  @JsonKey(name: 'standard')
  String get standard;
  @JsonKey(name: 'symbol')
  String get symbol;
  @JsonKey(name: 'totalSupply')
  int get totalSupply;
  @JsonKey(name: 'weight')
  int get weight;
  @JsonKey(name: 'coinMarketCapUcid')
  String get coinMarketCapUcid;

  /// Create a copy of AstroxTokenConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AstroxTokenConfigCopyWith<AstroxTokenConfig> get copyWith =>
      _$AstroxTokenConfigCopyWithImpl<AstroxTokenConfig>(
        this as AstroxTokenConfig,
        _$identity,
      );

  /// Serializes this AstroxTokenConfig to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AstroxTokenConfig &&
            (identical(other.blockChain, blockChain) ||
                other.blockChain == blockChain) &&
            (identical(other.contractAddress, contractAddress) ||
                other.contractAddress == contractAddress) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.digits, digits) || other.digits == digits) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.serviceCharge, serviceCharge) ||
                other.serviceCharge == serviceCharge) &&
            (identical(other.standard, standard) ||
                other.standard == standard) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.totalSupply, totalSupply) ||
                other.totalSupply == totalSupply) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.coinMarketCapUcid, coinMarketCapUcid) ||
                other.coinMarketCapUcid == coinMarketCapUcid));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    blockChain,
    contractAddress,
    createTime,
    desc,
    digits,
    icon,
    name,
    serviceCharge,
    standard,
    symbol,
    totalSupply,
    weight,
    coinMarketCapUcid,
  );

  @override
  String toString() {
    return 'AstroxTokenConfig(blockChain: $blockChain, contractAddress: $contractAddress, createTime: $createTime, desc: $desc, digits: $digits, icon: $icon, name: $name, serviceCharge: $serviceCharge, standard: $standard, symbol: $symbol, totalSupply: $totalSupply, weight: $weight, coinMarketCapUcid: $coinMarketCapUcid)';
  }
}

/// @nodoc
abstract mixin class $AstroxTokenConfigCopyWith<$Res> {
  factory $AstroxTokenConfigCopyWith(
    AstroxTokenConfig value,
    $Res Function(AstroxTokenConfig) _then,
  ) = _$AstroxTokenConfigCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'blockChain') String blockChain,
    @JsonKey(name: 'contractAddress') String contractAddress,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'desc') String desc,
    @JsonKey(name: 'digits') int digits,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'serviceCharge') int serviceCharge,
    @JsonKey(name: 'standard') String standard,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'totalSupply') int totalSupply,
    @JsonKey(name: 'weight') int weight,
    @JsonKey(name: 'coinMarketCapUcid') String coinMarketCapUcid,
  });
}

/// @nodoc
class _$AstroxTokenConfigCopyWithImpl<$Res>
    implements $AstroxTokenConfigCopyWith<$Res> {
  _$AstroxTokenConfigCopyWithImpl(this._self, this._then);

  final AstroxTokenConfig _self;
  final $Res Function(AstroxTokenConfig) _then;

  /// Create a copy of AstroxTokenConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? blockChain = null,
    Object? contractAddress = null,
    Object? createTime = null,
    Object? desc = null,
    Object? digits = null,
    Object? icon = null,
    Object? name = null,
    Object? serviceCharge = null,
    Object? standard = null,
    Object? symbol = null,
    Object? totalSupply = null,
    Object? weight = null,
    Object? coinMarketCapUcid = null,
  }) {
    return _then(
      _self.copyWith(
        blockChain: null == blockChain
            ? _self.blockChain
            : blockChain // ignore: cast_nullable_to_non_nullable
                  as String,
        contractAddress: null == contractAddress
            ? _self.contractAddress
            : contractAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        desc: null == desc
            ? _self.desc
            : desc // ignore: cast_nullable_to_non_nullable
                  as String,
        digits: null == digits
            ? _self.digits
            : digits // ignore: cast_nullable_to_non_nullable
                  as int,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        serviceCharge: null == serviceCharge
            ? _self.serviceCharge
            : serviceCharge // ignore: cast_nullable_to_non_nullable
                  as int,
        standard: null == standard
            ? _self.standard
            : standard // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        totalSupply: null == totalSupply
            ? _self.totalSupply
            : totalSupply // ignore: cast_nullable_to_non_nullable
                  as int,
        weight: null == weight
            ? _self.weight
            : weight // ignore: cast_nullable_to_non_nullable
                  as int,
        coinMarketCapUcid: null == coinMarketCapUcid
            ? _self.coinMarketCapUcid
            : coinMarketCapUcid // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [AstroxTokenConfig].
extension AstroxTokenConfigPatterns on AstroxTokenConfig {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AstroxTokenConfig value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenConfig() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AstroxTokenConfig value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenConfig():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AstroxTokenConfig value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenConfig() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'blockChain') String blockChain,
      @JsonKey(name: 'contractAddress') String contractAddress,
      @JsonKey(name: 'createTime') String createTime,
      @JsonKey(name: 'desc') String desc,
      @JsonKey(name: 'digits') int digits,
      @JsonKey(name: 'icon') String icon,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'serviceCharge') int serviceCharge,
      @JsonKey(name: 'standard') String standard,
      @JsonKey(name: 'symbol') String symbol,
      @JsonKey(name: 'totalSupply') int totalSupply,
      @JsonKey(name: 'weight') int weight,
      @JsonKey(name: 'coinMarketCapUcid') String coinMarketCapUcid,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenConfig() when $default != null:
        return $default(
          _that.blockChain,
          _that.contractAddress,
          _that.createTime,
          _that.desc,
          _that.digits,
          _that.icon,
          _that.name,
          _that.serviceCharge,
          _that.standard,
          _that.symbol,
          _that.totalSupply,
          _that.weight,
          _that.coinMarketCapUcid,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'blockChain') String blockChain,
      @JsonKey(name: 'contractAddress') String contractAddress,
      @JsonKey(name: 'createTime') String createTime,
      @JsonKey(name: 'desc') String desc,
      @JsonKey(name: 'digits') int digits,
      @JsonKey(name: 'icon') String icon,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'serviceCharge') int serviceCharge,
      @JsonKey(name: 'standard') String standard,
      @JsonKey(name: 'symbol') String symbol,
      @JsonKey(name: 'totalSupply') int totalSupply,
      @JsonKey(name: 'weight') int weight,
      @JsonKey(name: 'coinMarketCapUcid') String coinMarketCapUcid,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenConfig():
        return $default(
          _that.blockChain,
          _that.contractAddress,
          _that.createTime,
          _that.desc,
          _that.digits,
          _that.icon,
          _that.name,
          _that.serviceCharge,
          _that.standard,
          _that.symbol,
          _that.totalSupply,
          _that.weight,
          _that.coinMarketCapUcid,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'blockChain') String blockChain,
      @JsonKey(name: 'contractAddress') String contractAddress,
      @JsonKey(name: 'createTime') String createTime,
      @JsonKey(name: 'desc') String desc,
      @JsonKey(name: 'digits') int digits,
      @JsonKey(name: 'icon') String icon,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'serviceCharge') int serviceCharge,
      @JsonKey(name: 'standard') String standard,
      @JsonKey(name: 'symbol') String symbol,
      @JsonKey(name: 'totalSupply') int totalSupply,
      @JsonKey(name: 'weight') int weight,
      @JsonKey(name: 'coinMarketCapUcid') String coinMarketCapUcid,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenConfig() when $default != null:
        return $default(
          _that.blockChain,
          _that.contractAddress,
          _that.createTime,
          _that.desc,
          _that.digits,
          _that.icon,
          _that.name,
          _that.serviceCharge,
          _that.standard,
          _that.symbol,
          _that.totalSupply,
          _that.weight,
          _that.coinMarketCapUcid,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AstroxTokenConfig implements AstroxTokenConfig {
  const _AstroxTokenConfig({
    @JsonKey(name: 'blockChain') required this.blockChain,
    @JsonKey(name: 'contractAddress') required this.contractAddress,
    @JsonKey(name: 'createTime') required this.createTime,
    @JsonKey(name: 'desc') this.desc = '',
    @JsonKey(name: 'digits') this.digits = 0,
    @JsonKey(name: 'icon') this.icon = '',
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'serviceCharge') this.serviceCharge = 0,
    @JsonKey(name: 'standard') required this.standard,
    @JsonKey(name: 'symbol') required this.symbol,
    @JsonKey(name: 'totalSupply') this.totalSupply = 0,
    @JsonKey(name: 'weight') this.weight = 0,
    @JsonKey(name: 'coinMarketCapUcid') this.coinMarketCapUcid = '',
  });
  factory _AstroxTokenConfig.fromJson(Map<String, dynamic> json) =>
      _$AstroxTokenConfigFromJson(json);

  @override
  @JsonKey(name: 'blockChain')
  final String blockChain;
  @override
  @JsonKey(name: 'contractAddress')
  final String contractAddress;
  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'desc')
  final String desc;
  @override
  @JsonKey(name: 'digits')
  final int digits;
  @override
  @JsonKey(name: 'icon')
  final String icon;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'serviceCharge')
  final int serviceCharge;
  @override
  @JsonKey(name: 'standard')
  final String standard;
  @override
  @JsonKey(name: 'symbol')
  final String symbol;
  @override
  @JsonKey(name: 'totalSupply')
  final int totalSupply;
  @override
  @JsonKey(name: 'weight')
  final int weight;
  @override
  @JsonKey(name: 'coinMarketCapUcid')
  final String coinMarketCapUcid;

  /// Create a copy of AstroxTokenConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AstroxTokenConfigCopyWith<_AstroxTokenConfig> get copyWith =>
      __$AstroxTokenConfigCopyWithImpl<_AstroxTokenConfig>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AstroxTokenConfigToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AstroxTokenConfig &&
            (identical(other.blockChain, blockChain) ||
                other.blockChain == blockChain) &&
            (identical(other.contractAddress, contractAddress) ||
                other.contractAddress == contractAddress) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.digits, digits) || other.digits == digits) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.serviceCharge, serviceCharge) ||
                other.serviceCharge == serviceCharge) &&
            (identical(other.standard, standard) ||
                other.standard == standard) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.totalSupply, totalSupply) ||
                other.totalSupply == totalSupply) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.coinMarketCapUcid, coinMarketCapUcid) ||
                other.coinMarketCapUcid == coinMarketCapUcid));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    blockChain,
    contractAddress,
    createTime,
    desc,
    digits,
    icon,
    name,
    serviceCharge,
    standard,
    symbol,
    totalSupply,
    weight,
    coinMarketCapUcid,
  );

  @override
  String toString() {
    return 'AstroxTokenConfig(blockChain: $blockChain, contractAddress: $contractAddress, createTime: $createTime, desc: $desc, digits: $digits, icon: $icon, name: $name, serviceCharge: $serviceCharge, standard: $standard, symbol: $symbol, totalSupply: $totalSupply, weight: $weight, coinMarketCapUcid: $coinMarketCapUcid)';
  }
}

/// @nodoc
abstract mixin class _$AstroxTokenConfigCopyWith<$Res>
    implements $AstroxTokenConfigCopyWith<$Res> {
  factory _$AstroxTokenConfigCopyWith(
    _AstroxTokenConfig value,
    $Res Function(_AstroxTokenConfig) _then,
  ) = __$AstroxTokenConfigCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'blockChain') String blockChain,
    @JsonKey(name: 'contractAddress') String contractAddress,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'desc') String desc,
    @JsonKey(name: 'digits') int digits,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'serviceCharge') int serviceCharge,
    @JsonKey(name: 'standard') String standard,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'totalSupply') int totalSupply,
    @JsonKey(name: 'weight') int weight,
    @JsonKey(name: 'coinMarketCapUcid') String coinMarketCapUcid,
  });
}

/// @nodoc
class __$AstroxTokenConfigCopyWithImpl<$Res>
    implements _$AstroxTokenConfigCopyWith<$Res> {
  __$AstroxTokenConfigCopyWithImpl(this._self, this._then);

  final _AstroxTokenConfig _self;
  final $Res Function(_AstroxTokenConfig) _then;

  /// Create a copy of AstroxTokenConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? blockChain = null,
    Object? contractAddress = null,
    Object? createTime = null,
    Object? desc = null,
    Object? digits = null,
    Object? icon = null,
    Object? name = null,
    Object? serviceCharge = null,
    Object? standard = null,
    Object? symbol = null,
    Object? totalSupply = null,
    Object? weight = null,
    Object? coinMarketCapUcid = null,
  }) {
    return _then(
      _AstroxTokenConfig(
        blockChain: null == blockChain
            ? _self.blockChain
            : blockChain // ignore: cast_nullable_to_non_nullable
                  as String,
        contractAddress: null == contractAddress
            ? _self.contractAddress
            : contractAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        desc: null == desc
            ? _self.desc
            : desc // ignore: cast_nullable_to_non_nullable
                  as String,
        digits: null == digits
            ? _self.digits
            : digits // ignore: cast_nullable_to_non_nullable
                  as int,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        serviceCharge: null == serviceCharge
            ? _self.serviceCharge
            : serviceCharge // ignore: cast_nullable_to_non_nullable
                  as int,
        standard: null == standard
            ? _self.standard
            : standard // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        totalSupply: null == totalSupply
            ? _self.totalSupply
            : totalSupply // ignore: cast_nullable_to_non_nullable
                  as int,
        weight: null == weight
            ? _self.weight
            : weight // ignore: cast_nullable_to_non_nullable
                  as int,
        coinMarketCapUcid: null == coinMarketCapUcid
            ? _self.coinMarketCapUcid
            : coinMarketCapUcid // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$AstroxTokenBalance {
  AstroxTokenConfig get token;
  Decimal get balance;
  Decimal? get price;

  /// Create a copy of AstroxTokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AstroxTokenBalanceCopyWith<AstroxTokenBalance> get copyWith =>
      _$AstroxTokenBalanceCopyWithImpl<AstroxTokenBalance>(
        this as AstroxTokenBalance,
        _$identity,
      );

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AstroxTokenBalance &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.price, price) || other.price == price));
  }

  @override
  int get hashCode => Object.hash(runtimeType, token, balance, price);

  @override
  String toString() {
    return 'AstroxTokenBalance(token: $token, balance: $balance, price: $price)';
  }
}

/// @nodoc
abstract mixin class $AstroxTokenBalanceCopyWith<$Res> {
  factory $AstroxTokenBalanceCopyWith(
    AstroxTokenBalance value,
    $Res Function(AstroxTokenBalance) _then,
  ) = _$AstroxTokenBalanceCopyWithImpl;
  @useResult
  $Res call({AstroxTokenConfig token, Decimal balance, Decimal? price});

  $AstroxTokenConfigCopyWith<$Res> get token;
}

/// @nodoc
class _$AstroxTokenBalanceCopyWithImpl<$Res>
    implements $AstroxTokenBalanceCopyWith<$Res> {
  _$AstroxTokenBalanceCopyWithImpl(this._self, this._then);

  final AstroxTokenBalance _self;
  final $Res Function(AstroxTokenBalance) _then;

  /// Create a copy of AstroxTokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
    Object? balance = null,
    Object? price = freezed,
  }) {
    return _then(
      _self.copyWith(
        token: null == token
            ? _self.token
            : token // ignore: cast_nullable_to_non_nullable
                  as AstroxTokenConfig,
        balance: null == balance
            ? _self.balance
            : balance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        price: freezed == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as Decimal?,
      ),
    );
  }

  /// Create a copy of AstroxTokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AstroxTokenConfigCopyWith<$Res> get token {
    return $AstroxTokenConfigCopyWith<$Res>(_self.token, (value) {
      return _then(_self.copyWith(token: value));
    });
  }
}

/// Adds pattern-matching-related methods to [AstroxTokenBalance].
extension AstroxTokenBalancePatterns on AstroxTokenBalance {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AstroxTokenBalance value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenBalance() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AstroxTokenBalance value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenBalance():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AstroxTokenBalance value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenBalance() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(AstroxTokenConfig token, Decimal balance, Decimal? price)?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenBalance() when $default != null:
        return $default(_that.token, _that.balance, _that.price);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(AstroxTokenConfig token, Decimal balance, Decimal? price)
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenBalance():
        return $default(_that.token, _that.balance, _that.price);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(AstroxTokenConfig token, Decimal balance, Decimal? price)?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxTokenBalance() when $default != null:
        return $default(_that.token, _that.balance, _that.price);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _AstroxTokenBalance implements AstroxTokenBalance {
  const _AstroxTokenBalance({
    required this.token,
    required this.balance,
    required this.price,
  });

  @override
  final AstroxTokenConfig token;
  @override
  final Decimal balance;
  @override
  final Decimal? price;

  /// Create a copy of AstroxTokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AstroxTokenBalanceCopyWith<_AstroxTokenBalance> get copyWith =>
      __$AstroxTokenBalanceCopyWithImpl<_AstroxTokenBalance>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AstroxTokenBalance &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.price, price) || other.price == price));
  }

  @override
  int get hashCode => Object.hash(runtimeType, token, balance, price);

  @override
  String toString() {
    return 'AstroxTokenBalance(token: $token, balance: $balance, price: $price)';
  }
}

/// @nodoc
abstract mixin class _$AstroxTokenBalanceCopyWith<$Res>
    implements $AstroxTokenBalanceCopyWith<$Res> {
  factory _$AstroxTokenBalanceCopyWith(
    _AstroxTokenBalance value,
    $Res Function(_AstroxTokenBalance) _then,
  ) = __$AstroxTokenBalanceCopyWithImpl;
  @override
  @useResult
  $Res call({AstroxTokenConfig token, Decimal balance, Decimal? price});

  @override
  $AstroxTokenConfigCopyWith<$Res> get token;
}

/// @nodoc
class __$AstroxTokenBalanceCopyWithImpl<$Res>
    implements _$AstroxTokenBalanceCopyWith<$Res> {
  __$AstroxTokenBalanceCopyWithImpl(this._self, this._then);

  final _AstroxTokenBalance _self;
  final $Res Function(_AstroxTokenBalance) _then;

  /// Create a copy of AstroxTokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? token = null,
    Object? balance = null,
    Object? price = freezed,
  }) {
    return _then(
      _AstroxTokenBalance(
        token: null == token
            ? _self.token
            : token // ignore: cast_nullable_to_non_nullable
                  as AstroxTokenConfig,
        balance: null == balance
            ? _self.balance
            : balance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        price: freezed == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as Decimal?,
      ),
    );
  }

  /// Create a copy of AstroxTokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AstroxTokenConfigCopyWith<$Res> get token {
    return $AstroxTokenConfigCopyWith<$Res>(_self.token, (value) {
      return _then(_self.copyWith(token: value));
    });
  }
}

/// @nodoc
mixin _$AstroxWalletSummary {
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'totalValueUsd')
  Decimal get totalValueUsd;
  @JsonKey(name: 'hasEmptyValue')
  bool get hasEmptyValue;

  /// Create a copy of AstroxWalletSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AstroxWalletSummaryCopyWith<AstroxWalletSummary> get copyWith =>
      _$AstroxWalletSummaryCopyWithImpl<AstroxWalletSummary>(
        this as AstroxWalletSummary,
        _$identity,
      );

  /// Serializes this AstroxWalletSummary to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AstroxWalletSummary &&
            (identical(other.totalValueUsd, totalValueUsd) ||
                other.totalValueUsd == totalValueUsd) &&
            (identical(other.hasEmptyValue, hasEmptyValue) ||
                other.hasEmptyValue == hasEmptyValue));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalValueUsd, hasEmptyValue);

  @override
  String toString() {
    return 'AstroxWalletSummary(totalValueUsd: $totalValueUsd, hasEmptyValue: $hasEmptyValue)';
  }
}

/// @nodoc
abstract mixin class $AstroxWalletSummaryCopyWith<$Res> {
  factory $AstroxWalletSummaryCopyWith(
    AstroxWalletSummary value,
    $Res Function(AstroxWalletSummary) _then,
  ) = _$AstroxWalletSummaryCopyWithImpl;
  @useResult
  $Res call({
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'totalValueUsd')
    Decimal totalValueUsd,
    @JsonKey(name: 'hasEmptyValue') bool hasEmptyValue,
  });
}

/// @nodoc
class _$AstroxWalletSummaryCopyWithImpl<$Res>
    implements $AstroxWalletSummaryCopyWith<$Res> {
  _$AstroxWalletSummaryCopyWithImpl(this._self, this._then);

  final AstroxWalletSummary _self;
  final $Res Function(AstroxWalletSummary) _then;

  /// Create a copy of AstroxWalletSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? totalValueUsd = null, Object? hasEmptyValue = null}) {
    return _then(
      _self.copyWith(
        totalValueUsd: null == totalValueUsd
            ? _self.totalValueUsd
            : totalValueUsd // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        hasEmptyValue: null == hasEmptyValue
            ? _self.hasEmptyValue
            : hasEmptyValue // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [AstroxWalletSummary].
extension AstroxWalletSummaryPatterns on AstroxWalletSummary {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AstroxWalletSummary value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstroxWalletSummary() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AstroxWalletSummary value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxWalletSummary():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AstroxWalletSummary value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxWalletSummary() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @NumberDecimalRequiredConverter()
      @JsonKey(name: 'totalValueUsd')
      Decimal totalValueUsd,
      @JsonKey(name: 'hasEmptyValue') bool hasEmptyValue,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstroxWalletSummary() when $default != null:
        return $default(_that.totalValueUsd, _that.hasEmptyValue);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @NumberDecimalRequiredConverter()
      @JsonKey(name: 'totalValueUsd')
      Decimal totalValueUsd,
      @JsonKey(name: 'hasEmptyValue') bool hasEmptyValue,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxWalletSummary():
        return $default(_that.totalValueUsd, _that.hasEmptyValue);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @NumberDecimalRequiredConverter()
      @JsonKey(name: 'totalValueUsd')
      Decimal totalValueUsd,
      @JsonKey(name: 'hasEmptyValue') bool hasEmptyValue,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxWalletSummary() when $default != null:
        return $default(_that.totalValueUsd, _that.hasEmptyValue);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AstroxWalletSummary implements AstroxWalletSummary {
  const _AstroxWalletSummary({
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'totalValueUsd')
    required this.totalValueUsd,
    @JsonKey(name: 'hasEmptyValue') this.hasEmptyValue = false,
  });
  factory _AstroxWalletSummary.fromJson(Map<String, dynamic> json) =>
      _$AstroxWalletSummaryFromJson(json);

  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'totalValueUsd')
  final Decimal totalValueUsd;
  @override
  @JsonKey(name: 'hasEmptyValue')
  final bool hasEmptyValue;

  /// Create a copy of AstroxWalletSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AstroxWalletSummaryCopyWith<_AstroxWalletSummary> get copyWith =>
      __$AstroxWalletSummaryCopyWithImpl<_AstroxWalletSummary>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$AstroxWalletSummaryToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AstroxWalletSummary &&
            (identical(other.totalValueUsd, totalValueUsd) ||
                other.totalValueUsd == totalValueUsd) &&
            (identical(other.hasEmptyValue, hasEmptyValue) ||
                other.hasEmptyValue == hasEmptyValue));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalValueUsd, hasEmptyValue);

  @override
  String toString() {
    return 'AstroxWalletSummary(totalValueUsd: $totalValueUsd, hasEmptyValue: $hasEmptyValue)';
  }
}

/// @nodoc
abstract mixin class _$AstroxWalletSummaryCopyWith<$Res>
    implements $AstroxWalletSummaryCopyWith<$Res> {
  factory _$AstroxWalletSummaryCopyWith(
    _AstroxWalletSummary value,
    $Res Function(_AstroxWalletSummary) _then,
  ) = __$AstroxWalletSummaryCopyWithImpl;
  @override
  @useResult
  $Res call({
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'totalValueUsd')
    Decimal totalValueUsd,
    @JsonKey(name: 'hasEmptyValue') bool hasEmptyValue,
  });
}

/// @nodoc
class __$AstroxWalletSummaryCopyWithImpl<$Res>
    implements _$AstroxWalletSummaryCopyWith<$Res> {
  __$AstroxWalletSummaryCopyWithImpl(this._self, this._then);

  final _AstroxWalletSummary _self;
  final $Res Function(_AstroxWalletSummary) _then;

  /// Create a copy of AstroxWalletSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? totalValueUsd = null, Object? hasEmptyValue = null}) {
    return _then(
      _AstroxWalletSummary(
        totalValueUsd: null == totalValueUsd
            ? _self.totalValueUsd
            : totalValueUsd // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        hasEmptyValue: null == hasEmptyValue
            ? _self.hasEmptyValue
            : hasEmptyValue // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
mixin _$AstroxToken {
  @JsonKey(name: 'blockChain')
  String get chainName;
  @JsonKey(name: 'symbol')
  String get symbol;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'logo')
  String get logo;
  @JsonKey(name: 'address')
  String get address;
  @JsonKey(name: 'decimals')
  int get decimals;
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'balance')
  Decimal get balance;
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'realBalance')
  Decimal get realBalance;
  @NumberDecimalConverter()
  @JsonKey(name: 'priceUsd')
  Decimal? get priceUsd;
  @NumberDecimalConverter()
  @JsonKey(name: 'valueUsd')
  Decimal? get valueUsd;

  /// Create a copy of AstroxToken
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AstroxTokenCopyWith<AstroxToken> get copyWith =>
      _$AstroxTokenCopyWithImpl<AstroxToken>(this as AstroxToken, _$identity);

  /// Serializes this AstroxToken to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AstroxToken &&
            (identical(other.chainName, chainName) ||
                other.chainName == chainName) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.decimals, decimals) ||
                other.decimals == decimals) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.realBalance, realBalance) ||
                other.realBalance == realBalance) &&
            (identical(other.priceUsd, priceUsd) ||
                other.priceUsd == priceUsd) &&
            (identical(other.valueUsd, valueUsd) ||
                other.valueUsd == valueUsd));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    chainName,
    symbol,
    name,
    logo,
    address,
    decimals,
    balance,
    realBalance,
    priceUsd,
    valueUsd,
  );

  @override
  String toString() {
    return 'AstroxToken(chainName: $chainName, symbol: $symbol, name: $name, logo: $logo, address: $address, decimals: $decimals, balance: $balance, realBalance: $realBalance, priceUsd: $priceUsd, valueUsd: $valueUsd)';
  }
}

/// @nodoc
abstract mixin class $AstroxTokenCopyWith<$Res> {
  factory $AstroxTokenCopyWith(
    AstroxToken value,
    $Res Function(AstroxToken) _then,
  ) = _$AstroxTokenCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'blockChain') String chainName,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'logo') String logo,
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'decimals') int decimals,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'balance') Decimal balance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'realBalance')
    Decimal realBalance,
    @NumberDecimalConverter() @JsonKey(name: 'priceUsd') Decimal? priceUsd,
    @NumberDecimalConverter() @JsonKey(name: 'valueUsd') Decimal? valueUsd,
  });
}

/// @nodoc
class _$AstroxTokenCopyWithImpl<$Res> implements $AstroxTokenCopyWith<$Res> {
  _$AstroxTokenCopyWithImpl(this._self, this._then);

  final AstroxToken _self;
  final $Res Function(AstroxToken) _then;

  /// Create a copy of AstroxToken
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chainName = null,
    Object? symbol = null,
    Object? name = null,
    Object? logo = null,
    Object? address = null,
    Object? decimals = null,
    Object? balance = null,
    Object? realBalance = null,
    Object? priceUsd = freezed,
    Object? valueUsd = freezed,
  }) {
    return _then(
      _self.copyWith(
        chainName: null == chainName
            ? _self.chainName
            : chainName // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        decimals: null == decimals
            ? _self.decimals
            : decimals // ignore: cast_nullable_to_non_nullable
                  as int,
        balance: null == balance
            ? _self.balance
            : balance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        realBalance: null == realBalance
            ? _self.realBalance
            : realBalance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        priceUsd: freezed == priceUsd
            ? _self.priceUsd
            : priceUsd // ignore: cast_nullable_to_non_nullable
                  as Decimal?,
        valueUsd: freezed == valueUsd
            ? _self.valueUsd
            : valueUsd // ignore: cast_nullable_to_non_nullable
                  as Decimal?,
      ),
    );
  }
}

/// Adds pattern-matching-related methods to [AstroxToken].
extension AstroxTokenPatterns on AstroxToken {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AstroxToken value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstroxToken() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AstroxToken value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxToken():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AstroxToken value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxToken() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'blockChain') String chainName,
      @JsonKey(name: 'symbol') String symbol,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'logo') String logo,
      @JsonKey(name: 'address') String address,
      @JsonKey(name: 'decimals') int decimals,
      @NumberDecimalRequiredConverter()
      @JsonKey(name: 'balance')
      Decimal balance,
      @NumberDecimalRequiredConverter()
      @JsonKey(name: 'realBalance')
      Decimal realBalance,
      @NumberDecimalConverter() @JsonKey(name: 'priceUsd') Decimal? priceUsd,
      @NumberDecimalConverter() @JsonKey(name: 'valueUsd') Decimal? valueUsd,
    )?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstroxToken() when $default != null:
        return $default(
          _that.chainName,
          _that.symbol,
          _that.name,
          _that.logo,
          _that.address,
          _that.decimals,
          _that.balance,
          _that.realBalance,
          _that.priceUsd,
          _that.valueUsd,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
      @JsonKey(name: 'blockChain') String chainName,
      @JsonKey(name: 'symbol') String symbol,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'logo') String logo,
      @JsonKey(name: 'address') String address,
      @JsonKey(name: 'decimals') int decimals,
      @NumberDecimalRequiredConverter()
      @JsonKey(name: 'balance')
      Decimal balance,
      @NumberDecimalRequiredConverter()
      @JsonKey(name: 'realBalance')
      Decimal realBalance,
      @NumberDecimalConverter() @JsonKey(name: 'priceUsd') Decimal? priceUsd,
      @NumberDecimalConverter() @JsonKey(name: 'valueUsd') Decimal? valueUsd,
    )
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxToken():
        return $default(
          _that.chainName,
          _that.symbol,
          _that.name,
          _that.logo,
          _that.address,
          _that.decimals,
          _that.balance,
          _that.realBalance,
          _that.priceUsd,
          _that.valueUsd,
        );
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
      @JsonKey(name: 'blockChain') String chainName,
      @JsonKey(name: 'symbol') String symbol,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'logo') String logo,
      @JsonKey(name: 'address') String address,
      @JsonKey(name: 'decimals') int decimals,
      @NumberDecimalRequiredConverter()
      @JsonKey(name: 'balance')
      Decimal balance,
      @NumberDecimalRequiredConverter()
      @JsonKey(name: 'realBalance')
      Decimal realBalance,
      @NumberDecimalConverter() @JsonKey(name: 'priceUsd') Decimal? priceUsd,
      @NumberDecimalConverter() @JsonKey(name: 'valueUsd') Decimal? valueUsd,
    )?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstroxToken() when $default != null:
        return $default(
          _that.chainName,
          _that.symbol,
          _that.name,
          _that.logo,
          _that.address,
          _that.decimals,
          _that.balance,
          _that.realBalance,
          _that.priceUsd,
          _that.valueUsd,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AstroxToken extends AstroxToken implements IToken {
  const _AstroxToken({
    @JsonKey(name: 'blockChain') required this.chainName,
    @JsonKey(name: 'symbol') required this.symbol,
    @JsonKey(name: 'name') required this.name,
    @JsonKey(name: 'logo') required this.logo,
    @JsonKey(name: 'address') required this.address,
    @JsonKey(name: 'decimals') required this.decimals,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'balance')
    required this.balance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'realBalance')
    required this.realBalance,
    @NumberDecimalConverter() @JsonKey(name: 'priceUsd') required this.priceUsd,
    @NumberDecimalConverter() @JsonKey(name: 'valueUsd') required this.valueUsd,
  }) : super._();
  factory _AstroxToken.fromJson(Map<String, dynamic> json) =>
      _$AstroxTokenFromJson(json);

  @override
  @JsonKey(name: 'blockChain')
  final String chainName;
  @override
  @JsonKey(name: 'symbol')
  final String symbol;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'logo')
  final String logo;
  @override
  @JsonKey(name: 'address')
  final String address;
  @override
  @JsonKey(name: 'decimals')
  final int decimals;
  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'balance')
  final Decimal balance;
  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'realBalance')
  final Decimal realBalance;
  @override
  @NumberDecimalConverter()
  @JsonKey(name: 'priceUsd')
  final Decimal? priceUsd;
  @override
  @NumberDecimalConverter()
  @JsonKey(name: 'valueUsd')
  final Decimal? valueUsd;

  /// Create a copy of AstroxToken
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AstroxTokenCopyWith<_AstroxToken> get copyWith =>
      __$AstroxTokenCopyWithImpl<_AstroxToken>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AstroxTokenToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AstroxToken &&
            (identical(other.chainName, chainName) ||
                other.chainName == chainName) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.decimals, decimals) ||
                other.decimals == decimals) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.realBalance, realBalance) ||
                other.realBalance == realBalance) &&
            (identical(other.priceUsd, priceUsd) ||
                other.priceUsd == priceUsd) &&
            (identical(other.valueUsd, valueUsd) ||
                other.valueUsd == valueUsd));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    chainName,
    symbol,
    name,
    logo,
    address,
    decimals,
    balance,
    realBalance,
    priceUsd,
    valueUsd,
  );

  @override
  String toString() {
    return 'AstroxToken(chainName: $chainName, symbol: $symbol, name: $name, logo: $logo, address: $address, decimals: $decimals, balance: $balance, realBalance: $realBalance, priceUsd: $priceUsd, valueUsd: $valueUsd)';
  }
}

/// @nodoc
abstract mixin class _$AstroxTokenCopyWith<$Res>
    implements $AstroxTokenCopyWith<$Res> {
  factory _$AstroxTokenCopyWith(
    _AstroxToken value,
    $Res Function(_AstroxToken) _then,
  ) = __$AstroxTokenCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'blockChain') String chainName,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'logo') String logo,
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'decimals') int decimals,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'balance') Decimal balance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'realBalance')
    Decimal realBalance,
    @NumberDecimalConverter() @JsonKey(name: 'priceUsd') Decimal? priceUsd,
    @NumberDecimalConverter() @JsonKey(name: 'valueUsd') Decimal? valueUsd,
  });
}

/// @nodoc
class __$AstroxTokenCopyWithImpl<$Res> implements _$AstroxTokenCopyWith<$Res> {
  __$AstroxTokenCopyWithImpl(this._self, this._then);

  final _AstroxToken _self;
  final $Res Function(_AstroxToken) _then;

  /// Create a copy of AstroxToken
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? chainName = null,
    Object? symbol = null,
    Object? name = null,
    Object? logo = null,
    Object? address = null,
    Object? decimals = null,
    Object? balance = null,
    Object? realBalance = null,
    Object? priceUsd = freezed,
    Object? valueUsd = freezed,
  }) {
    return _then(
      _AstroxToken(
        chainName: null == chainName
            ? _self.chainName
            : chainName // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        decimals: null == decimals
            ? _self.decimals
            : decimals // ignore: cast_nullable_to_non_nullable
                  as int,
        balance: null == balance
            ? _self.balance
            : balance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        realBalance: null == realBalance
            ? _self.realBalance
            : realBalance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        priceUsd: freezed == priceUsd
            ? _self.priceUsd
            : priceUsd // ignore: cast_nullable_to_non_nullable
                  as Decimal?,
        valueUsd: freezed == valueUsd
            ? _self.valueUsd
            : valueUsd // ignore: cast_nullable_to_non_nullable
                  as Decimal?,
      ),
    );
  }
}

/// @nodoc
mixin _$WalletPortfolioAstrox {
  AstroxWalletSummary get summary;
  List<AstroxToken> get tokens;

  /// Create a copy of WalletPortfolioAstrox
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WalletPortfolioAstroxCopyWith<WalletPortfolioAstrox> get copyWith =>
      _$WalletPortfolioAstroxCopyWithImpl<WalletPortfolioAstrox>(
        this as WalletPortfolioAstrox,
        _$identity,
      );

  /// Serializes this WalletPortfolioAstrox to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WalletPortfolioAstrox &&
            (identical(other.summary, summary) || other.summary == summary) &&
            const DeepCollectionEquality().equals(other.tokens, tokens));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    summary,
    const DeepCollectionEquality().hash(tokens),
  );

  @override
  String toString() {
    return 'WalletPortfolioAstrox(summary: $summary, tokens: $tokens)';
  }
}

/// @nodoc
abstract mixin class $WalletPortfolioAstroxCopyWith<$Res> {
  factory $WalletPortfolioAstroxCopyWith(
    WalletPortfolioAstrox value,
    $Res Function(WalletPortfolioAstrox) _then,
  ) = _$WalletPortfolioAstroxCopyWithImpl;
  @useResult
  $Res call({AstroxWalletSummary summary, List<AstroxToken> tokens});

  $AstroxWalletSummaryCopyWith<$Res> get summary;
}

/// @nodoc
class _$WalletPortfolioAstroxCopyWithImpl<$Res>
    implements $WalletPortfolioAstroxCopyWith<$Res> {
  _$WalletPortfolioAstroxCopyWithImpl(this._self, this._then);

  final WalletPortfolioAstrox _self;
  final $Res Function(WalletPortfolioAstrox) _then;

  /// Create a copy of WalletPortfolioAstrox
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? summary = null, Object? tokens = null}) {
    return _then(
      _self.copyWith(
        summary: null == summary
            ? _self.summary
            : summary // ignore: cast_nullable_to_non_nullable
                  as AstroxWalletSummary,
        tokens: null == tokens
            ? _self.tokens
            : tokens // ignore: cast_nullable_to_non_nullable
                  as List<AstroxToken>,
      ),
    );
  }

  /// Create a copy of WalletPortfolioAstrox
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AstroxWalletSummaryCopyWith<$Res> get summary {
    return $AstroxWalletSummaryCopyWith<$Res>(_self.summary, (value) {
      return _then(_self.copyWith(summary: value));
    });
  }
}

/// Adds pattern-matching-related methods to [WalletPortfolioAstrox].
extension WalletPortfolioAstroxPatterns on WalletPortfolioAstrox {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_WalletPortfolioAstrox value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WalletPortfolioAstrox() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_WalletPortfolioAstrox value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WalletPortfolioAstrox():
        return $default(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_WalletPortfolioAstrox value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WalletPortfolioAstrox() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(AstroxWalletSummary summary, List<AstroxToken> tokens)?
    $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WalletPortfolioAstrox() when $default != null:
        return $default(_that.summary, _that.tokens);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(AstroxWalletSummary summary, List<AstroxToken> tokens)
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WalletPortfolioAstrox():
        return $default(_that.summary, _that.tokens);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(AstroxWalletSummary summary, List<AstroxToken> tokens)?
    $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WalletPortfolioAstrox() when $default != null:
        return $default(_that.summary, _that.tokens);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _WalletPortfolioAstrox extends WalletPortfolioAstrox
    implements WalletPortfolio<AstroxToken> {
  const _WalletPortfolioAstrox({
    required this.summary,
    required final List<AstroxToken> tokens,
  }) : _tokens = tokens,
       super._();
  factory _WalletPortfolioAstrox.fromJson(Map<String, dynamic> json) =>
      _$WalletPortfolioAstroxFromJson(json);

  @override
  final AstroxWalletSummary summary;
  final List<AstroxToken> _tokens;
  @override
  List<AstroxToken> get tokens {
    if (_tokens is EqualUnmodifiableListView) return _tokens;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tokens);
  }

  /// Create a copy of WalletPortfolioAstrox
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WalletPortfolioAstroxCopyWith<_WalletPortfolioAstrox> get copyWith =>
      __$WalletPortfolioAstroxCopyWithImpl<_WalletPortfolioAstrox>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$WalletPortfolioAstroxToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WalletPortfolioAstrox &&
            (identical(other.summary, summary) || other.summary == summary) &&
            const DeepCollectionEquality().equals(other._tokens, _tokens));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    summary,
    const DeepCollectionEquality().hash(_tokens),
  );

  @override
  String toString() {
    return 'WalletPortfolioAstrox(summary: $summary, tokens: $tokens)';
  }
}

/// @nodoc
abstract mixin class _$WalletPortfolioAstroxCopyWith<$Res>
    implements $WalletPortfolioAstroxCopyWith<$Res> {
  factory _$WalletPortfolioAstroxCopyWith(
    _WalletPortfolioAstrox value,
    $Res Function(_WalletPortfolioAstrox) _then,
  ) = __$WalletPortfolioAstroxCopyWithImpl;
  @override
  @useResult
  $Res call({AstroxWalletSummary summary, List<AstroxToken> tokens});

  @override
  $AstroxWalletSummaryCopyWith<$Res> get summary;
}

/// @nodoc
class __$WalletPortfolioAstroxCopyWithImpl<$Res>
    implements _$WalletPortfolioAstroxCopyWith<$Res> {
  __$WalletPortfolioAstroxCopyWithImpl(this._self, this._then);

  final _WalletPortfolioAstrox _self;
  final $Res Function(_WalletPortfolioAstrox) _then;

  /// Create a copy of WalletPortfolioAstrox
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? summary = null, Object? tokens = null}) {
    return _then(
      _WalletPortfolioAstrox(
        summary: null == summary
            ? _self.summary
            : summary // ignore: cast_nullable_to_non_nullable
                  as AstroxWalletSummary,
        tokens: null == tokens
            ? _self._tokens
            : tokens // ignore: cast_nullable_to_non_nullable
                  as List<AstroxToken>,
      ),
    );
  }

  /// Create a copy of WalletPortfolioAstrox
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AstroxWalletSummaryCopyWith<$Res> get summary {
    return $AstroxWalletSummaryCopyWith<$Res>(_self.summary, (value) {
      return _then(_self.copyWith(summary: value));
    });
  }
}
