import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart';

import '/constants/envs.dart' show envApiUrlAstroxOp;
import '/models/_shared.dart' show NumberDecimalConverter, NumberDecimalRequiredConverter;
import '/models/business.dart';
import '/provider/api.dart' hide apiServiceProvider;
import '_path.dart';

part 'astrox.freezed.dart';

part 'astrox.g.dart';

final class AstroxApi {
  AstroxApi(this.ref);

  final Ref ref;

  late final op = AstroxOpApi(ref);
}

final class AstroxOpApi {
  AstroxOpApi(this.ref);

  final Ref ref;

  late final _http = ref.read(httpProvider).clone(options: BaseOptions(baseUrl: envApiUrlAstroxOp));

  Future<List<Network>> getNetworks({
    CancelToken? cancelToken,
  }) async {
    final res = await _http
        .get(
          ApiPath.op.networkConfig,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => Network.fromJson(v.asJson()));
    return rep.list;
  }

  Future<AstroxPaged<AstroxTokenConfig>> getTokens({
    required String blockChain,
    required int pageNum,
    required int pageSize,
    CancelToken? cancelToken,
  }) async {
    final res = await _http
        .post(
          ApiPath.op.tokenList,
          data: {
            'blockChain': blockChain,
            'pageNum': pageNum,
            'pageSize': pageSize,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => AstroxPaged.fromJson(v.asJson(), (v) => AstroxTokenConfig.fromJson(v.asJson())),
    );
    return rep.data;
  }
}

@Freezed(genericArgumentFactories: true)
sealed class AstroxPaged<T> with _$AstroxPaged<T> {
  const factory AstroxPaged({
    @JsonKey(name: 'pageNum') required int page,
    @JsonKey(name: 'pageSize') required int size,
    @JsonKey(name: 'totalPage') required int pages,
    @JsonKey(name: 'total') @Default(0) int total,
    @JsonKey(name: 'list') @Default([]) List<T> list,
  }) = _AstroxPaged<T>;

  factory AstroxPaged.fromJson(Map<String, Object?> json, ObjectFactory<T> factory) =>
      _$AstroxPagedFromJson(json, factory);
}

@freezed
sealed class AstroxTokenConfig with _$AstroxTokenConfig {
  const factory AstroxTokenConfig({
    @JsonKey(name: 'blockChain') required String blockChain,
    @JsonKey(name: 'contractAddress') required String contractAddress,
    @JsonKey(name: 'createTime') required String createTime,
    @JsonKey(name: 'desc') @Default('') String desc,
    @JsonKey(name: 'digits') @Default(0) int digits,
    @JsonKey(name: 'icon') @Default('') String icon,
    @JsonKey(name: 'name') @Default('') String name,
    @JsonKey(name: 'serviceCharge') @Default(0) int serviceCharge,
    @JsonKey(name: 'standard') required String standard,
    @JsonKey(name: 'symbol') required String symbol,
    @JsonKey(name: 'totalSupply') @Default(0) int totalSupply,
    @JsonKey(name: 'weight') @Default(0) int weight,
    @JsonKey(name: 'coinMarketCapUcid') @Default('') String coinMarketCapUcid,
  }) = _AstroxTokenConfig;

  factory AstroxTokenConfig.fromJson(Map<String, dynamic> json) => _$AstroxTokenConfigFromJson(json);
}

@Freezed(fromJson: false, toJson: false)
sealed class AstroxTokenBalance with _$AstroxTokenBalance {
  const factory AstroxTokenBalance({
    required AstroxTokenConfig token,
    required Decimal balance,
    required Decimal? price,
  }) = _AstroxTokenBalance;
}

@freezed
sealed class AstroxWalletSummary with _$AstroxWalletSummary implements IWalletSummary {
  const factory AstroxWalletSummary({
    @NumberDecimalRequiredConverter() @JsonKey(name: 'totalValueUsd') required Decimal totalValueUsd,
    @JsonKey(name: 'hasEmptyValue') @Default(false) bool hasEmptyValue,
  }) = _AstroxWalletSummary;

  factory AstroxWalletSummary.fromJson(Map<String, dynamic> json) => _$AstroxWalletSummaryFromJson(json);

  static final empty = AstroxWalletSummary(
    totalValueUsd: Decimal.zero,
  );
}

@freezed
sealed class AstroxToken with _$AstroxToken implements IToken {
  @Implements<IToken>()
  const factory AstroxToken({
    @JsonKey(name: 'blockChain') required String chainName,
    @JsonKey(name: 'symbol') required String symbol,
    @JsonKey(name: 'name') required String name,
    @JsonKey(name: 'logo') required String logo,
    @JsonKey(name: 'address') required String address,
    @JsonKey(name: 'decimals') required int decimals,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'balance') required Decimal balance,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'realBalance') required Decimal realBalance,
    @NumberDecimalConverter() @JsonKey(name: 'priceUsd') required Decimal? priceUsd,
    @NumberDecimalConverter() @JsonKey(name: 'valueUsd') required Decimal? valueUsd,
  }) = _AstroxToken;

  const AstroxToken._();

  factory AstroxToken.fromJson(Map<String, dynamic> json) => _$AstroxTokenFromJson(json);

  @override
  String get chainIdOKX => '';
}

@freezed
sealed class WalletPortfolioAstrox with _$WalletPortfolioAstrox implements WalletPortfolio<AstroxToken> {
  @Implements<WalletPortfolio<AstroxToken>>()
  const factory WalletPortfolioAstrox({
    required AstroxWalletSummary summary,
    required List<AstroxToken> tokens,
  }) = _WalletPortfolioAstrox;

  const WalletPortfolioAstrox._();

  factory WalletPortfolioAstrox.fromJson(Map<String, dynamic> json) => _$WalletPortfolioAstroxFromJson(json);

  static final empty = WalletPortfolioAstrox(
    summary: AstroxWalletSummary.empty,
    tokens: <AstroxToken>[],
  );

  @override
  AstroxToken? tokenByAddress(String address) => tokens.firstWhereOrNull(
    (e) => e.address == address,
  );
}
