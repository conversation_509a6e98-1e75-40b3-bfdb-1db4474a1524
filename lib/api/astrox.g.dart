// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'astrox.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AstroxPaged<T> _$AstroxPagedFromJson<T>(
  Map json,
  T Function(Object? json) fromJsonT,
) => _AstroxPaged<T>(
  page: (json['pageNum'] as num).toInt(),
  size: (json['pageSize'] as num).toInt(),
  pages: (json['totalPage'] as num).toInt(),
  total: (json['total'] as num?)?.toInt() ?? 0,
  list: (json['list'] as List<dynamic>?)?.map(fromJsonT).toList() ?? const [],
);

Map<String, dynamic> _$AstroxPagedToJson<T>(
  _AstroxPaged<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'pageNum': instance.page,
  'pageSize': instance.size,
  'totalPage': instance.pages,
  'total': instance.total,
  'list': instance.list.map(toJsonT).toList(),
};

_AstroxTokenConfig _$AstroxTokenConfigFromJson(Map json) => _AstroxTokenConfig(
  blockChain: json['blockChain'] as String,
  contractAddress: json['contractAddress'] as String,
  createTime: json['createTime'] as String,
  desc: json['desc'] as String? ?? '',
  digits: (json['digits'] as num?)?.toInt() ?? 0,
  icon: json['icon'] as String? ?? '',
  name: json['name'] as String? ?? '',
  serviceCharge: (json['serviceCharge'] as num?)?.toInt() ?? 0,
  standard: json['standard'] as String,
  symbol: json['symbol'] as String,
  totalSupply: (json['totalSupply'] as num?)?.toInt() ?? 0,
  weight: (json['weight'] as num?)?.toInt() ?? 0,
  coinMarketCapUcid: json['coinMarketCapUcid'] as String? ?? '',
);

Map<String, dynamic> _$AstroxTokenConfigToJson(_AstroxTokenConfig instance) =>
    <String, dynamic>{
      'blockChain': instance.blockChain,
      'contractAddress': instance.contractAddress,
      'createTime': instance.createTime,
      'desc': instance.desc,
      'digits': instance.digits,
      'icon': instance.icon,
      'name': instance.name,
      'serviceCharge': instance.serviceCharge,
      'standard': instance.standard,
      'symbol': instance.symbol,
      'totalSupply': instance.totalSupply,
      'weight': instance.weight,
      'coinMarketCapUcid': instance.coinMarketCapUcid,
    };

_AstroxWalletSummary _$AstroxWalletSummaryFromJson(Map json) =>
    _AstroxWalletSummary(
      totalValueUsd: const NumberDecimalRequiredConverter().fromJson(
        json['totalValueUsd'] as Object,
      ),
      hasEmptyValue: json['hasEmptyValue'] as bool? ?? false,
    );

Map<String, dynamic> _$AstroxWalletSummaryToJson(
  _AstroxWalletSummary instance,
) => <String, dynamic>{
  'totalValueUsd': const NumberDecimalRequiredConverter().toJson(
    instance.totalValueUsd,
  ),
  'hasEmptyValue': instance.hasEmptyValue,
};

_AstroxToken _$AstroxTokenFromJson(Map json) => _AstroxToken(
  chainName: json['blockChain'] as String,
  symbol: json['symbol'] as String,
  name: json['name'] as String,
  logo: json['logo'] as String,
  address: json['address'] as String,
  decimals: (json['decimals'] as num).toInt(),
  balance: const NumberDecimalRequiredConverter().fromJson(
    json['balance'] as Object,
  ),
  realBalance: const NumberDecimalRequiredConverter().fromJson(
    json['realBalance'] as Object,
  ),
  priceUsd: const NumberDecimalConverter().fromJson(json['priceUsd']),
  valueUsd: const NumberDecimalConverter().fromJson(json['valueUsd']),
);

Map<String, dynamic> _$AstroxTokenToJson(
  _AstroxToken instance,
) => <String, dynamic>{
  'blockChain': instance.chainName,
  'symbol': instance.symbol,
  'name': instance.name,
  'logo': instance.logo,
  'address': instance.address,
  'decimals': instance.decimals,
  'balance': const NumberDecimalRequiredConverter().toJson(instance.balance),
  'realBalance': const NumberDecimalRequiredConverter().toJson(
    instance.realBalance,
  ),
  'priceUsd': const NumberDecimalConverter().toJson(instance.priceUsd),
  'valueUsd': const NumberDecimalConverter().toJson(instance.valueUsd),
};

_WalletPortfolioAstrox _$WalletPortfolioAstroxFromJson(Map json) =>
    _WalletPortfolioAstrox(
      summary: AstroxWalletSummary.fromJson(
        Map<String, dynamic>.from(json['summary'] as Map),
      ),
      tokens: (json['tokens'] as List<dynamic>)
          .map((e) => AstroxToken.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$WalletPortfolioAstroxToJson(
  _WalletPortfolioAstrox instance,
) => <String, dynamic>{
  'summary': instance.summary.toJson(),
  'tokens': instance.tokens.map((e) => e.toJson()).toList(),
};
