import 'package:share_plus/share_plus.dart';

import '/constants/envs.dart' show envUrlWebsite;

export 'package:share_plus/share_plus.dart';

final class ShareBuilder {
  ShareBuilder();

  final _queryParameters = <String, String>{};
  final _paths = <String>[];

  static SharePlus get $ => SharePlus.instance;

  static Future<ShareResult> shareUri(Uri uri) {
    return $.share(ShareParams(uri: uri));
  }

  static const sourceApp = 'app';

  ShareBuilder addQueryParameter(String key, String value) {
    _queryParameters[key] = value;
    return this;
  }

  ShareBuilder addPath(String value) {
    assert(!value.startsWith('/'));
    _paths.add(value);
    return this;
  }

  ShareBuilder addPaths(List<String> value) {
    _paths.addAll(value);
    return this;
  }

  Uri build() {
    final uri = Uri.parse(envUrlWebsite).replace(
      pathSegments: _paths,
      queryParameters: _queryParameters.isNotEmpty ? _queryParameters : null,
    );
    return uri;
  }
}
