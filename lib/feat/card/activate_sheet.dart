import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_l10n/me_l10n.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_ui/me_ui.dart' hide ScrollableBottomSheet;

import '/constants/toast_messages.dart';
import '/feat/card/helper.dart' show CardActivateParams;
import '/internals/methods.dart' show isNetworkError;
import '/models/card.dart' show CardType, CardInfoBasicInactivated;
import '/provider/api.dart' show ApiException, apiServiceProvider;
import '/res/assets.gen.dart';
import '/routes/card3_routes.dart' show Routes;
import '/ui/widgets/app_loading.dart' show AppLoading;
import '/ui/widgets/dialog/scrollable_bottom_sheet.dart' show ScrollableBottomSheet;
import '/ui/widgets/toast.dart' show Card3ToastUtil;

class ActivateCardSheet extends ConsumerWidget {
  const ActivateCardSheet({
    super.key,
    required this.card,
    required this.params,
  });

  final CardInfoBasicInactivated card;
  final CardActivateParams params;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ScrollableBottomSheet(
      title: 'New item detected',
      sliversBuilder: (context) => [
        SliverFillRemaining(
          hasScrollBody: false,
          child: Column(
            spacing: 24.0,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: _CardCover(
                  card: card,
                  params: params,
                  height: 200.0,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 3.0),
                decoration: BoxDecoration(
                  border: Border.all(color: context.meTheme.successColor),
                  borderRadius: RadiusConstants.max,
                ),
                child: Row(
                  spacing: 6.0,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox.square(
                      dimension: 10.0,
                      child: CircleAvatar(
                        backgroundColor: context.meTheme.successColor,
                      ),
                    ),
                    Text(
                      'To be activated',
                      style: TextStyle(color: context.meTheme.successColor),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
      bottomBuilder: (context) => ThemeTextButton(
        onPressed: () => AppLoading.run(() async {
          try {
            await ref
                .read(apiServiceProvider)
                .activateCardByParams(
                  uid: params.uid,
                  ctr: params.ctr ?? '',
                  cmac: params.cmac ?? '',
                  activateCode: params.activateCode,
                );
            final card = await ref
                .read(apiServiceProvider)
                .getCard(
                  cardCode: params.uid,
                  ctr: params.ctr ?? '',
                  cmac: params.cmac ?? '',
                  activateCode: params.activateCode,
                );
            meNavigator.pushReplacementNamed(
              Routes.cardActivateResult.name,
              arguments: Routes.cardActivateResult.d(card: card, activated: true),
            );
          } on ApiException catch (e) {
            Card3ToastUtil.showToast(message: ToastMessages.activationError('${e.message} ${e.code}'));
            rethrow;
          } catch (e) {
            Card3ToastUtil.showToast(
              message: ToastMessages.activationError(
                isNetworkError(e) ? context.l10nME.networkError : '$e',
              ),
            );
            rethrow;
          }
        }),
        text: 'Activate Now',
      ),
    );
  }
}

class _CardCover extends StatelessWidget {
  const _CardCover({
    required this.card,
    required this.params,
    this.height,
  });

  final CardInfoBasicInactivated card;
  final CardActivateParams params;
  final double? height;

  @override
  Widget build(BuildContext context) {
    final paddedId = card.id.toString().padLeft(8, '0');
    final fit = BoxFit.cover;
    return Column(
      spacing: 6.0,
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: Container(
            width: double.infinity,
            height: height,
            alignment: Alignment.center,
            child: DecoratedBox(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x20000000),
                    blurRadius: 10,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: Hero(
                tag: 'card-activate-cover-${params.uid}',
                child: MEImage(
                  card.backCover,
                  borderRadius: BorderRadius.circular(12.0),
                  fit: BoxFit.fitHeight,
                  emptyBuilder: (context) => switch (card.cardType) {
                    CardType.STICKER => Assets.icons.images.coverSticker.image(fit: fit),
                    CardType.WRISTBAND => Assets.icons.images.coverWristband.image(fit: fit),
                    _ => Assets.icons.images.coverNormalBack.image(fit: fit),
                  },
                ),
              ),
            ),
          ),
        ),
        Text(
          '${paddedId.substring(0, 4)} ${paddedId.substring(paddedId.length - 4)}',
          style: context.textTheme.bodySmall,
        ),
      ],
    );
  }
}
