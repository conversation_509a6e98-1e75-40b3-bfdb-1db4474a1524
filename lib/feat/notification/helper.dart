import 'dart:io' as io show Platform;
import 'dart:math' as math show Random;

import 'package:flutter/widgets.dart' show AppLifecycleListener, AppLifecycleState;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:me_misc/me_misc.dart' show MERouteAction, MERouteListener, meNavigatorObserver;

import '/routes/card3_routes.dart' show Routes;
import 'handler.dart' show handleNotificationOpenWithPayload;

final class NotificationChannel {
  const NotificationChannel({
    required this.id,
    required this.name,
    required this.description,
  });

  final String id;
  final String name;
  final String description;
}

abstract final class NotificationHelper {
  static const channelNotification = NotificationChannel(
    id: 'notification',
    name: 'Notifications',
    description: 'Notify updates you need to know.',
  );
  static const channelLive = NotificationChannel(
    id: 'live',
    name: 'Live Messages',
    description: 'Messages from the ongoing live room.',
  );
  static const channels = [channelNotification, channelLive];

  static FlutterLocalNotificationsPlugin get _$ => FlutterLocalNotificationsPlugin();

  static AndroidFlutterLocalNotificationsPlugin get _$android => _$.resolvePlatformSpecificImplementation()!;

  static IOSFlutterLocalNotificationsPlugin get _$ios => _$.resolvePlatformSpecificImplementation()!;

  static MacOSFlutterLocalNotificationsPlugin get _$macos => _$.resolvePlatformSpecificImplementation()!;

  static final _idRandom = math.Random();
  static AppLifecycleState _lifecycleState = AppLifecycleState.resumed;

  static bool _startHandle = false;
  static NotificationResponse? _pendingResponse;

  static Future<void> initialize() async {
    AppLifecycleListener(
      onStateChange: (state) => _lifecycleState = state,
    );
    meNavigatorObserver.addListener(
      MERouteListener(
        actions: MERouteAction.values.toSet(),
        onRouteChanged: (_, newRoute, _) {
          final previousStartHandle = _startHandle;
          _startHandle =
              meNavigatorObserver.history.any(
                (route) => route.settings.name == Routes.home.name,
              ) ||
              newRoute?.settings.name == Routes.home.name;
          if (_pendingResponse case final r? when _startHandle && !previousStartHandle) {
            _handleNotificationOpen(r);
            _pendingResponse = null;
          }
        },
      ),
    );

    final sAndroid = const AndroidInitializationSettings(
      'app_logo', // Drawable.
    );
    final sDarwin = const DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
      requestProvisionalPermission: true,
    );
    final settings = InitializationSettings(
      android: sAndroid,
      iOS: sDarwin,
      macOS: sDarwin,
    );
    await _$.initialize(
      settings,
      onDidReceiveNotificationResponse: _handleNotificationOpen,
    );
    final launchDetails = await _$.getNotificationAppLaunchDetails();
    _pendingResponse = launchDetails?.notificationResponse;
  }

  static void _handleNotificationOpen(NotificationResponse response) {
    if (!_startHandle) {
      _pendingResponse = response;
      return;
    }
    if (response.payload case final payload?) {
      handleNotificationOpenWithPayload(payload);
    }
  }

  static Future<void> postNotification({
    required String? title,
    required String? body,
    required String? payload,
    int? id,
    AndroidNotificationDetails? detailsAndroid,
    required NotificationChannel channelAndroid,
    String? iconAndroid,
    String? largeIconAndroid,
    DarwinNotificationDetails? detailsDarwin,
    Set<AppLifecycleState> postWhenAppLifecycles = const {
      AppLifecycleState.detached,
      AppLifecycleState.inactive,
      AppLifecycleState.hidden,
      AppLifecycleState.paused,
    },
  }) async {
    if (!postWhenAppLifecycles.contains(_lifecycleState)) {
      return;
    }

    final bool? hasPermission;
    if (io.Platform.isAndroid) {
      hasPermission = await _$android.requestNotificationsPermission();
    } else if (io.Platform.isIOS) {
      hasPermission = await _$ios.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
        provisional: true,
      );
    } else if (io.Platform.isMacOS) {
      hasPermission = await _$macos.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
        provisional: true,
      );
    } else {
      hasPermission = true;
    }
    if (hasPermission != true) {
      return;
    }

    final android = AndroidNotificationDetails(
      channelAndroid.id,
      channelAndroid.name,
      channelDescription: channelAndroid.description,
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
      icon: iconAndroid,
      largeIcon: switch (largeIconAndroid) {
        final path? => FilePathAndroidBitmap(path),
        _ => null,
      },
    );
    final darwin = const DarwinNotificationDetails(
      presentBadge: true,
      presentBanner: true,
      presentList: true,
      presentSound: true,
      interruptionLevel: InterruptionLevel.active,
    );
    final details = NotificationDetails(
      android: detailsAndroid ?? android,
      iOS: detailsDarwin ?? darwin,
      macOS: detailsDarwin ?? darwin,
    );

    if (payload?.trim() == '') {
      payload = null;
    }
    return _$.show(
      id ?? _idRandom.nextInt(0x7FFFFFFF),
      title,
      body,
      details,
      payload: payload,
    );
  }
}
