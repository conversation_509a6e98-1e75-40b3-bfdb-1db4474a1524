import 'dart:convert' show jsonDecode;

import 'package:collection/collection.dart' show IterableExtension;
import 'package:me_extensions/me_extensions.dart' show MEBaseMapExtension;
import 'package:me_misc/me_misc.dart' show MENavigatorExtension, meNavigator, meNavi<PERSON>or<PERSON><PERSON>, meNavigatorObserver;
import 'package:me_models/me_models.dart' show NotificationParams;
import 'package:me_utils/me_utils.dart' show LogUtil;

import '/internals/methods.dart' show handleExceptions;
import '/internals/riverpod.dart' show globalContainer;
import '/models/business.dart' show ImageAITaskDone;
import '/models/user.dart' show UserFromRelationType;
import '/provider/settings.dart' show selectedIndexProvider;
import '/routes/card3_routes.dart' show Routes;
import '/ui/biz/home/<USER>' show connectionsRelationTypeProvider;

enum NotificationParamsAction {
  navigateToHomeTab('navigateToHomeTab'),
  openNotificationCenter('openNotificationCenter'),
  openConnections('openConnections'),
  openAIGenerateImage('openAIGenerateImage');

  const NotificationParamsAction(this.value);

  final String value;

  static NotificationParamsAction? fromValue(String? value) {
    if (value == null || value.isEmpty) {
      return null;
    }
    return NotificationParamsAction.values.firstWhereOrNull(
      (e) => e.value == value,
    );
  }
}

bool handleNotificationOpenWithPayload(Object value) {
  try {
    final Map<String, dynamic>? parsedPayload = switch (value) {
      final String p when p.trim().isNotEmpty => jsonDecode(p.trim()),
      final Map<String, dynamic> p => p,
      final Map map => map.toJsonMap(),
      _ => null,
    };
    if (parsedPayload == null || parsedPayload.isEmpty) {
      return false;
    }

    final Map<String, dynamic> effectivePayload = switch (parsedPayload) {
      {'params': {'app': final Map appParams}} => appParams.toJsonMap(),
      {'app': final Map appParams} => appParams.toJsonMap(),
      _ => parsedPayload,
    };
    if (effectivePayload.isEmpty) {
      return false;
    }

    final params = NotificationParams.fromJson(effectivePayload);

    final action = NotificationParamsAction.fromValue(params.nativeAction);
    if (action != null) {
      switch (action) {
        case NotificationParamsAction.navigateToHomeTab:
          final tabIndex = params.nativeActionArguments?['tabIndex'] as int?;
          _handleNavigateToHomeTab(tabIndex);
        case NotificationParamsAction.openNotificationCenter:
          _handleOpenNotificationCenter();
        case NotificationParamsAction.openConnections:
          final type = UserFromRelationType.fromName(
            params.nativeActionArguments?['type'] as String?,
          );
          _handleOpenConnections(type);
        case NotificationParamsAction.openAIGenerateImage:
          final arguments = params.nativeActionArguments;
          if (arguments == null) {
            return false;
          }
          final taskResult = ImageAITaskDone.fromJson(arguments);
          _handleOpenAIGenerateImage(taskResult);
      }
      return true;
    }

    if (params.navigationRoute case final route?) {
      if (meNavigatorKey.currentState == null) {
        LogUtil.w(
          'Navigator is not ready for navigations.',
          tag: '🔔 HandleNotificationOpenWithPayload',
          tagWithTrace: false,
        );
        return false;
      }
      if (params.navigationRouteReplaceExists) {
        meNavigator.removeNamedAndPushAndRemoveUntil(
          route,
          arguments: params.navigationArguments,
          predicate: (_) => true,
        );
      } else {
        meNavigator.pushNamed(
          route,
          arguments: params.navigationArguments,
        );
      }
      return true;
    }

    return false;
  } catch (e, s) {
    handleExceptions(error: e, stackTrace: s);
    return false;
  }
}

// ServiceApi get _service => globalContainer.read(apiServiceProvider);

bool _handleNavigateToHomeTab(int? tabIndex) {
  if (tabIndex == null) {
    return false;
  }
  if (!meNavigatorObserver.history.any((r) => r.settings.name == Routes.home.name)) {
    return false;
  }
  meNavigator.popUntil((r) => r.settings.name == Routes.home.name);
  globalContainer.read(selectedIndexProvider.notifier).state = tabIndex;
  return true;
}

void _handleOpenNotificationCenter() {
  meNavigator.removeNamedAndPushAndRemoveUntil(
    Routes.notification.name,
    predicate: (_) => true,
  );
}

void _handleOpenConnections(UserFromRelationType? type) {
  if (type != null && _handleNavigateToHomeTab(1)) {
    globalContainer.read(connectionsRelationTypeProvider.notifier).state = type;
  }
}

Future<void> _handleOpenAIGenerateImage(ImageAITaskDone taskResult) async {
  final result = await meNavigator.pushNamed(
    Routes.aiGenerateImage.name,
    arguments: Routes.aiGenerateImage.d(
      taskId: taskResult.taskId,
      generatedUrl: taskResult.imageUrl,
    ),
  );
  if (result is! String) {
    return;
  }
  meNavigator.pushNamed(
    Routes.socialEditProfile.name,
    arguments: Routes.socialEditProfile.d(pendingAvatarUrl: result),
  );
}
