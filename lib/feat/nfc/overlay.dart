import 'dart:async';
import 'dart:io' as io show Platform;

import 'package:flutter/material.dart';
import 'package:me_l10n/me_l10n.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_ui/me_ui.dart';

import '/res/assets.gen.dart';
import 'helper.dart';

const _routeDuration = Duration(milliseconds: 500);

class NfcStateOverlay extends StatefulWidget {
  const NfcStateOverlay._();

  static OverlayEntry? _entry;
  static final _state = ValueNotifier<NfcScanState>(NfcScanState.scanning);

  static void show() {
    if (io.Platform.isIOS) {
      return;
    }

    if (_entry != null) {
      return;
    }

    updateState(NfcScanState.scanning);
    final entry = _entry = OverlayEntry(
      builder: (context) => const NfcStateOverlay._(),
    );
    Overlay.of(meContext).insert(entry);
  }

  static void updateState(NfcScanState state) {
    _state.value = state;
  }

  static void dismiss() {
    if (io.Platform.isIOS) {
      return;
    }

    final entry = _entry;
    if (entry == null) {
      return;
    }

    _entry = null;
    Timer(_routeDuration, () {
      entry.remove();
      updateState(NfcScanState.scanning);
    });
  }

  @override
  State<NfcStateOverlay> createState() => _NfcStateOverlayState();
}

class _NfcStateOverlayState extends State<NfcStateOverlay> {
  bool _showing = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _showing = true;
      });
    });
  }

  void _stop() {
    setState(() {
      _showing = false;
    });
    NfcStateOverlay.dismiss();
    NfcHelper.stopPolling();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: NfcStateOverlay._state,
      builder: (context, state, _) => _build(context, state),
    );
  }

  Widget _build(BuildContext context, NfcScanState state) {
    final theme = Theme.of(context);
    return Material(
      type: MaterialType.transparency,
      child: AnimatedContainer(
        curve: Curves.easeInOutCubic,
        duration: _routeDuration,
        color: _showing ? Colors.black54 : Colors.transparent,
        alignment: Alignment.bottomCenter,
        padding: const EdgeInsets.all(8.0),
        child: AnimatedAlign(
          alignment: _showing ? Alignment.bottomCenter : Alignment.topCenter,
          heightFactor: _showing ? 1.0 : 0.0,
          duration: _routeDuration,
          curve: Curves.easeInOutCubic,
          child: Container(
            constraints: const BoxConstraints(
              maxWidth: 400.0,
              maxHeight: 420.0,
            ),
            padding: const EdgeInsets.all(32.0),
            decoration: ShapeDecoration(
              shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(36.0)),
              color: theme.cardColor,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _buildStatusText(state),
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                AnimatedSwitcher(
                  duration: kThemeAnimationDuration,
                  switchInCurve: Curves.easeInOutCubic,
                  switchOutCurve: Curves.easeInOutCubic,
                  child: _buildIcon(state),
                ),

                RippleTap(
                  onTap: _stop,
                  width: double.infinity,
                  padding: const EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(14.0)),
                  color: theme.dividerColor,
                  child: Text(
                    context.l10nME.cancelButton,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _buildStatusText(NfcScanState state) {
    switch (state) {
      case NfcScanState.failed:
        return 'Scan failed';
      default:
        return 'Ready to Scan';
    }
  }

  Widget _buildIcon(NfcScanState state) {
    return SizedBox.square(
      dimension: 150.0,
      child: FittedBox(
        fit: BoxFit.cover,
        child: switch (state) {
          NfcScanState.success => const Icon(
            Icons.check_circle_outline_rounded,
            color: Colors.green,
          ),
          NfcScanState.failed => const Icon(
            Icons.error_outline_rounded,
            color: Colors.red,
          ),
          _ => Assets.lottie.nfc.lottie(),
        },
      ),
    );
  }
}
