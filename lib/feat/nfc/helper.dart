import 'dart:async';
import 'dart:convert';
import 'dart:io' as io show Platform;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show PlatformException;
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart' show meNavigatorObserver;
import 'package:me_utils/me_utils.dart' show LogUtil;
import 'package:ndef_record/ndef_record.dart';
import 'package:nfc_manager/nfc_manager.dart';
import 'package:nfc_manager/nfc_manager_ios.dart' as ios;
import 'package:nfc_manager_ndef/nfc_manager_ndef.dart';

import '/feat/card/helper.dart';
import '/feat/link/helper.dart';
import '/internals/methods.dart' show handleExceptions;
import '/routes/card3_routes.dart' show Routes;
import '/ui/widgets/toast.dart';
import 'overlay.dart';

export 'package:ndef_record/ndef_record.dart';
export 'package:nfc_manager/nfc_manager.dart';
export 'package:nfc_manager/nfc_manager_ios.dart';
export 'package:nfc_manager_ndef/nfc_manager_ndef.dart';
export 'overlay.dart';

typedef NfcResultCallback = bool Function(String result);

enum NfcScanState {
  scanning,
  success,
  failed,
}

enum NfcTagContentType { text, uri, other }

final class NfcTagContent {
  const NfcTagContent({
    required this.type,
    required this.raw,
    required this.readable,
    this.languageCode,
  });

  final NfcTagContentType type;
  final Uint8List raw;
  final String readable;
  final String? languageCode;

  Uri? get readableUri => switch (readable.trim()) {
    final s when s.isNotEmpty => Uri.tryParse(s),
    _ => null,
  };
}

final class NfcHelper {
  static const _tag = '📲 NFCHelper';

  static NfcManager get _$ => NfcManager.instance;

  static void initialize() {
    AppLifecycleListener(onStateChange: _onLifecycleStateChanged);
    _onLifecycleStateChanged(AppLifecycleState.resumed);
  }

  static Timer? _abilityCheckTimer;

  static void _onLifecycleStateChanged(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _startAutomaticallyPolling();
      _abilityCheckTimer = Timer.periodic(const Duration(seconds: 3), (_) {
        _startAutomaticallyPolling();
      });
    } else if (state == AppLifecycleState.hidden) {
      if (_sessionLock case final lock?) {
        lock.maybeComplete(null);
        _sessionLock = null;
      }
      _stopSession();
      _abilityCheckTimer?.cancel();
    }
  }

  static bool get running => _sessionLock != null;
  static Completer<NfcTag?>? _sessionLock;

  static Future<bool> _checkAbility() async {
    try {
      return await _$.isAvailable();
    } catch (e, s) {
      if (e is PlatformException && e.code == 'not_supported') {
        return false;
      }
      handleExceptions(error: e, stackTrace: s, tag: _tag, tagWithTrace: false);
      return false;
    }
  }

  static Future<void> _startSession() {
    return _$.startSession(
      pollingOptions: NfcPollingOption.values.toSet(),
      onDiscovered: _onDiscovered,
      onSessionErrorIos: _onErrorIOS,
      invalidateAfterFirstReadIos: true,
    );
  }

  static Future<void> _stopSession() {
    return _$.stopSession().catchError((e, s) {});
  }

  static void _startAutomaticallyPolling() {
    // Don't polling on iOS, since a system popup will be shown.
    if (io.Platform.isIOS) {
      return;
    }

    if (running) {
      return;
    }

    // Don't polling until we got to the home.
    if (!meNavigatorObserver.history.any((e) => e.settings.name == Routes.home.name)) {
      return;
    }

    // A session is now running.
    final lock = _sessionLock = Completer<NfcTag?>();
    Future<void>(() async {
      final ability = await _checkAbility();
      if (!ability) {
        lock.maybeComplete();
        _sessionLock = null;
        return;
      }
      return _startSession();
    }).catchError((e, s) {
      lock.maybeCompleteError(e, s);
      _sessionLock = null;
    });
  }

  static Future<void> _onDiscovered(NfcTag tag) async {
    if (io.Platform.isIOS) {
      _sessionLock?.maybeComplete(null);
      _sessionLock = null;
    }

    LogUtil.d('Detected NFC tag...', tag: _tag, tagWithTrace: false);
    final ndef = Ndef.from(tag);
    if (ndef == null) {
      LogUtil.w('Empty NDEF tag', tag: _tag, tagWithTrace: false);
      return;
    }
    NdefMessage? ndefMessage = ndef.cachedMessage;
    if (ndefMessage == null || ndefMessage.records.isEmpty) {
      ndefMessage = await ndef.read();
    }
    if (ndefMessage == null || ndefMessage.records.isEmpty) {
      LogUtil.w(
        'Empty NDEF message/records for the NDEF tag: $tag',
        tag: _tag,
        tagWithTrace: false,
      );
      return;
    }

    final records = ndefMessage.records;
    final contents = <NfcTagContent>[];
    for (final record in records) {
      try {
        final wellKnown = record.typeNameFormat == TypeNameFormat.wellKnown;
        final type = String.fromCharCodes(record.type);

        if (wellKnown && type == 'U') {
          final payload = record.payload;
          final uri = String.fromCharCodes(payload.sublist(1));

          contents.add(
            NfcTagContent(
              type: NfcTagContentType.uri,
              raw: payload,
              readable: uri,
            ),
          );
          continue;
        }

        // Try to make text content regardless of parameters.
        final payload = record.payload;

        final languageCodeLength = payload.first & 0x3f;
        final languageCodeBytesEnd = (1 + languageCodeLength).min(payload.length);
        final languageCodeBytes = payload.sublist(1, languageCodeBytesEnd);
        final languageCode = String.fromCharCodes(languageCodeBytes);

        final isUTF16 = (payload.first & 0x80) != 0;
        final textBytes = payload.sublist(languageCodeBytesEnd);
        final text = isUTF16 ? String.fromCharCodes(textBytes) : utf8.decode(textBytes);

        final content = NfcTagContent(
          type: NfcTagContentType.text,
          raw: record.payload,
          readable: text,
          languageCode: languageCode,
        );
        contents.add(content);
      } catch (e, s) {
        final exception = NfcTagDeserializeException(record: record, error: e);
        handleExceptions(error: exception, stackTrace: s, tag: _tag, tagWithTrace: false);
        continue;
      }
    }
    if (contents.isEmpty) {
      return;
    }

    // Mark the scanning succeed state and stop the session for iOS.
    NfcStateOverlay.updateState(NfcScanState.success);
    Timer(const Duration(seconds: 2), () {
      NfcStateOverlay.dismiss();
    });
    if (io.Platform.isIOS) {
      _stopSession();
    }

    for (final content in contents) {
      if (_onResult(content.readable)) {
        break;
      }
    }
  }

  /// 处理NFC错误
  static Future<void> _onErrorIOS(ios.NfcReaderSessionErrorIos error) async {
    _sessionLock?.maybeComplete(null);
    _sessionLock = null;

    final code = error.code;
    // final replacingNames = <String>{
    //   'ndefReaderSessionError',
    //   'readerError',
    //   'readerSessionInvalidationError',
    //   'readerTransceiveError',
    //   'tagCommandConfigurationError',
    // };
    // final name = code.name.apply((String it) {
    //   for (final name in replacingNames) {
    //     it = it.replaceFirst(name, '');
    //   }
    //   return it;
    // });

    final canceled =
        code == ios.NfcReaderErrorCodeIos.readerSessionInvalidationErrorUserCanceled ||
        code == ios.NfcReaderErrorCodeIos.readerTransceiveErrorSessionInvalidated;
    LogUtil.e(
      '$code ${error.message}',
      enabled: !canceled,
      report: !canceled,
    );
  }

  static NfcResultCallback? _onResultCallback;
  static bool _manuallyPolling = false;

  static bool _onResult(String result) {
    if (_manuallyPolling) {
      _manuallyPolling = false;
      Timer(const Duration(seconds: 2), () {
        NfcStateOverlay.dismiss();
      });
    }

    if (_onResultCallback case final cb?) {
      final handled = cb(result);
      if (handled) {
        _onResultCallback = null;
      }
      return handled;
    }

    if (CardHelper.isCard3Format(result) && CardHelper.handleCardActivation(result)) {
      return true;
    }

    if (CardHelper.formalizeUrl(result) case final uri?) {
      AppLinkHelper.handleUri(uri);
      return true;
    }

    return false;
  }

  static Future<void> startPollingManually({
    NfcResultCallback? onResult,
  }) async {
    final ability = await _checkAbility();
    if (!ability) {
      Card3ToastUtil.showToast(message: ToastMessages.nfcNotAvailable);
      return;
    }

    _manuallyPolling = true;
    _onResultCallback = onResult;
    NfcStateOverlay.show();
    if (!running) {
      await _stopSession();
      return _startSession();
    }
  }

  static Future<void> stopPolling({
    bool force = false,
  }) {
    _manuallyPolling = false;
    NfcStateOverlay.dismiss();

    if (io.Platform.isAndroid && !force) {
      return SynchronousFuture(null);
    }

    return _stopSession();
  }
}

final class NfcTagDeserializeException implements Exception {
  NfcTagDeserializeException({
    required this.record,
    required this.error,
  });

  final NdefRecord record;
  final Object error;

  @override
  String toString() {
    final buffer = StringBuffer('NfcTagDeserializeException: $error');
    buffer.writeln('|--- format: ${record.typeNameFormat}');
    buffer.writeln('|--- type>b: ${record.type}');
    buffer.writeln('|--- bytes : ${record.payload}');
    buffer.writeln('|--- id    : ${record.identifier}');
    return buffer.toString();
  }
}

extension NdefRecordExt on NdefRecord {
  static NdefRecord fromJson(Map<String, dynamic> json) {
    return NdefRecord(
      typeNameFormat: (json['typeNameFormat'] as String).run(
        (it) => TypeNameFormat.values.firstWhere((e) => e.name == it),
      ),
      type: Uint8List.fromList((json['type'] as List).cast()),
      identifier: Uint8List.fromList((json['identifier'] as List).cast()),
      payload: Uint8List.fromList((json['payload'] as List).cast()),
    );
  }

  String get typeString => type.run((it) {
    try {
      return String.fromCharCodes(type);
    } catch (e) {
      return '<malformed>';
    }
  });

  Map<String, Object?> toJson() {
    return <String, Object?>{
      'typeNameFormat': typeNameFormat.name,
      'type': type,
      'id': identifier,
      'payload': payload,
    };
  }
}
