import 'package:cached_video_player_plus/cached_video_player_plus.dart';

import '/internals/box.dart' show BoxCaches;

final class AppVideoMetadataStorage implements IVideoPlayerMetadataStorage {
  AppVideoMetadataStorage();

  final _keyPrefix = 'video_metadata:v0';

  String _key(String key) => '$_keyPrefix:$key';

  @override
  Future<int?> read(String key) async {
    final result = BoxCaches.get(_key(key));
    if (result case final int? value) {
      return value;
    }
    return null;
  }

  @override
  Future<void> write(String key, int value) {
    return BoxCaches.put(key, value);
  }

  @override
  Future<void> remove(String key) {
    return BoxCaches.delete(key);
  }

  @override
  Future<void> erase() {
    return BoxCaches.clearByPrefix(_keyPrefix);
  }
}
