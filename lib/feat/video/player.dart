import 'dart:async' show Completer;

import 'package:cached_video_player_plus/cached_video_player_plus.dart';
import 'package:flutter/cupertino.dart' show CupertinoActivityIndicator;
import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart' show meRouteObserver;
import 'package:me_ui/me_ui.dart';
import 'package:video_player/video_player.dart' show VideoPlayer, VideoPlayerController, VideoPlayerOptions;
import 'package:visibility_detector/visibility_detector.dart';

class AppNetworkVideoPlayer extends StatefulWidget {
  const AppNetworkVideoPlayer({
    super.key,
    required this.url,
    required this.pauseWhenInvisible,
  });

  final String url;
  final bool pauseWhenInvisible;

  @override
  State<AppNetworkVideoPlayer> createState() => _AppNetworkVideoPlayerState();
}

class _AppNetworkVideoPlayerState extends State<AppNetworkVideoPlayer> with RouteAware {
  late CachedVideoPlayerPlus _controller;

  VideoPlayerController get _inner => _controller.controller;

  Completer<void>? _initializeLock;
  bool _initializeError = false;
  bool _started = false;

  @override
  void initState() {
    super.initState();
    _updateController();
    _initialize();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    meRouteObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void didUpdateWidget(AppNetworkVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.url != widget.url) {
      _reset();
    }
  }

  @override
  void didPushNext() {
    if (_controller.isInitialized) {
      _inner.pause();
    }
  }

  @override
  void dispose() {
    _initializeLock?.future.whenComplete(() {
      _controller.dispose();
    });
    super.dispose();
  }

  void _updateController() {
    _controller = CachedVideoPlayerPlus.networkUrl(
      Uri.parse(widget.url),
      videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
    );
  }

  Future<void> _reset() async {
    if (_initializeLock case final lock?) {
      await lock.future;
    }
    if (!mounted) {
      return;
    }

    if (_controller.isInitialized && _started) {
      await _inner.pause();
    }
    if (!mounted) {
      return;
    }

    await safeSetState(() {
      _initializeError = false;
      _started = false;
    });
    _controller.dispose();
    _updateController();
    await _initialize();
  }

  Future<void> _initialize() async {
    if (_initializeLock case final lock?) {
      return lock.future;
    }

    if (_controller.isInitialized) {
      return;
    }

    safeSetState(() {
      _initializeError = false;
    });
    final lock = _initializeLock = Completer<void>();
    _controller
        .initialize()
        .then(
          lock.complete,
          onError: (e, s) {
            _initializeError = true;
            lock.completeError(e, s);
          },
        )
        .whenComplete(() {
          safeSetState(() {});
          _initializeLock = null;
        });
    return lock.future;
  }

  void _toggle() {
    if (!_controller.isInitialized) {
      return;
    }

    if (!_started) {
      safeSetState(() {
        _started = true;
      });
    }
    if (_inner.value.isPlaying) {
      _inner.pause();
    } else {
      _inner.play();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final meTheme = theme.extension<METheme>()!;
    Widget child = const SizedBox.shrink();
    if (_controller.isInitialized) {
      child = FittedBox(
        fit: BoxFit.cover,
        child: RepaintBoundary(
          child: SizedBox.fromSize(
            size: _inner.value.size,
            child: VideoPlayer(_inner),
          ),
        ),
      );
      child = AnimatedOpacity(
        opacity: _started ? 1.0 : 0.0,
        duration: kThemeAnimationDuration,
        child: child,
      );
      child = Stack(
        fit: StackFit.expand,
        children: [
          child,
          PositionedDirectional(
            end: 16.0,
            bottom: 16.0,
            child: RippleTap(
              onTap: () => _toggle(),
              padding: const EdgeInsets.all(4.0),
              shape: const CircleBorder(),
              color: theme.scaffoldBackgroundColor.withValues(alpha: 0.5),
              child: ValueListenableBuilder(
                valueListenable: _inner,
                builder: (context, value, _) => Icon(
                  value.isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
                  color: theme.textTheme.bodyMedium?.color,
                  size: 28.0,
                ),
              ),
            ),
          ),
        ],
      );
    } else if (_initializeError) {
      child = Container(
        padding: const EdgeInsets.all(16.0),
        alignment: AlignmentDirectional.bottomEnd,
        child: RippleTap(
          onTap: () => _initialize(),
          padding: const EdgeInsets.all(4.0),
          shape: const CircleBorder(),
          color: theme.scaffoldBackgroundColor.withValues(alpha: 0.5),
          child: Icon(
            Icons.restart_alt_rounded,
            color: meTheme.failingColor,
            size: 28.0,
          ),
        ),
      );
    } else {
      child = Container(
        padding: const EdgeInsets.all(16.0),
        alignment: AlignmentDirectional.bottomEnd,
        child: const CupertinoActivityIndicator(),
      );
    }
    if (_controller.isInitialized && widget.pauseWhenInvisible) {
      child = VisibilityDetector(
        key: Key('video-player-${widget.url}-$hashCode'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction <= 0.0 && _controller.isInitialized && _inner.value.isPlaying) {
            _inner.pause();
          }
        },
        child: child,
      );
    }
    return child;
  }
}
