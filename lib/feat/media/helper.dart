import 'dart:async';

import 'package:video_player/video_player.dart';

abstract class MediaHelper {
  static Future<void> oneshotAsset(
    String dataSource, {
    String? package,
  }) async {
    final controller = VideoPlayerController.asset(
      dataSource,
      package: package,
      videoPlayerOptions: VideoPlayerOptions(
        mixWithOthers: true,
        allowBackgroundPlayback: true,
      ),
    );
    final lock = Completer<void>();
    try {
      controller.addListener(() {
        if (controller.value.isCompleted) {
          lock.complete();
        }
      });
      await controller.initialize();
      await controller.play();
      await lock.future;
      await controller.dispose();
    } catch (e) {
      await controller.dispose();
    }
  }
}
