import 'dart:async' show FutureOr;

import 'package:flutter/widgets.dart' show Route;
import 'package:me_misc/me_misc.dart'
    show MENavigatorExtension, globalL10nME, meNavigator, meNavigatorObserver, postRun;

import '/constants/envs.dart';
import '/feat/card/helper.dart' show CardHelper;
import '/internals/methods.dart' show copyAndToast, handleExceptions;
import '/internals/riverpod.dart' show globalContainer;
import '/models/user.dart' show UserReferralRequest;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart' show userReferralFromProvider;
import '/routes/card3_routes.dart' show Routes;
import '/ui/widgets/app_loading.dart';
import '/ui/widgets/dialog/tiny_dialog.dart';
import 'helper.dart' show AppLinkHelper;

abstract class AppLinkHandler {
  const AppLinkHandler();

  bool routeAtHome(Route? newRoute) =>
      meNavigatorObserver.history.any((route) => route.settings.name == Routes.home.name) ||
      newRoute?.settings.name == Routes.home.name;

  bool routeAtLogin(Route? newRoute) =>
      meNavigatorObserver.history.any((route) => route.settings.name == Routes.login.name) ||
      newRoute?.settings.name == Routes.login.name;

  bool availableFor(Uri uri, {Route? newRoute}) => routeAtHome(newRoute);

  FutureOr<bool> onLink(Uri uri);
}

class SchemeLinkHandler extends AppLinkHandler {
  const SchemeLinkHandler();

  static const methodActivateCard = 'active_card';
  static const methodCheckin = 'checkin';
  static const methodCustomize = 'customize';
  static const methodEvent = 'event';
  static const methodOpen = 'open';
  static const methodProfile = 'profile';
  static const methodReferral = 'referral';
  static const methodWebview = 'webview';
  static const methods = [
    methodActivateCard,
    methodCheckin,
    methodCustomize,
    methodEvent,
    methodOpen,
    methodProfile,
    methodReferral,
    methodWebview,
  ];

  static bool _validateUri(Uri uri) {
    return uri.scheme == AppLinkHelper.scheme;
  }

  @override
  bool onLink(Uri uri) {
    if (!_validateUri(uri)) {
      return false;
    }

    final path = uri.pathSegments.firstOrNull;
    switch (path) {
      case methodOpen:
        // Simply open the app.
        return true;
      case methodCustomize:
        return _handleCustomize(uri);
      case methodProfile:
        return _handleProfile(uri);
      case methodCheckin:
      case methodEvent:
      case methodWebview:
        return _handleWebview(uri);
      case methodReferral:
        return _handleReferral(uri);
      default:
        _handleActiveCard(uri);

        // Also reply true since no other handlers can handle the scheme link.
        return true;
    }
  }

  static void _handleActiveCard(Uri uri) {
    if (CardHelper.isCard3Format(uri.toString())) {
      CardHelper.handleCardActivation(uri.toString());
    }
  }

  static bool _handleCustomize(Uri uri) {
    final code = uri.queryParameters['code'];
    if (code == null) {
      return false;
    }
    postRun(() {
      meNavigator.removeNamedAndPushAndRemoveUntil(
        Routes.customize.name,
        arguments: Routes.customize.d(code: code),
        predicate: (_) => true,
      );
    });
    return true;
  }

  static bool _handleProfile(Uri uri) {
    return const SocialLinkHandler().onLink(uri);
  }

  static bool _handleWebview(Uri uri) {
    final url = (uri.queryParameters['url'] ?? '').trim();
    final title = (uri.queryParameters['title'] ?? '').trim();
    if (url.isEmpty) {
      return false;
    }

    late final String decodedUrl, decodedTitle;
    try {
      decodedUrl = Uri.decodeComponent(url);
      decodedTitle = Uri.decodeComponent(title);
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return false;
    }

    final parsedUrl = Uri.tryParse(decodedUrl);
    if (parsedUrl == null || !parsedUrl.hasAbsolutePath) {
      return false;
    }

    meNavigator.removeNamedAndPushAndRemoveUntil(
      Routes.webview.name,
      arguments: Routes.webview.d(
        url: parsedUrl.toString(),
        title: decodedTitle.isNotEmpty ? decodedTitle : null,
      ),
      predicate: (_) => true,
    );
    return true;
  }

  static bool _handleReferral(Uri uri) {
    return const ReferralLinkHandlerNoAuth().onLink(uri);
  }
}

class WebsiteLinkHandler extends AppLinkHandler {
  const WebsiteLinkHandler();

  static Iterable<String> get _allowedHosts => [
    envUrlCard3,
    envUrlWebsite,
  ].map((e) => Uri.parse(e).host);

  @override
  Future<bool> onLink(Uri uri) async {
    if (!_allowedHosts.contains(uri.host)) {
      return false;
    }

    // Ignore root page link.
    if (uri.pathSegments.isEmpty && uri.queryParameters.isEmpty) {
      return true;
    }

    final pathFirst = uri.pathSegments.elementAtOrNull(0)?.trim();
    switch (pathFirst) {
      case SchemeLinkHandler.methodCheckin:
      case SchemeLinkHandler.methodEvent:
        final title = uri.queryParameters['title']?.trim() ?? '';
        postRun(() {
          meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.webview.name,
            arguments: Routes.webview.d(
              url: uri.toString(),
              title: title.isNotEmpty ? title : null,
            ),
            predicate: (_) => true,
          );
        });
        return true;
      case SchemeLinkHandler.methodCustomize:
        final code = uri.queryParameters['code']?.trim() ?? uri.queryParameters['eventId']?.trim();
        if (code == null || code.isEmpty) {
          return false;
        }
        postRun(() {
          meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.customize.name,
            arguments: Routes.customize.d(code: code),
            predicate: (_) => true,
          );
        });
        return true;
      case SchemeLinkHandler.methodProfile:
        final code = uri.queryParameters['card_code']?.trim();
        if (code == null) {
          return false;
        }
        postRun(() {
          meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.socialProfile.name,
            arguments: Routes.socialProfile.d(code: code),
            predicate: (_) => true,
          );
        });
        return true;
      case 'u': // User handle redirects
        if (uri.pathSegments.elementAtOrNull(1)?.trim() case final handle? when handle.isNotEmpty) {
          AppLoading.run(() async {
            final code = await globalContainer.read(apiServiceProvider).getReferralCodeFromHandle(handle);
            if (code != null) {
              meNavigator.removeNamedAndPushAndRemoveUntil(
                Routes.socialProfile.name,
                arguments: Routes.socialProfile.d(code: code),
                predicate: (_) => true,
              );
            }
          });
        }
        return true;
      default:
        // Ignores payment callback links pattern 1.
        if (uri.pathSegments case ['payment', 'callback', ...]) {
          return true;
        }
        // Ignores payment callback links pattern 2.
        if (uri.pathSegments case [_, 'payment', 'callback', ...]) {
          return true;
        }

        final referralCode = uri.queryParameters['referral'];
        if (referralCode != null && referralCode.isNotEmpty) {
          meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.socialProfile.name,
            arguments: Routes.socialProfile.d(code: referralCode),
            predicate: (_) => true,
          );
          return true;
        }

        if (pathFirst != null && pathFirst.isNotEmpty) {
          final code = await globalContainer.read(apiServiceProvider).getReferralCodeFromHandle(pathFirst);
          if (code != null) {
            meNavigator.removeNamedAndPushAndRemoveUntil(
              Routes.socialProfile.name,
              arguments: Routes.socialProfile.d(code: code),
              predicate: (_) => true,
            );
            return true;
          }
        }

        TinyDialog.show(
          text: 'Please copy the link and open it in a browser app manually.',
          defaultButtonText: globalL10nME.copyButton,
          defaultButtonOnPressed: () {
            copyAndToast(uri.toString());
            meNavigator.maybePop();
          },
        );
        return true;
    }
  }
}

class ShortLinkHandler extends AppLinkHandler {
  const ShortLinkHandler();

  static Iterable<String> get _allowedHosts => [
    envUrlShort,
    envUrlShortApp,
  ].map((e) => e.replaceFirst(RegExp(r'https?://'), ''));

  @override
  bool onLink(Uri uri) {
    if (!_allowedHosts.contains(uri.host)) {
      return false;
    }

    final url = uri.toString();

    final params = CardHelper.extractActivateParams(url);
    final code = CardHelper.codeRegex.firstMatch(url)?.group(0);
    if (params == null && code == null) {
      return false;
    }

    if (params != null) {
      return CardHelper.handleCardActivation(url);
    }

    if (code != null) {
      meNavigator.removeNamedAndPushAndRemoveUntil(
        Routes.socialProfile.name,
        arguments: Routes.socialProfile.d(code: code),
        predicate: (_) => true,
      );
      return true;
    }

    return false;
  }
}

class SocialLinkHandler extends AppLinkHandler {
  const SocialLinkHandler();

  static String? _extractCodeFromUrl(Uri uri) {
    String? code;
    final url = uri.toString();
    if ((uri.scheme == AppLinkHelper.scheme || url.startsWith(envUrlSocial)) &&
        uri.pathSegments.elementAtOrNull(0) == 'profile') {
      code = CardHelper.codeRegex.firstMatch(uri.queryParameters['card_code'] ?? '')?.group(0);
    }
    if (code?.trim().isEmpty ?? false) {
      code = null;
    }
    return code;
  }

  @override
  bool onLink(Uri uri) {
    final code = _extractCodeFromUrl(uri);
    if (code == null) {
      return false;
    }
    postRun(() {
      meNavigator.removeNamedAndPushAndRemoveUntil(
        Routes.socialProfile.name,
        arguments: Routes.socialProfile.d(code: code),
        predicate: (_) => true,
      );
    });
    return true;
  }
}

class ReferralLinkHandlerNoAuth extends AppLinkHandler {
  const ReferralLinkHandlerNoAuth();

  @override
  bool availableFor(Uri uri, {Route? newRoute}) => routeAtLogin(newRoute);

  @override
  bool onLink(Uri uri) {
    UserReferralRequest? request;
    final url = uri.toString();
    if (url.startsWith(envUrlCard3) || url.startsWith(envUrlWebsite)) {
      final referralCode = uri.queryParameters['referral'];
      if (referralCode != null) {
        request = UserReferralRequest(referralCode: referralCode);
      }
      String? path = uri.pathSegments.elementAtOrNull(0);
      if (path == 'u') {
        path = uri.pathSegments.elementAtOrNull(1);
      }
      if (path?.trim() case final handle? when handle.isNotEmpty && !SchemeLinkHandler.methods.contains(handle)) {
        request = UserReferralRequest(handle: handle);
      }
    }
    if (request == null && CardHelper.isCard3Format(url)) {
      final params = CardHelper.extractActivateParams(url);
      if (params?.uid case final cardCode?) {
        request = UserReferralRequest(cardCode: cardCode);
      }
    }

    if (request == null || request.isEmpty) {
      return false;
    }

    void setRequest(UserReferralRequest request) {
      globalContainer.read(userReferralFromProvider.notifier).state = request;
    }

    if (request.referralCode != null || request.cardCode != null) {
      setRequest(request);
      return true;
    }

    AppLoading.run(
      () async {
        final referral = await globalContainer.read(apiServiceProvider).getReferralCodeFromHandle(request!.handle!);
        if (referral != null) {
          setRequest(UserReferralRequest(referralCode: referral));
        }
      },
      until: const Duration(seconds: 1),
    );

    return true;
  }
}

class ProfileLinkNoAuthHandler extends AppLinkHandler {
  const ProfileLinkNoAuthHandler();

  static String? _extractCodeFromUrlSync(Uri uri) {
    final url = uri.toString();
    final pathFirst = uri.pathSegments.elementAtOrNull(0);

    String? code;
    if ((uri.scheme == AppLinkHelper.scheme || url.startsWith(envUrlSocial)) && pathFirst == 'profile') {
      code = CardHelper.codeRegex.firstMatch(uri.queryParameters['card_code'] ?? '')?.group(0);
    } else if (ShortLinkHandler._allowedHosts.contains(uri.host)) {
      code = CardHelper.codeRegex.firstMatch(url)?.group(0);
    }
    if (code?.trim().isEmpty ?? false) {
      code = null;
    }
    return code;
  }

  static Future<String?> _extractCodeFromHandleAsync(String handle) {
    return AppLoading.run(
      () => globalContainer.read(apiServiceProvider).getReferralCodeFromHandle(handle),
      until: const Duration(seconds: 1),
    );
  }

  @override
  bool availableFor(Uri uri, {Route? newRoute}) => routeAtLogin(newRoute);

  @override
  bool onLink(Uri uri) {
    if (WebsiteLinkHandler._allowedHosts.contains(uri.host)) {
      String? path = uri.pathSegments.elementAtOrNull(0);
      if (path == 'u') {
        path = uri.pathSegments.elementAtOrNull(1);
      }
      if (path?.trim() case final handle? when handle.isNotEmpty && !SchemeLinkHandler.methods.contains(handle)) {
        Future<void>(() async {
          final code = await _extractCodeFromHandleAsync(handle);
          if (code != null) {
            _pushProfile(code);
          }
        });
        return true;
      }
      return false;
    }

    final code = _extractCodeFromUrlSync(uri);
    if (code == null) {
      return false;
    }

    _pushProfile(code);
    return true;
  }

  void _pushProfile(String code) {
    postRun(() {
      meNavigator.removeNamedAndPushAndRemoveUntil(
        Routes.socialProfile.name,
        arguments: Routes.socialProfile.d(code: code),
        predicate: (_) => true,
      );
    });
  }
}
