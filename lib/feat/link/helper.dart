import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_utils/me_utils.dart' show LogUtil;
import 'package:url_launcher/url_launcher.dart' show canLaunchUrl, launchUrl, LaunchMode;

import '/constants/envs.dart';
import '/internals/methods.dart' show copyAndToast, handleExceptions;
import '/routes/card3_routes.dart' show Routes;
import '/ui/widgets/dialog/tiny_dialog.dart';
import '/ui/widgets/toast.dart';

import 'handler.dart';

abstract class AppLinkHelper {
  static const _tag = '🔗 AppLinkHelper';

  static const scheme = 'card3';
  static final _$ = AppLinks();

  static final _handlers = <AppLinkHandler>[
    const SchemeLinkHandler(),
    const WebsiteLinkHandler(),
    const SocialLinkHandler(),
    const ShortLinkHandler(),
    const ProfileLinkNoAuth<PERSON>andler(),
    const ReferralLinkHandlerNoAuth(),
  ];

  static StreamSubscription<Uri>? _sub;
  static bool _startHandle = false;

  static Uri? pendingLink;
  static Uri? _launchedVerifiedLink;

  static Iterable<String> get allowedHosts => [
    envUrlCard3,
    envUrlSocial,
    envUrlShort,
    envUrlShortApp,
    envUrlWebsite,
  ].map((e) => Uri.parse(e).host);

  static void register() {
    _sub?.cancel();
    _sub = _$.uriLinkStream.listen(handleUri);
    meNavigatorObserver.addListener(
      MERouteListener(
        actions: MERouteAction.values.toSet(),
        onRouteChanged: (_, newRoute, _) {
          final previousStartHandle = _startHandle;
          final newRouteName = newRoute?.settings.name;
          _startHandle = newRouteName != null && newRouteName != Routes.root.name;
          if (pendingLink case final link? when _startHandle && !previousStartHandle) {
            handleUri(link, newRoute: newRoute);
          }
        },
      ),
    );
    _fetchInitialLink();
  }

  static void _fetchInitialLink() {
    _$.getInitialLink().then(
      (link) {
        if (link != null) {
          LogUtil.d(
            'Initial link: $link',
            tag: _tag,
            tagWithTrace: false,
          );
          pendingLink ??= link;
        }
      },
      onError: (e, s) {
        handleExceptions(error: e, stackTrace: s);
      },
    );
  }

  static void addHandler(AppLinkHandler handler) {
    _handlers.add(handler);
  }

  static void removeHandler(AppLinkHandler handler) {
    _handlers.remove(handler);
  }

  static Future<bool> handleUri(Uri uri, {Route? newRoute}) async {
    if (!_startHandle) {
      LogUtil.d(
        'Pending AppLink: $uri',
        tag: _tag,
        tagWithTrace: false,
      );

      pendingLink = uri;
      return false;
    }

    LogUtil.d(
      'Incoming AppLink: $uri',
      tag: _tag,
      tagWithTrace: false,
    );

    Future<bool> launchIfApplicable() async {
      // Fallback to launch the URL with external application.
      if (await canLaunchUrl(uri)) {
        LogUtil.w(
          'Link handled by native launching\n$uri',
          tag: _tag,
          tagWithTrace: false,
        );
        launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      }
      return false;
    }

    // Launch external links if applicable.
    if (uri.scheme != AppLinkHelper.scheme && !allowedHosts.contains(uri.host)) {
      pendingLink = null;
      LogUtil.w(
        'Link is neither match the scheme (${AppLinkHelper.scheme}) nor in the allowed host list.'
        '\n$uri',
        tag: _tag,
        tagWithTrace: false,
      );
      return launchIfApplicable();
    }

    final availableHandlers = _handlers.where(
      (handler) => handler.availableFor(uri, newRoute: newRoute),
    );
    if (availableHandlers.isEmpty) {
      LogUtil.w(
        'Pending link but no handlers are available.'
        '\n$uri',
        tag: _tag,
        tagWithTrace: false,
      );
      return false;
    }

    pendingLink = null;
    for (final handler in availableHandlers) {
      try {
        final bool handled;
        final result = handler.onLink(uri);
        if (result is Future<bool>) {
          handled = await result.timeout(const Duration(seconds: 30));
        } else {
          handled = result;
        }
        if (handled) {
          LogUtil.d(
            'Link handled by ${handler.runtimeType}\n$uri',
            tag: _tag,
            tagWithTrace: false,
          );
          return true;
        }
      } catch (e) {
        Card3ToastUtil.showToast(message: 'Failed to handle the link.');
        rethrow;
      }
    }

    if (_launchedVerifiedLink == uri) {
      LogUtil.w(
        'Link not handled but launched again causing loop\n$uri',
        tag: _tag,
        tagWithTrace: false,
      );
      TinyDialog.show(
        text:
            "The Card3 app cannot handle the link, yet it's been relaunched with the app again.\n"
            'Please copy the link and open it in a browser app manually.',
        defaultButtonText: globalL10nME.copyButton,
        defaultButtonOnPressed: () {
          copyAndToast(uri.toString());
          meNavigator.maybePop();
        },
      );
      return false;
    }

    await postRun(() {});
    final confirmLaunchExternally = await TinyDialog.show(
      text:
          'The link cannot be handled by the Card3 app.\n'
          'Would you like to open it with external applications?',
      captionText: uri.toString(),
      buttonsBuilder: (context) => const TinyDialogButtonGroup(),
    );
    if (confirmLaunchExternally && await launchIfApplicable()) {
      _launchedVerifiedLink = uri;
      LogUtil.w(
        'Link not handled but launched\n$uri',
        tag: _tag,
        tagWithTrace: false,
      );
      return true;
    }

    LogUtil.w(
      'Link not handled\n$uri',
      tag: _tag,
      tagWithTrace: false,
    );
    return false;
  }

  // 清理资源
  static void dispose() {
    _sub?.cancel();
  }
}
