import 'dart:async' show Completer;
import 'dart:convert' show jsonEncode;
import 'dart:io' as io show Platform;

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:me_analytics/me_analytics.dart';
import 'package:me_misc/me_misc.dart' show MENavigatorObserver;
import 'package:me_utils/me_utils.dart' show PackageUtil;
import 'package:uuid/uuid.dart';

import '/constants/release.dart' show Release;
import '/feat/firebase/options.dart' show firebaseApp;
import '/internals/box.dart' show BoxService;

final class MEAnalyticsFirebaseImpl extends IMEAnalytics {
  MEAnalyticsFirebaseImpl() {
    _init();
  }

  final _$ = FirebaseAnalytics.instanceFor(app: firebaseApp);

  static const _uuid = Uuid();

  final _initializeLock = Completer<void>();

  void _init() {
    Future<void>(() async {
      await _$.setAnalyticsCollectionEnabled(true);
      await _$.setDefaultEventParameters({
        'app_build': PackageUtil.buildString,
        'app_channel': Release.channel,
        'app_platform': io.Platform.operatingSystem,
      });
    }).then(_initializeLock.complete).catchError(_initializeLock.completeError);
  }

  String _formalizeName(String name) {
    return name.replaceAll(RegExp(r'[-\\.]+'), '_');
  }

  void _injectParameters(Map<String, Object?>? parameters) {
    if (parameters == null) {
      return;
    }
    final user = BoxService.getUserInfo();
    parameters.addAll({
      'app_user_email': user?.userEmail,
      'app_user_referral_code': user?.referralCode,
    });
  }

  @override
  Future<String> logEvent(
    String name, {
    Map<String, Object?>? parameters,
    String? type,
  }) async {
    await _initializeLock.future;
    name = _formalizeName(name);
    _injectParameters(parameters);
    final uuid = _uuid.v7();
    if (type == MENavigatorObserver.analyticsTypeRouteChanged) {
      await _$.logScreenView(
        screenName: name,
        parameters: buildParameters(parameters, type: type),
      );
    } else {
      await _$.logEvent(
        name: name,
        parameters: buildParameters(parameters, type: type),
      );
    }
    return uuid;
  }

  @override
  Map<String, Object>? formalizeParameters(Map<String, Object?>? parameters) {
    final map = super.formalizeParameters(parameters);

    Object write(Object v) {
      if (v is Map) {
        v.removeWhere((k, v) => k == null || v == null);
        final map = v.map((k, v) => MapEntry(write(k), write(v)));
        return jsonEncode(map);
      }

      if (v is Iterable) {
        return jsonEncode(v.nonNulls.map(write).toList());
      }

      if (v is num || v is String) {
        return v;
      }

      return v.toString();
    }

    map?.updateAll((key, value) {
      return write(value);
    });

    return map;
  }
}
