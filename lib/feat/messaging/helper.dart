import 'dart:async';
import 'dart:io' as io show Platform;

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/widgets.dart' show AppLifecycleListener, AppLifecycleState;
import 'package:me_misc/me_misc.dart';
import 'package:me_utils/me_utils.dart';

import '/constants/envs.dart' show isSealed, envBotUtilFCMUrl, envBotUtilFCMKey;
import '/constants/release.dart' show Release;
import '/feat/firebase/options.dart';
import '/feat/notification/handler.dart' show handleNotificationOpenWithPayload;
import '/internals/methods.dart' show handleExceptions;
import '/internals/riverpod.dart';
import '/models/user.dart';
import '/provider/api.dart' show apiServiceProvider, retryWith;
import '/routes/card3_routes.dart' show Routes;
import 'handler.dart';

abstract class MessagingHelper {
  static const _tag = '📨 MessagingHelper';

  static FirebaseMessaging get _$ => FirebaseMessaging.instance;

  static String? get token => _token;
  static String? _token;
  static final _tokenLocks = <String, Completer<void>>{};

  static bool get hasPermissions => _hasPermissions;
  static const bool _hasPermissions = true;

  static bool _startHandle = false;
  static RemoteMessage? _pendingMessage;

  static AppLifecycleState get lifecycleState => _lifecycleState;
  static AppLifecycleState _lifecycleState = AppLifecycleState.resumed;

  static Future<void> initialize() async {
    AppLifecycleListener(onStateChange: (state) => _lifecycleState = state);
    addHandler(const MessagingNotificationHandler());
    FirebaseMessaging.onMessage.listen(_handleMessage);
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpen);
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    _fetchInitialMessage();
    _$.onTokenRefresh.listen(
      _handleTokenRefresh,
      onError: (e, s) {
        handleExceptions(
          error: e,
          stackTrace: s,
          tag: _tag,
          tagWithTrace: false,
        );
      },
    );
    meNavigatorObserver.addListener(
      MERouteListener(
        actions: MERouteAction.values.toSet(),
        onRouteChanged: (_, newRoute, _) {
          final previousStartHandle = _startHandle;
          _startHandle =
              meNavigatorObserver.history.any(
                (route) => route.settings.name == Routes.home.name,
              ) ||
              newRoute?.settings.name == Routes.home.name;
          if (_startHandle && !previousStartHandle) {
            _requestPermissions();
            uploadTokenIfAvailable(token: _token);
          }
          if (_pendingMessage case final link? when _startHandle && !previousStartHandle) {
            _handleMessage(link);
            _pendingMessage = null;
          }
        },
      ),
    );

    String? token;
    try {
      if (io.Platform.isIOS || io.Platform.isMacOS) {
        final apns = await _$.getAPNSToken();
        if (apns == null) {
          return;
        }
      }
      token = _token = await retryWith(
        () => _$.getToken().timeout(const Duration(seconds: 30)),
        retryTimes: 3,
      );
      if (token == null) {
        return;
      }
    } on FirebaseException catch (e) {
      handleExceptions(error: e, tag: _tag, tagWithTrace: false);
      return;
    }
    _handleTokenRefresh(token);
    _fetchInitialMessage();
  }

  static Future<void> uploadTokenIfAvailable({String? token}) async {
    token ??= _token;
    if (token == null) {
      return;
    }
    if (_tokenLocks[token] case final lock?) {
      return lock.future;
    }
    final lock = Completer<void>();
    _tokenLocks[token] = lock;

    globalContainer
        .read(apiServiceProvider)
        .updateSettings(UserSettingsRequest.fromPlatform(fcm: token))
        .then(
          lock.complete,
          onError: (e, s) {
            lock.completeError(e, s);
            _tokenLocks.remove(token);
          },
        );
    return lock.future;
  }

  static Future<void> _requestPermissions() {
    return Future.wait([
      _$.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: true,
      ),
      // TODO: Handles more permissions.
      // [
      //   // if (Platform.isAndroid) Permission.systemAlertWindow,
      // ].request(),
    ]);
  }

  static void _fetchInitialMessage() {
    _$.getInitialMessage().then(
      (message) {
        if (message != null) {
          _pendingMessage ??= message;
        }
      },
      onError: (e, s) {
        handleExceptions(error: e, stackTrace: s);
      },
    );
  }

  static Future<void> _handleTokenRefresh(String newToken) async {
    if (newToken == _token) {
      return;
    }
    LogUtil.d('FCM token refreshed: $newToken', tag: _tag, tagWithTrace: false);
    if (_startHandle) {
      uploadTokenIfAvailable(token: newToken);
    } else {
      _token = newToken;
    }
  }

  static final _messageHandlers = <MessageHandler>{};

  static void addHandler(MessageHandler handler) {
    _messageHandlers.add(handler);
  }

  static void removeHandler(MessageHandler handler) {
    _messageHandlers.remove(handler);
  }

  static void _logRemoteMessage(RemoteMessage remoteMessage) {
    LogUtil.dd(
      () {
        final buffer = StringBuffer('FCM Remote message:\n');
        for (final MapEntry(:key, :value) in remoteMessage.toMap().entries) {
          buffer.writeln('—— [$key]: $value');
        }
        return buffer.toString();
      },
      tag: _tag,
      tagWithTrace: false,
      report: !isSealed,
      reportOverrideUri: Uri.parse(envBotUtilFCMUrl),
      reportOverrideSigningKey: envBotUtilFCMKey,
    );
  }

  static bool _handleMessage(RemoteMessage remoteMessage) {
    _logRemoteMessage(remoteMessage);
    for (final handler in _messageHandlers) {
      try {
        if (handler.onRemoteMessage(remoteMessage)) {
          return true;
        }
      } catch (e, s) {
        handleExceptions(
          error: e,
          stackTrace: s,
          tag: _tag,
          tagWithTrace: false,
        );
      }
    }
    return false;
  }

  static void _handleMessageOpen(RemoteMessage remoteMessage) {
    if (remoteMessage.data['data'] case final data? when data.isNotEmpty) {
      handleNotificationOpenWithPayload(data);
    }
  }
}

@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage remoteMessage) async {
  // If you're going to use other Firebase services in the background,
  // such as Firestore, make sure you call `initializeApp` before using other
  // Firebase services.
  final isIsolate = !io.Platform.isIOS && !io.Platform.isMacOS;
  await Future.wait([
    if (isIsolate) ...[
      DeviceUtil.initDeviceInfo(),
      PackageUtil.initInfo(
        buildTime: Release.buildTime,
        appVersionName: Release.versionName,
        appVersionCode: Release.versionCode,
      ),
    ],
    Firebase.initializeApp(options: firebaseOptionsByFlavor),
  ]);
  if (isIsolate) {
    MessagingHelper.addHandler(
      const MessagingNotificationHandler(postNotificationIfAvailable: false),
    );
  }
  MessagingHelper._handleMessage(remoteMessage);
}
