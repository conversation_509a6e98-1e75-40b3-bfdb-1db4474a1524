import 'dart:convert' show jsonEncode;

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/widgets.dart' show AppLifecycleState;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:me_misc/me_misc.dart';

import '/feat/notification/helper.dart' show NotificationHelper;
import 'helper.dart';

abstract class MessageHandler {
  const MessageHandler();

  AppLifecycleState get appLifecycleState => MessagingHelper.lifecycleState;

  bool onRemoteMessage(RemoteMessage remoteMessage);
}

class MessagingNotificationHandler extends MessageHandler {
  const MessagingNotificationHandler({
    this.postNotificationIfAvailable = true,
  });

  final bool postNotificationIfAvailable;

  @override
  bool onRemoteMessage(RemoteMessage remoteMessage) {
    final notification = remoteMessage.notification;
    if (notification == null) {
      return false;
    }
    // Do nothing when not allowed to post notification.
    if (!postNotificationIfAvailable) {
      return true;
    }
    // Only handle when the app is at the foreground.
    if (appLifecycleState != AppLifecycleState.resumed) {
      return true;
    }
    Future(() async {
      final nAndroid = notification.android, nDarwin = notification.apple;
      final channelAndroid = NotificationHelper.channels.firstWhere(
        (e) => e.id == nAndroid?.channelId,
        orElse: () => NotificationHelper.channelNotification,
      );
      final largeIconFileAndroid = switch (nAndroid?.imageUrl) {
        final url? => await CacheImageProvider.getCachedFileIfExists(url),
        _ => null,
      };
      NotificationHelper.postNotification(
        title: notification.title,
        body: notification.body,
        payload: switch (remoteMessage.data['data']) {
          final String data when data.trim().isNotEmpty => data.trim(),
          final data? => jsonEncode(data),
          null => null,
        },
        channelAndroid: channelAndroid,
        detailsAndroid: AndroidNotificationDetails(
          channelAndroid.id,
          channelAndroid.name,
          largeIcon: largeIconFileAndroid != null ? FilePathAndroidBitmap(largeIconFileAndroid.path) : null,
          priority: Priority.values.firstWhere(
            (e) => e.index == nAndroid?.priority.index,
            orElse: () => Priority.defaultPriority,
          ),
          visibility: switch (nAndroid?.visibility) {
            AndroidNotificationVisibility.public => NotificationVisibility.public,
            AndroidNotificationVisibility.private => NotificationVisibility.private,
            AndroidNotificationVisibility.secret => NotificationVisibility.secret,
            null => null,
          },
          tag: nAndroid?.tag,
          ticker: nAndroid?.ticker,
        ),
        detailsDarwin: DarwinNotificationDetails(
          subtitle: nDarwin?.subtitle,
          badgeNumber: int.tryParse(nDarwin?.badge ?? ''),
          sound: nDarwin?.sound?.name,
          criticalSoundVolume: nDarwin?.sound?.volume.toDouble(),
          interruptionLevel: nDarwin?.sound?.critical == true ? InterruptionLevel.critical : null,
        ),
        postWhenAppLifecycles: {AppLifecycleState.resumed},
      );
    });
    return true;
  }
}
