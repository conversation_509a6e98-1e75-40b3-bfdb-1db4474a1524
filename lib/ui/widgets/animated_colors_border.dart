import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart';

class AnimatedColorsBorder extends StatefulWidget {
  const AnimatedColorsBorder({
    super.key,
    required this.animating,
    required this.child,
    this.borderRadius,
    this.width = 4.0,
    this.reverse = false,
  });

  final bool animating;
  final Widget child;
  final BorderRadius? borderRadius;
  final double width;
  final bool reverse;

  @override
  State<AnimatedColorsBorder> createState() => _AnimatedColorsBorderState();
}

class _AnimatedColorsBorderState extends State<AnimatedColorsBorder> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    if (widget.animating) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(AnimatedColorsBorder oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.animating != oldWidget.animating) {
      if (widget.animating) {
        _controller.repeat();
      } else {
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeColor = context.themeColor;
    if (widget.animating) {
      return AnimatedBuilder(
        animation: _controller,
        builder: (context, child) => CustomPaint(
          painter: _AnimatedBorderPainter(
            themeColor: themeColor,
            animation: _controller,
            borderRadius: widget.borderRadius ?? context.meTheme.borderRadius,
            reverse: widget.reverse,
            width: widget.width,
          ),
          child: child,
        ),
        child: widget.child,
      );
    }
    return widget.child;
  }
}

class _AnimatedBorderPainter extends CustomPainter {
  const _AnimatedBorderPainter({
    required this.themeColor,
    required this.animation,
    required this.reverse,
    required this.width,
    this.borderRadius,
  }) : super(repaint: animation);

  final Color themeColor;
  final Animation<double> animation;
  final bool reverse;
  final double width;
  final BorderRadius? borderRadius;

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final path = Path();
    if (borderRadius case final br?) {
      path.addRRect(
        RRect.fromRectAndCorners(
          rect,
          topLeft: br.topLeft,
          topRight: br.topRight,
          bottomLeft: br.bottomLeft,
          bottomRight: br.bottomRight,
        ),
      );
    } else {
      path.addRect(rect);
    }

    final gradientColors = [
      themeColor,
      themeColor,
      Colors.white,
      themeColor,
      themeColor,
      Colors.white,
      themeColor,
      themeColor,
    ];

    final gradientStops = List<double>.generate(
      gradientColors.length,
      (index) => index / (gradientColors.length - 1),
    );

    final gradient = SweepGradient(
      center: Alignment.center,
      startAngle: 0.0,
      endAngle: 2 * math.pi,
      colors: gradientColors,
      stops: gradientStops,
      transform: GradientRotation(
        animation.value * 2 * math.pi * (reverse ? -1 : 1),
      ),
    );

    final paint = Paint()
      ..shader = gradient.createShader(rect)
      ..strokeWidth = width
      ..style = PaintingStyle.stroke;

    for (final metric in path.computeMetrics()) {
      final extractPath = metric.extractPath(0, metric.length);
      canvas.drawPath(extractPath, paint);
    }
  }

  @override
  bool shouldRepaint(_AnimatedBorderPainter oldDelegate) => true;
}
