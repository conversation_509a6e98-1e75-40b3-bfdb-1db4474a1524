import 'package:flutter/material.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_ui/me_ui.dart' show MEImage;

import '/internals/methods.dart' show handleExceptions;
import '/models/user.dart' show UserWithAvatar;

class UserAvatar extends StatelessWidget {
  const UserAvatar({
    super.key,
    required this.user,
    this.dimension,
    this.fit = BoxFit.cover,
    this.emptyBuilder,
    this.errorBuilder,
    this.borderRadius,
    this.backgroundColor,
    this.placeholderColor,
    this.placeholderPadding,
    this.heroTag,
  });

  final UserWithAvatar? user;
  final double? dimension;
  final BoxFit fit;
  final WidgetBuilder? emptyBuilder;
  final ImageErrorWidgetBuilder? errorBuilder;
  final BorderRadiusGeometry? borderRadius;
  final Color? backgroundColor;
  final Color? placeholderColor;
  final EdgeInsetsGeometry? placeholderPadding;
  final String? heroTag;

  @override
  Widget build(BuildContext context) {
    Widget child = MEImage(
      user?.avatar ?? '',
      clipOval: borderRadius == null,
      width: dimension,
      height: dimension,
      cacheWidth: dimension?.toCache(context),
      backgroundColor: backgroundColor ?? Theme.of(context).dividerColor,
      borderRadius: borderRadius ?? BorderRadius.zero,
      fit: fit,
      emptyBuilder: emptyBuilder ?? _buildPlaceholder,
      errorBuilder:
          errorBuilder ??
          (context, e, s) {
            handleExceptions(error: e, stackTrace: s);
            return _buildPlaceholder(context);
          },
    );
    if (heroTag case final heroTag?) {
      child = Hero(tag: heroTag, child: child);
    }
    return child;
  }

  Widget _buildPlaceholder(BuildContext context) {
    return FittedBox(
      fit: fit,
      child: Padding(
        padding: placeholderPadding ?? EdgeInsets.zero,
        child: Icon(Icons.account_circle, color: placeholderColor),
      ),
    );
  }
}
