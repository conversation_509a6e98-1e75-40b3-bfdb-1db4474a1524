import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/card.dart' show SocialPlatform;
import '/provider/card.dart' show fetchMyCardsProvider;
import 'data.dart' show SocialSvgIcon;

class SocialGrid extends ConsumerWidget {
  const SocialGrid({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final result = ref.watch(fetchMyCardsProvider);
    return result.when(
      data: (data) {
        final socialPlatforms = _filterSocialPlatforms(data);
        return GridView.builder(
          padding: const EdgeInsets.only(bottom: 40),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: 1.3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: socialPlatforms.length,
          itemBuilder: (context, index) {
            final platform = socialPlatforms[index];
            return GestureDetector(
              onTap: () {
                context.navigator.pushReplacementNamed(
                  Routes.socialPlatform.name,
                  arguments: Routes.socialPlatform.d(platform: platform),
                );
              },
              child: SocialSvgIcon(
                platform: platform,
                clipOval: false,
                backgroundColor: theme.colorScheme.surface,
              ),
            );
          },
        );
      },
      loading: () => const AppLoading(),
      error: (e, s) => EmptyView(
        onTap: () => ref.invalidate(fetchMyCardsProvider),
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      ),
    );
  }

  List<SocialPlatform> _filterSocialPlatforms(List<dynamic>? cards) {
    if (cards == null || cards.isEmpty) {
      return SocialPlatform.values;
    }

    return SocialPlatform.values.where((platform) {
      final events = platform.events;
      // 如果平台没有event限制，或者用户有匹配的卡片
      return events == null || events.any((e) => cards.any((c) => c.eventId == e));
    }).toList();
  }
}
