import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:me_extensions/me_extensions.dart';

import '/models/card.dart' show SocialPlatform;

/// 自定义SVG图标组件
class SocialSvgIcon extends StatelessWidget {
  const SocialSvgIcon({
    super.key,
    required this.platform,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(16.0)),
    this.clipOval = true,
    this.backgroundColor,
  });

  final SocialPlatform platform;
  final double? width;
  final double? height;
  final BorderRadiusGeometry borderRadius;
  final bool clipOval;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    Widget child = SvgPicture.asset(
      'assets/icons/social/${platform.iconImageKey}.svg',
      fit: BoxFit.cover,
    );
    if (clipOval) {
      child = ClipOval(child: child);
    }
    child = Container(
      clipBehavior: Clip.antiAlias,
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: clipOval ? null : borderRadius,
        border: platform.borderColor?.run((it) => Border.all(color: it)),
        color: backgroundColor,
      ),
      child: child,
    );
    return child;
  }
}

/// 自定义SVG图标组件
class SocialDemoImage extends StatelessWidget {
  const SocialDemoImage({
    super.key,
    required this.name,
  });

  final String name;

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      'assets/icons/social/demo/$name.png',
    );
  }
}
