import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/provider/api.dart' show apiServiceProvider;
import '/provider/card.dart' show fetchPublicProfileProvider;
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;

class DescriptionActionSheet extends ConsumerStatefulWidget {
  const DescriptionActionSheet({super.key});

  @override
  ConsumerState<DescriptionActionSheet> createState() => _DescriptionState();
}

class _DescriptionState extends ConsumerState<DescriptionActionSheet> {
  final _cancelToken = CancelToken();
  final _formKey = GlobalKey<FormState>();

  late final TextEditingController nameController;
  late final TextEditingController titleController;
  late final TextEditingController companyController;

  bool _isLoading = false, _isClearing = false, _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _fetchUserInfo();
  }

  @override
  void dispose() {
    _cancelToken.cancel();
    nameController.dispose();
    titleController.dispose();
    companyController.dispose();
    super.dispose();
  }

  Future<void> _fetchUserInfo() async {
    final localUser = ref.read(userRepoProvider);
    nameController = TextEditingController(text: localUser?.name);
    titleController = TextEditingController(text: localUser?.title);
    companyController = TextEditingController(text: localUser?.company);

    final userInfo = await ref.read(fetchUserInfoProvider().future);
    safeSetState(() {
      nameController.text = userInfo.name;
      titleController.text = userInfo.title;
      companyController.text = userInfo.company;
    });
  }

  String? _validateName(String? value) {
    value = value?.trim() ?? '';

    if (value.isEmpty) {
      return 'Name is required';
    }

    if (value.length > 40) {
      return 'Name length should be less than 40';
    }

    return null;
  }

  String? _validateTitle(String? value) {
    value = value?.trim() ?? '';

    if (value.isEmpty) {
      return null;
    }

    if (value.length > 60) {
      return 'Title length should be less than 60';
    }

    return null;
  }

  String? _validateCompany(String? value) {
    value = value?.trim() ?? '';

    if (value.isEmpty) {
      return null;
    }

    if (value.length > 80) {
      return 'Title length should be less than 80';
    }

    return null;
  }

  Future<void> _handleSubmit() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
      _isSubmitting = true;
    });

    try {
      await ref
          .read(apiServiceProvider)
          .updateUserInfo(
            name: nameController.text.trim(),
            title: titleController.text.trim(),
            company: companyController.text.trim(),
            cancelToken: _cancelToken,
          );

      if (mounted) {
        ref.invalidate(fetchUserInfoProvider);
        if (ref.read(userRepoProvider) case final user?) {
          ref.invalidate(fetchPublicProfileProvider(code: user.cardCode));
          ref.invalidate(fetchPublicProfileProvider(code: user.referralCode));
        }
        Navigator.pop(context);
        Card3ToastUtil.showToast(message: ToastMessages.updated);
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToUpdate);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
        _isSubmitting = false;
      });
    }
  }

  Future<void> _handleClear() async {
    setState(() {
      _isLoading = true;
      _isClearing = true;
    });

    try {
      await ref
          .read(apiServiceProvider)
          .updateUserInfo(
            // name: '', // DO NOT CLEAR NAME.
            title: '',
            company: '',
            cancelToken: _cancelToken,
          );
      if (mounted) {
        ref.invalidate(fetchUserInfoProvider);
        if (ref.read(userRepoProvider) case final user?) {
          ref.invalidate(fetchPublicProfileProvider(code: user.cardCode));
          ref.invalidate(fetchPublicProfileProvider(code: user.referralCode));
        }
        Navigator.pop(context);
        Card3ToastUtil.showToast(message: ToastMessages.profileCleared);
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToClearProfile);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
        _isClearing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final fillColor = context.colorScheme.surface;
    return Padding(
      padding: const EdgeInsets.all(16).copyWith(
        bottom: MediaQuery.paddingOf(context).bottom.max(24.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Edit Profile',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, size: 24.0),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Expanded(
            child: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Stack(
                        children: [
                          TextFormField(
                            controller: nameController,
                            validator: _validateName,
                            onChanged: (_) => _formKey.currentState?.validate(),
                            style: const TextStyle(fontSize: 24),
                            decoration: InputDecoration(
                              fillColor: fillColor,
                              hintText: 'Name',
                            ),
                          ),
                          _buildCounter(context, nameController, 40),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Stack(
                        children: [
                          TextFormField(
                            controller: titleController,
                            validator: _validateTitle,
                            onChanged: (_) => _formKey.currentState?.validate(),
                            style: const TextStyle(fontSize: 24),
                            decoration: InputDecoration(
                              fillColor: fillColor,
                              hintText: 'Title (Optional)',
                            ),
                          ),
                          _buildCounter(context, titleController, 60),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Stack(
                        children: [
                          TextFormField(
                            controller: companyController,
                            validator: _validateCompany,
                            onChanged: (_) => _formKey.currentState?.validate(),
                            style: const TextStyle(fontSize: 24),
                            decoration: InputDecoration(
                              fillColor: fillColor,
                              hintText: 'Company (Optional)',
                            ),
                          ),
                          _buildCounter(context, companyController, 80),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          ThemeTextButtonGroup(
            fallbackToDefaultCancel: false,
            onCancel: _isLoading ? null : _handleClear,
            cancelFlex: 1,
            cancelThemeColor: context.meTheme.failingColor,
            cancelChild: _isClearing ? const AppLoading() : const Text('Clear'),
            fallbackToDefaultConfirm: false,
            onConfirm: _isLoading ? null : _handleSubmit,
            confirmChild: _isSubmitting ? const AppLoading() : Text(context.l10nME.submitButton),
          ),
        ],
      ),
    );
  }

  Widget _buildCounter(BuildContext context, TextEditingController controller, int maxLength) {
    return ValueListenableBuilder(
      valueListenable: controller,
      builder: (context, value, _) {
        final length = value.text.characters.length;
        return AnimatedPositionedDirectional(
          duration: kThemeAnimationDuration,
          end: 16.0,
          bottom: length > maxLength ? 0.0 : 3.0,
          child: AnimatedOpacity(
            duration: kThemeAnimationDuration,
            opacity: length < maxLength - 10 ? 0.0 : 1.0,
            child: AnimatedDefaultTextStyle(
              duration: kThemeAnimationDuration,
              style: context.textTheme.bodyMedium!.copyWith(
                color: length > maxLength ? context.meTheme.failingColor : Colors.white24,
                fontSize: length > maxLength ? 12.0 : 10.0,
              ),
              child: Text(
                '$length/$maxLength',
                textAlign: TextAlign.end,
              ),
            ),
          ),
        );
      },
    );
  }
}
