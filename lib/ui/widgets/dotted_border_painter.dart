import 'package:flutter/material.dart';

import '/res/colors.gen.dart' show ColorName;

class DottedBorderPainter extends CustomPainter {
  const DottedBorderPainter({
    super.repaint,
    required this.length,
    required this.spacing,
    this.thickness = 1.0,
    this.color,
    this.borderRadius,
  });

  final double length;
  final double spacing;
  final double thickness;
  final Color? color;
  final double? borderRadius;

  @override
  bool shouldRepaint(DottedBorderPainter oldDelegate) =>
      length != oldDelegate.length ||
      spacing != oldDelegate.spacing ||
      thickness != oldDelegate.thickness ||
      color != oldDelegate.color ||
      borderRadius != oldDelegate.borderRadius;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color ?? ColorName.dividerColorDark
      ..strokeWidth = thickness
      ..style = PaintingStyle.stroke;

    final path = Path();
    if (borderRadius case final br?) {
      path.addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(0, 0, size.width, size.height),
          Radius.circular(br),
        ),
      );
    } else {
      path.addRect(
        Rect.fromLTWH(0, 0, size.width, size.height),
      );
    }

    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final extractPath = pathMetric.extractPath(
          distance,
          distance + length,
        );
        canvas.drawPath(extractPath, paint);
        distance += length + spacing;
      }
    }
  }
}
