import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/card.dart';
import '/provider/api.dart';
import '/provider/card.dart';
import '/ui/widgets/common/error_widget.dart';
import '/ui/widgets/social/data.dart';

class TopicWidget extends ConsumerStatefulWidget {
  const TopicWidget({super.key});

  @override
  ConsumerState<TopicWidget> createState() => _TopicWidgetState();
}

class _TopicWidgetState extends ConsumerState<TopicWidget> {
  final _customTopicController = TextEditingController();

  @override
  void dispose() {
    _customTopicController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final result = ref.watch(fetchExtendProfileProvider());
    final Widget child;
    if (result.hasValue) {
      final profile = result.valueOrNull;
      child = Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          spacing: 8.0,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildIdentitySection(profile),
            _buildTalkToMeAboutSection(profile),
            _buildGithubSection(profile),
          ],
        ),
      );
    } else if (result.hasError && !result.isLoading) {
      child = NetworkErrorWidget(
        onRefresh: () => ref.invalidate(fetchExtendProfileProvider),
      );
    } else {
      child = const Gap.v(8.0);
    }
    return AnimatedSize(
      duration: kThemeAnimationDuration,
      curve: Curves.easeInOutCubic,
      child: child,
    );
  }

  Widget _buildIdentitySection(ExtendProfile? profile) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        color: context.theme.cardColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Text(
                  'I am a/an...',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => _showIdentitiesBottomSheet(
                  profile?.roles ?? [],
                ),
                child: Container(
                  width: 36,
                  height: 36,
                  padding: const EdgeInsets.all(6.0),
                  decoration: BoxDecoration(
                    color: ColorName.primaryTextColorLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Icon(Icons.add, color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (profile?.roles case final roles? when roles.isNotEmpty)
            GestureDetector(
              onTap: () => _showIdentitiesBottomSheet(roles),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: roles
                    .map(
                      (role) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: ColorName.themeColorDark,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          role,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            )
          else
            GestureDetector(
              onTap: () => _showIdentitiesBottomSheet([]),
              child: Text(
                'Developer, Investor, Founder...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTalkToMeAboutSection(ExtendProfile? profile) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        color: context.theme.cardColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Text(
                  'Talk to Me About',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => _showTopicsBottomSheet(profile?.topics ?? []),
                child: Container(
                  width: 36,
                  height: 36,
                  padding: const EdgeInsets.all(6.0),
                  decoration: BoxDecoration(
                    color: ColorName.primaryTextColorLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Icon(Icons.add, color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (profile?.topics case final topics? when topics.isNotEmpty)
            GestureDetector(
              onTap: () => _showTopicsBottomSheet(topics),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: topics
                    .map(
                      (topic) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: ColorName.themeColorDark,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          topic,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            )
          else
            GestureDetector(
              onTap: () => _showTopicsBottomSheet([]),
              child: Text(
                'ZK, DeFi, Layer2...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGithubSection(ExtendProfile? profile) {
    final hasProfile = profile?.githubHandle.isNotEmpty == true;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        color: context.theme.cardColor,
      ),
      child: Column(
        spacing: 8.0,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            spacing: 8.0,
            children: [
              const Expanded(
                child: Text(
                  'Github Contribution',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (hasProfile)
                GestureDetector(
                  onTap: _navigateToGithubEdit,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: ColorName.primaryTextColorLight,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
            ],
          ),
          GestureDetector(
            onTap: _navigateToGithubEdit,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: hasProfile ? 8.0 : 2.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.0),
                color: hasProfile ? ColorName.primaryTextColorLight : null,
              ),
              child: Row(
                spacing: 4.0,
                children: [
                  if (hasProfile)
                    Padding(
                      padding: const EdgeInsetsDirectional.only(start: 10.0, end: 6.0, top: 4.0, bottom: 4.0),
                      child: SocialSvgIcon(
                        platform: SocialPlatform.github,
                        width: 32.0,
                        height: 32.0,
                        borderRadius: BorderRadius.circular(8.0),
                        clipOval: true,
                      ),
                    )
                  else
                    const SocialSvgIcon(
                      platform: SocialPlatform.github,
                      width: 20.0,
                      height: 20.0,
                      clipOval: true,
                    ),
                  Expanded(
                    child: Text(
                      hasProfile ? profile!.githubHandle : 'Add GitHub username',
                      style: TextStyle(
                        color: hasProfile ? Colors.white : Colors.grey[600],
                        fontSize: hasProfile ? 18 : 16,
                        fontWeight: hasProfile ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showIdentitiesBottomSheet(List<String> selectedIdentities) {
    ScrollableBottomSheet.show(
      builder: (context) => TopicsSheet(
        title: 'I am a/an...',
        provider: fetchExtendProfileRolesProvider,
        selected: List.from(selectedIdentities),
        onSubmit: (selected) async {
          await ref.read(apiServiceProvider).updateExtendRoles(roles: selected.join(','));
          ref.invalidate(fetchExtendProfileProvider);
        },
      ),
    );
  }

  void _showTopicsBottomSheet(List<String> selectedTopics) {
    ScrollableBottomSheet.show(
      builder: (context) => TopicsSheet(
        title: 'Talk to Me About',
        provider: fetchExtendProfileTopicsProvider,
        selected: List.from(selectedTopics),
        onSubmit: (selected) async {
          await ref.read(apiServiceProvider).updateExtendTopics(topics: selected.join(','));
          ref.invalidate(fetchExtendProfileTopicsProvider);
        },
      ),
    );
  }

  void _navigateToGithubEdit() {
    // 跳转到social页面，传递ethcc_github参数和平台信息
    final profile = ref.read(fetchExtendProfileProvider()).valueOrNull;
    Navigator.of(context).pushNamed(
      Routes.socialPlatform.name,
      arguments: Routes.socialPlatform.d(
        platform: SocialPlatform.github,
        currentHandle: profile?.githubHandle,
        specialGitHub: true,
      ),
    );
  }
}

class TopicsSheet extends ConsumerStatefulWidget {
  const TopicsSheet({
    super.key,
    required this.title,
    required this.provider,
    required this.selected,
    required this.onSubmit,
  });

  final String title;
  final AutoDisposeFutureProvider<List<String>> provider;
  final List<String> selected;
  final Future<void> Function(List<String> selected) onSubmit;

  @override
  ConsumerState<TopicsSheet> createState() => _TopicSheetState();
}

class _TopicSheetState extends ConsumerState<TopicsSheet> {
  final List<String> tempSelected = [];
  final List<String> tempCustom = [];
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    tempSelected.addAll(widget.selected);
  }

  @override
  Widget build(BuildContext context) {
    return ScrollableBottomSheet(
      title: '${widget.title}...',
      sliversBuilder: (context) => [
        Consumer(
          builder: (context, ref, child) {
            final itemsResult = ref.watch(widget.provider);
            return itemsResult.when(
              loading: () => const SliverFillRemaining(child: AppLoading()),
              error: (error, stack) => SliverFillRemaining(
                child: NetworkErrorWidget(
                  onRefresh: () => ref.invalidate(widget.provider),
                ),
              ),
              data: (items) {
                final allIdentities = {
                  ...items.map((r) => r),
                  ...widget.selected,
                  ...tempCustom,
                }.toList();

                return _buildSelectionGrid(
                  items: allIdentities,
                  selectedItems: tempSelected,
                  customButtonText: '+ Custom',
                  onItemTap: (item) {
                    setState(() {
                      if (tempSelected.contains(item)) {
                        tempSelected.remove(item);
                      } else {
                        tempSelected.add(item);
                      }
                    });
                  },
                  onCustomTap: () => _showCustomItemDialog(
                    title: 'Add Custom Content',
                    hintText: '${widget.title}...',
                    onCustomItemAdded: (customIdentity) {
                      setState(() {
                        tempCustom.add(customIdentity);
                        tempSelected.add(customIdentity);
                      });
                    },
                  ),
                );
              },
            );
          },
        ),
      ],
      bottomBuilder: (context) => ThemeTextButton(
        onPressed: () async {
          if (_isSubmitting) {
            return;
          }
          setState(() {
            _isSubmitting = true;
          });
          try {
            await widget.onSubmit(tempSelected);
            if (mounted) {
              ref.invalidate(fetchExtendProfileProvider);
              Card3ToastUtil.showToast(message: ToastMessages.updated);
              Navigator.maybePop(context);
            }
          } catch (e) {
            if (mounted) {
              Card3ToastUtil.showToast(message: ToastMessages.failedToUpdate);
            }
            rethrow;
          } finally {
            safeSetState(() {
              _isSubmitting = false;
            });
          }
        },
        child: _isSubmitting ? const AppLoading() : Text(context.l10nME.submitButton),
      ),
    );
  }

  // 通用的选择网格组件
  Widget _buildSelectionGrid({
    required List<String> items,
    required List<String> selectedItems,
    required String customButtonText,
    required Function(String) onItemTap,
    required VoidCallback onCustomTap,
  }) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      sliver: SliverGrid.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 2.7,
        ),
        itemCount: items.length + 1, // +1 for custom button
        itemBuilder: (context, index) {
          if (index == items.length) {
            return _buildCustomButton(
              context,
              customButtonText,
              onCustomTap,
            );
          }

          final item = items[index].trim();
          final isSelected = selectedItems.contains(item);

          return RippleTap(
            onTap: () => onItemTap(item),
            padding: const EdgeInsets.symmetric(horizontal: 8),
            alignment: Alignment.center,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: isSelected ? context.themeColor : context.theme.dividerColor,
                width: 2.0,
              ),
            ),
            color: isSelected ? context.themeColor.withValues(alpha: 0.5) : context.theme.cardColor,
            child: Text(
              item,
              style: const TextStyle(fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.fade,
            ),
          );
        },
      ),
    );
  }

  // 通用的自定义按钮组件
  Widget _buildCustomButton(
    BuildContext context,
    String text,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: context.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: context.theme.dividerColor,
            width: 2,
          ),
        ),
        child: Center(
          child: Text(
            text,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // 通用的自定义项目对话框
  void _showCustomItemDialog({
    required String title,
    required String hintText,
    required Function(String) onCustomItemAdded,
  }) {
    final customItemController = TextEditingController();
    bool isValid = false;
    String errorText = '';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {
          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: Text(title),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: customItemController,
                  autofocus: true,
                  maxLength: 36,
                  onChanged: (value) {
                    setDialogState(() {
                      final trimmedValue = value.trim();
                      if (trimmedValue.isEmpty) {
                        isValid = false;
                        errorText = 'Required';
                      } else if (trimmedValue.length > 36) {
                        isValid = false;
                        errorText = 'Content length should be less than 30';
                      } else {
                        isValid = true;
                        errorText = '';
                      }
                    });
                  },
                  decoration: InputDecoration(
                    hintText: hintText,
                    errorText: errorText.isNotEmpty ? errorText : null,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: isValid
                    ? () {
                        final customItem = customItemController.text.trim();
                        onCustomItemAdded(customItem);
                        Navigator.pop(context);
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isValid ? ColorName.themeColorDark : Colors.grey[300],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Add',
                  style: TextStyle(color: isValid ? Colors.white : Colors.grey[600]),
                ),
              ),
              const Gap.h(2.0),
            ],
          );
        },
      ),
    );
  }
}
