import 'package:flutter/material.dart';

import '/res/assets.gen.dart';
import '/res/colors.gen.dart';

class AppBackButton extends StatelessWidget {
  const AppBackButton({super.key, this.onPressed});

  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () {
        if (onPressed != null) {
          onPressed!();
        } else {
          Navigator.of(context).maybePop();
        }
      },
      icon: Container(
        padding: const EdgeInsets.all(6.0),
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          color: ColorName.cardColorDark,
          shape: BoxShape.circle,
        ),
        child: Assets.icons.buttonIconBack.svg(),
      ),
    );
  }
}
