import 'package:flutter/widgets.dart';

class AnimatedOvalBorder extends ImplicitlyAnimatedWidget {
  const AnimatedOvalBorder({
    super.key,
    required super.duration,
    super.curve,
    required this.color,
    required this.strokeWidth,
  });

  final Color color;
  final double strokeWidth;

  @override
  AnimatedWidgetBaseState<AnimatedOvalBorder> createState() => _AnimatedOvalBorderState();
}

class _AnimatedOvalBorderState extends AnimatedWidgetBaseState<AnimatedOvalBorder> {
  ColorTween? _color;
  Tween<double>? _strokeWidth;

  @override
  void forEachTween(TweenVisitor<dynamic> visitor) {
    _color =
        visitor(
              _color,
              widget.color,
              (dynamic value) => ColorTween(begin: value),
            )
            as ColorTween?;
    _strokeWidth =
        visitor(
              _strokeWidth,
              widget.strokeWidth,
              (dynamic value) => Tween<double>(begin: value),
            )
            as Tween<double>?;
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _OvalBorderPainter(
        strokeWidth: _strokeWidth!.evaluate(animation),
        color: _color!.evaluate(animation)!,
      ),
    );
  }
}

class _OvalBorderPainter extends CustomPainter {
  _OvalBorderPainter({
    required this.strokeWidth,
    required this.color,
  });

  final double strokeWidth;
  final Color color;

  @override
  void paint(Canvas canvas, Size size) {
    double strokeWidth = this.strokeWidth;
    if (strokeWidth < 0.5) {
      strokeWidth = 0;
    }
    if (strokeWidth == 0) {
      return;
    }

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;
    final rect = Rect.fromCircle(
      center: size.center(Offset.zero),
      radius: size.width / 2 + strokeWidth,
    );
    canvas.drawOval(rect, paint);
  }

  @override
  bool shouldRepaint(covariant _OvalBorderPainter oldDelegate) {
    return oldDelegate.strokeWidth != strokeWidth || oldDelegate.color != color;
  }
}
