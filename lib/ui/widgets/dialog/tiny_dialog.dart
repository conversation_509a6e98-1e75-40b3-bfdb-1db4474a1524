import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_l10n/me_l10n.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_ui/me_ui.dart';

class TinyDialog extends StatelessWidget {
  const TinyDialog._({
    super.key,
    required this.text,
    this.captionText,
    this.childrenBuilder,
    this.buttonsBuilder,
    this.defaultButtonText,
    this.defaultButtonOnPressed,
  });

  final String text;
  final String? captionText;
  final List<Widget> Function(BuildContext context)? childrenBuilder;
  final Widget Function(BuildContext)? buttonsBuilder;
  final String? defaultButtonText;
  final VoidCallback? defaultButtonOnPressed;

  static Future<T?> show<T>({
    BuildContext? context,
    Key? key,
    required String text,
    String? captionText,
    List<Widget> Function(BuildContext context)? childrenBuilder,
    Widget Function(BuildContext)? buttonsBuilder,
    String? defaultButtonText,
    VoidCallback? defaultButtonOnPressed,
  }) {
    return showDialog<T>(
      context: context ?? meContext,
      builder: (context) => TinyDialog._(
        key: key,
        text: text,
        captionText: captionText,
        childrenBuilder: childrenBuilder,
        buttonsBuilder: buttonsBuilder,
        defaultButtonText: defaultButtonText,
        defaultButtonOnPressed: defaultButtonOnPressed,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final meTheme = theme.extension<METheme>()!;
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: 24.0,
          vertical: 42.0,
        ).add(MediaQuery.paddingOf(context)).add(MediaQuery.viewInsetsOf(context)),
        padding: const EdgeInsetsDirectional.all(10.0),
        decoration: BoxDecoration(
          border: Border.all(color: theme.dividerColor, width: 2.0),
          borderRadius: meTheme.borderRadius,
          color: theme.cardColor,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          spacing: 10.0,
          children: [
            SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 14.0),
              child: Column(
                spacing: 10.0,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(text, style: theme.textTheme.headlineSmall),
                  if (captionText case final text?) Text(text, style: theme.textTheme.bodySmall),
                ],
              ),
            ),
            if (childrenBuilder case final children?) ...children(context),
            if (buttonsBuilder case final builder?)
              builder(context)
            else
              TinyDialogButton.secondary(
                onPressed: defaultButtonOnPressed ?? Navigator.of(context).maybePop,
                text: defaultButtonText ?? context.l10nME.okButton,
              ),
          ],
        ),
      ),
    );
  }
}

class TinyDialogButton extends StatelessWidget {
  const TinyDialogButton({
    super.key,
    required this.onPressed,
    this.text,
    this.child,
  }) : _isSecondary = false;

  const TinyDialogButton.secondary({
    super.key,
    required this.onPressed,
    this.text,
    this.child,
  }) : _isSecondary = true;

  final VoidCallback onPressed;
  final String? text;
  final Widget? child;
  final bool _isSecondary;

  @override
  Widget build(BuildContext context) {
    return ThemeTextButton(
      onPressed: onPressed,
      width: null,
      height: 46.0,
      alignment: null,
      intrinsicWidth: false,
      borderRadius: BorderRadius.circular(16.0),
      themeColor: _isSecondary ? Theme.of(context).extension<METheme>()?.listColor : null,
      text: text,
      textStyle: const TextStyle(fontWeight: FontWeight.normal),
      child: child,
    );
  }
}

class TinyDialogButtonGroup extends StatelessWidget {
  const TinyDialogButtonGroup({
    super.key,
    this.onCancel,
    this.onConfirm,
    this.cancelText,
    this.confirmText,
    this.cancelChild,
    this.confirmChild,
  });

  final VoidCallback? onCancel;
  final VoidCallback? onConfirm;
  final String? cancelText;
  final String? confirmText;
  final Widget? cancelChild;
  final Widget? confirmChild;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 10.0,
      children: [
        Expanded(
          child: TinyDialogButton.secondary(
            onPressed: onCancel ?? () => Navigator.of(context).maybePop(false),
            text: cancelText ?? (cancelChild == null ? context.l10nME.cancelButton : null),
            child: cancelChild,
          ),
        ),
        Expanded(
          child: TinyDialogButton(
            onPressed: onConfirm ?? () => Navigator.of(context).maybePop(true),
            text: confirmText ?? (confirmChild == null ? context.l10nME.confirmButton : null),
            child: confirmChild,
          ),
        ),
      ],
    );
  }
}
