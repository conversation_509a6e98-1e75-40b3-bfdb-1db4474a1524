import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_ui/me_ui.dart';

import '/res/assets.gen.dart';
import '/res/fonts.gen.dart';

const _defaultChildrenSpacing = 16.0;

class EmptyView extends StatelessWidget {
  const EmptyView({
    super.key,
    this.icon,
    this.message,
    this.onTap,
    this.childrenSpacing = _defaultChildrenSpacing,
    this.childrenBuilder,
  });

  final Widget? icon;
  final String? message;
  final VoidCallback? onTap;
  final double childrenSpacing;
  final List<Widget> Function(BuildContext)? childrenBuilder;

  @override
  Widget build(BuildContext context) {
    return Tapper(
      onTap: onTap,
      child: Align(
        alignment: const AlignmentDirectional(0.0, -0.5),
        child: Column(
          spacing: childrenSpacing,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildIcon(context),
            if (message case final String message)
              PlaceholderText(
                message,
                style: TextStyle(
                  color: context.meTheme.blueGreyIconColor,
                  fontSize: 20.0,
                  fontFamily: FontFamily.harmonyOSSans,
                  fontWeight: FontWeight.w700,
                  height: 24.0 / 20.0,
                ),
                textAlign: TextAlign.center,
              ),
            ...?childrenBuilder?.call(context),
          ],
        ),
      ),
    );
  }

  Widget _buildIcon(BuildContext context) {
    if (icon case final icon?) {
      return icon;
    }
    final asset = switch (Theme.of(context).brightness) {
      Brightness.dark => Assets.lottie.placeholderEmptyDark,
      Brightness.light => Assets.lottie.placeholderEmptyLight,
    };
    return asset.lottie(width: 100.0, fit: BoxFit.cover);
  }
}

class SliverEmptyView extends StatelessWidget {
  const SliverEmptyView({
    super.key,
    this.icon,
    this.message,
    this.onTap,
    this.childrenSpacing = _defaultChildrenSpacing,
    this.childrenBuilder,
    this.fillRemaining = true,
  });

  final Widget? icon;
  final String? message;
  final VoidCallback? onTap;
  final double childrenSpacing;
  final List<Widget> Function(BuildContext)? childrenBuilder;
  final bool fillRemaining;

  @override
  Widget build(BuildContext context) {
    final child = EmptyView(
      icon: icon,
      message: message,
      onTap: onTap,
      childrenSpacing: childrenSpacing,
      childrenBuilder: childrenBuilder,
    );
    if (fillRemaining) {
      return SliverFillRemaining(child: child);
    }
    return SliverToBoxAdapter(child: child);
  }
}

class RefreshableEmptyView extends StatelessWidget {
  const RefreshableEmptyView({
    super.key,
    this.icon,
    this.message,
    this.onTap,
    this.childrenSpacing = _defaultChildrenSpacing,
    this.childrenBuilder,
  });

  final Widget? icon;
  final String? message;
  final VoidCallback? onTap;
  final double childrenSpacing;
  final List<Widget> Function(BuildContext)? childrenBuilder;

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        SliverEmptyView(
          icon: icon,
          message: message,
          onTap: onTap,
          childrenSpacing: childrenSpacing,
          childrenBuilder: childrenBuilder,
          fillRemaining: true,
        ),
      ],
    );
  }
}
