import 'package:flutter/foundation.dart' show defaultTargetPlatform;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show FilteringTextInputFormatter;
import 'package:me_constants/me_constants.dart' show METhemeBuildContextExtension;
import 'package:pinput/pinput.dart';

PinTheme _defaultPinTheme(
  BuildContext context, {
  Color? themeColor,
  bool isSelected = false,
  bool enabled = true,
}) {
  return PinTheme(
    width: MediaQuery.sizeOf(context).width,
    height: 80.0,
    constraints: const BoxConstraints.tightFor(height: 80.0),
    textStyle: const TextStyle(
      fontSize: 48.0,
      fontWeight: FontWeight.bold,
    ),
    decoration: BoxDecoration(
      border: isSelected ? Border.all(color: themeColor ?? context.themeColor, width: 2.0) : null,
      borderRadius: const BorderRadius.all(Radius.circular(16.0)),
      color: enabled ? Theme.of(context).cardColor : Theme.of(context).disabledColor,
    ),
  );
}

class PinCodeInputWidget extends StatefulWidget {
  const PinCodeInputWidget({
    super.key,
    required this.length,
    required this.controller,
    required this.onCompleted,
    required this.onChanged,
    this.enabled = true,
  });

  final int length;
  final TextEditingController controller;
  final Function(String) onCompleted;
  final Function(String) onChanged;
  final bool enabled;

  @override
  State<PinCodeInputWidget> createState() => _PinCodeInputWidgetState();
}

class _PinCodeInputWidgetState extends State<PinCodeInputWidget> with WidgetsBindingObserver {
  TextEditingController get _controller => widget.controller;

  final _focusNode = FocusNode();

  // String _lastFromClipboard = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _requestFocus();
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void _requestFocus() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_focusNode.hasFocus) {
        _focusNode.requestFocus();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Pinput(
      length: 6,
      controller: _controller,
      focusNode: _focusNode,
      enabled: widget.enabled,
      autofocus: true,
      showCursor: false,
      obscureText: false,
      defaultPinTheme: _defaultPinTheme(context),
      focusedPinTheme: _defaultPinTheme(context, isSelected: true),
      pinAnimationType: PinAnimationType.fade,
      scrollPadding: EdgeInsets.only(
        bottom: 20.0 + MediaQuery.of(context).viewInsets.bottom,
      ),
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      onChanged: widget.onChanged,
      onCompleted: widget.onCompleted,
      // onClipboardFound: (text) {
      //   text = text.trim();
      //   if (text == _lastFromClipboard) {
      //     return;
      //   }
      //   if (RegExp('^\\d{${widget.length}}\$').hasMatch(text)) {
      //     _controller.text = text;
      //     _lastFromClipboard = text;
      //   }
      // },
      contextMenuBuilder: (context, editableTextState) {
        if (defaultTargetPlatform == TargetPlatform.iOS && SystemContextMenu.isSupported(context)) {
          return SystemContextMenu.editableText(editableTextState: editableTextState);
        }
        return AdaptiveTextSelectionToolbar.editableText(editableTextState: editableTextState);
      },
    );
  }
}
