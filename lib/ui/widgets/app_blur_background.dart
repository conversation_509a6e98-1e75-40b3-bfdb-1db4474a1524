import 'package:flutter/material.dart';

import '/res/assets.gen.dart' show Assets;

class AppBlurBackground extends StatelessWidget {
  const AppBlurBackground({
    super.key,
    this.child,
  });

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Transform.scale(
          scale: 3.0,
          alignment: const AlignmentDirectional(-0.5, 0.5),
          child: RepaintBoundary(child: Assets.icons.images.gradient.image()),
        ),
        ?child,
      ],
    );
  }
}
