import 'dart:ui' as ui;

import 'package:ff_annotation_route_library/ff_annotation_route_library.dart';
import 'package:flutter/material.dart';
import 'package:me_ui/me_ui.dart';

import '/res/assets.gen.dart';

@FFRoute(name: '/image/viewer', pageRouteType: PageRouteType.transparent)
class ImageViewer extends StatelessWidget {
  const ImageViewer({
    super.key,
    required this.imageUrl,
  });

  final String imageUrl;

  @override
  Widget build(BuildContext context) {
    final brokenWidget = Center(
      child: Assets.icons.placeholderBroken.svg(
        width: 200.0,
        fit: BoxFit.contain,
      ),
    );
    return Tapper(
      onTap: () => Navigator.of(context).maybePop(),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned.fill(
            child: BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
              child: const ColoredBox(color: Colors.black54),
            ),
          ),
          Padding(
            padding: MediaQuery.paddingOf(context).add(const EdgeInsets.all(16.0)),
            child: MEImage(
              imageUrl,
              fit: BoxFit.contain,
              emptyBuilder: (context) => brokenWidget,
              errorBuilder: (context, _, _) => brokenWidget,
            ),
          ),
        ],
      ),
    );
  }
}
