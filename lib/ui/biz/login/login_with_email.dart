import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/feat/link/handler.dart' show ReferralLinkHandlerNoAuth;
import '/feat/scan/uni_qr.dart';
import '/internals/box.dart' show Boxes;
import '/models/user.dart' show UserReferralRequest;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart';
import '/ui/widgets/pin_code.dart';

const _pinCodeLength = 6;

@FFRoute(name: '/login/email')
class LoginWithEmail extends ConsumerStatefulWidget {
  const LoginWithEmail({super.key});

  @override
  ConsumerState<LoginWithEmail> createState() => _LoginWithEmailState();
}

class _LoginWithEmailState extends ConsumerState<LoginWithEmail> {
  final _cancelToken = CancelToken();

  String get _email => _emailController.text.trim().replaceAll('＠', '@').replaceAll('。', '.');
  final _emailController = TextEditingController();
  final _codeController = TextEditingController();

  final _emailFocusNode = FocusNode();

  bool _isLoading = false;
  bool _isEmailInput = true; // 控制显示邮箱输入或验证码输入

  // 添加邮箱验证相关状态
  bool _emailHasError = false;
  String _emailErrorMessage = '';

  final _resendSeconds = 60;
  late int _resendCountdown = _resendSeconds;
  Timer? _resendTimer;

  @override
  void dispose() {
    _cancelToken.cancel();
    _emailController.dispose();
    _codeController.dispose();
    _emailFocusNode.dispose();
    _resendTimer?.cancel();
    super.dispose();
  }

  void _setResendTimer() {
    _resendTimer?.cancel();
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        _resendTimer = null;
        return;
      }

      setState(() {
        if (_resendCountdown == 1) {
          timer.cancel();
          _resendTimer = null;
          _resendCountdown = _resendSeconds;
        } else {
          _resendCountdown--;
        }
      });
    });
    safeSetState(() {});
  }

  // 邮箱格式验证正则表达式
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  // 验证邮箱格式
  bool _validateEmail(String email) {
    if (email.isEmpty) {
      _emailErrorMessage = 'Email is required';
      return false;
    }
    if (!_emailRegex.hasMatch(email)) {
      _emailErrorMessage = 'Please enter a valid email address';
      return false;
    }
    _emailErrorMessage = '';
    return true;
  }

  // 邮箱输入变化时的处理
  void _onEmailChanged(String value) {
    // 输入时清除错误状态
    if (_emailHasError) {
      safeSetState(() {
        _emailHasError = false;
        _emailErrorMessage = '';
      });
    }
  }

  bool _shouldRetryLogin = false;

  Future<void> _sendCodeUniversal(String email) {
    return ref.read(apiServiceProvider).emailSendCode(email: email);
  }

  // 发送邮箱验证码
  Future<void> _sendEmailCode() async {
    final email = _email;

    // 验证邮箱格式
    if (!_validateEmail(email)) {
      safeSetState(() {
        _emailHasError = true;
      });
      return;
    }

    safeSetState(() {
      _isLoading = true;
      _emailHasError = false;
    });

    try {
      await _sendCodeUniversal(email);
      _setResendTimer();
      safeSetState(() {
        _isEmailInput = false; // 切换到验证码输入界面
        _shouldRetryLogin = false;

        Card3ToastUtil.showToast(message: ToastMessages.emailCodeResent);
        FocusManager.instance.primaryFocus?.unfocus();
      });
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToSendEmailCode);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  // 验证邮箱验证码
  Future<void> _verifyEmailCode() async {
    final code = _codeController.text;
    if (code.length < 6) {
      Card3ToastUtil.showToast(message: ToastMessages.invalidOrExpiredLoginCode);
      return;
    }

    safeSetState(() {
      _isLoading = true;
      _shouldRetryLogin = false;
    });

    try {
      final referral = ref.read(userReferralFromProvider);
      final authorization = await ref
          .read(apiServiceProvider)
          .emailLogin(
            email: _email,
            code: code,
            referralRequest: referral,
            cancelToken: _cancelToken,
          );
      if (!mounted) {
        return;
      }

      final self = await ref
          .read(apiServiceProvider)
          .getSelfUserInfo(
            token: authorization,
            cancelToken: _cancelToken,
          );
      if (!mounted) {
        return;
      }

      await Future.wait([
        ref.read(persistentTokenRepoProvider.notifier).update(authorization),
        ref.read(userRepoProvider.notifier).update(self),
        Boxes.initUser(self),
      ]);
      await ChainManager.instance.initialize();
      meNavigator.removeNamedAndPushAndRemoveUntil(
        Routes.home.name,
        predicate: (_) => false,
      );
    } catch (e) {
      final String message;
      if (e is ApiException) {
        message = '${e.message} (${e.code})';
      } else {
        message = isNetworkError(e) ? context.l10nME.networkError : '$e';
      }

      safeSetState(() {
        Card3ToastUtil.showToast(message: ToastMessages.loginFailed(message));
        _shouldRetryLogin = true;
      });

      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  // 重新发送验证码
  Future<void> _resendCode() async {
    safeSetState(() {
      _isLoading = true;
    });
    try {
      await _sendCodeUniversal(_email);
      safeSetState(() {
        _codeController.clear();
        Card3ToastUtil.showToast(message: ToastMessages.emailCodeResent);
      });
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToResendEmailCode);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final referral = ref.watch(userReferralFromProvider);
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          return;
        }
        ref.read(userReferralFromProvider.notifier).state = UserReferralRequest.empty;
      },
      child: AppScaffold(
        onBackButtonPressed: () {
          if (_isEmailInput) {
            Navigator.of(context).maybePop();
          } else {
            setState(() {
              _isEmailInput = true; // 切换到邮箱输入界面
              _codeController.clear();
              _shouldRetryLogin = false;
            });
            // 切换回邮箱输入界面时，重新聚焦到邮箱输入框
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _emailFocusNode.requestFocus();
            });
          }
        },
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 40.0, bottom: 20.0),
              child: Text(
                _isEmailInput ? 'Log In / Sign Up' : 'Enter login code',
                style: context.textTheme.headlineSmall,
              ),
            ),

            _isEmailInput ? _buildEmailInput(context) : _buildPinInput(context),

            if (referral.isEmpty && _isEmailInput) _buildReferralScan(context),

            if (!referral.isEmpty) _buildReferral(context, referral),

            if (!_isEmailInput && _isLoading) const AppLoading(size: 64.0),
          ],
        ),
        bottomButtonBuilder: (context) => Column(
          spacing: 16.0,
          children: [
            if (_isEmailInput)
              ThemeTextButton(
                onPressed: _isLoading || _resendTimer != null ? null : _sendEmailCode,
                child: _isLoading
                    ? const AppLoading()
                    : Text(
                        () {
                          final buffer = StringBuffer('Send Code');
                          if (_resendTimer != null) {
                            buffer.write(' (${_resendCountdown}s)');
                          }
                          return buffer.toString();
                        }(),
                      ),
              )
            else if (_shouldRetryLogin)
              ThemeTextButton.outlined(
                onPressed: _verifyEmailCode,
                themeColor: context.meTheme.failingColor,
                text: context.l10nME.retryButton,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmailInput(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: _emailController,
          focusNode: _emailFocusNode,
          autofocus: true,
          enabled: !_isLoading,
          keyboardType: TextInputType.emailAddress,
          autocorrect: false,
          autofillHints: [AutofillHints.email],
          textInputAction: TextInputAction.done,
          onChanged: _onEmailChanged,
          onSubmitted: (_) => _sendEmailCode(),
          decoration: const InputDecoration(hintText: 'Your email'),
          style: const TextStyle(fontSize: 24),
        ),
        if (_emailHasError && _emailErrorMessage.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            _emailErrorMessage,
            style: const TextStyle(color: Colors.red, fontSize: 14),
          ),
        ],
      ],
    );
  }

  Widget _buildPinInput(BuildContext context) {
    return Column(
      spacing: 24.0,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 16.0),
          child: Text.rich(
            TextSpan(
              children: [
                const TextSpan(text: 'Please check '),
                TextSpan(
                  text: _email,
                  style: TextStyle(
                    color: context.themeColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const TextSpan(
                  text: ' for an email from privy.io and enter your code below.',
                ),
              ],
            ),
            style: const TextStyle(fontSize: 16),
          ),
        ),
        PinCodeInputWidget(
          length: _pinCodeLength,
          controller: _codeController,
          enabled: !_isLoading,
          onCompleted: (value) {
            if (mounted) {
              _verifyEmailCode();
            }
          },
          onChanged: (value) {
            if (mounted) {
              _shouldRetryLogin = false;
            }
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              "Didn't get the email? ",
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF757575),
              ),
            ),
            GestureDetector(
              onTap: _isLoading || _resendTimer != null ? null : _resendCode,
              child: Text(
                'Resend code',
                style: TextStyle(
                  fontSize: 16,
                  color: context.themeColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (_resendTimer != null)
              Text(
                ' (${_resendCountdown}s)',
                style: const TextStyle(fontSize: 16, color: Colors.grey),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildReferralScan(BuildContext context) {
    return Center(
      child: RippleTap(
        onTap: () async {
          await Navigator.of(context).pushNamed(
            Routes.scan.name,
            arguments: Routes.scan.d(
              manager: ScanManager({_ReferralHandler()}),
            ),
          );
        },
        padding: const EdgeInsets.all(8.0),
        borderRadius: BorderRadius.circular(8.0),
        child: Text(
          'Referral by others? Press to scan the QR code.',
          style: context.textTheme.bodySmall?.copyWith(fontSize: 14.0),
        ),
      ),
    );
  }

  Widget _buildReferral(BuildContext context, UserReferralRequest referral) {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 16.0),
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: context.themeColor.withValues(alpha: 0.1),
        ),
        child: Column(
          children: [
            Row(
              spacing: 8.0,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: PlaceholderText(
                      'You are being referred by',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: context.meTheme.primaryTextColor.withValues(alpha: 0.5),
                        fontSize: 16.0,
                      ),
                      matchedStyle: TextStyle(
                        color: context.themeColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                RippleTap(
                  onTap: () async {
                    final result = await TinyDialog.show(
                      text: 'Are you sure you want to remove the referral?',
                      buttonsBuilder: (context) => const TinyDialogButtonGroup(),
                    );
                    if (result == true) {
                      ref.read(userReferralFromProvider.notifier).state = UserReferralRequest.empty;
                    }
                  },
                  width: 24.0,
                  height: 24.0,
                  child: const FittedBox(child: Icon(Icons.close)),
                ),
              ],
            ),
            Text(
              referral.effectiveDisplay,
              style: TextStyle(
                color: context.themeColor,
                fontSize: 24.0,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

final class _ReferralHandler extends ScanHandler<String> {
  @override
  Future<ScanDat> acceptSuccess(String text) async {
    final uri = Uri.tryParse(text);
    if (uri == null) {
      return ScanDat.mismatched();
    }

    if (const ReferralLinkHandlerNoAuth().onLink(uri)) {
      return ScanDat.processed(uri);
    }

    Card3ToastUtil.showToast(message: ToastMessages.notValidReferralQrCode);
    return ScanDat.mismatched();
  }
}
