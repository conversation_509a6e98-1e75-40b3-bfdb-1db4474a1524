import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:me_misc/me_misc.dart' show meRouteObserver;
import 'package:video_player/video_player.dart';

import '/internals/box.dart' show Boxes;
import '/provider/api.dart' show apiServiceProvider;
import '/ui/widgets/app_logo.dart';

@FFRoute(name: '/login')
class Login extends ConsumerStatefulWidget {
  const Login({super.key});

  @override
  ConsumerState<Login> createState() => _LoginState();
}

class _LoginState extends ConsumerState<Login> with RouteAware {
  VideoPlayerController? _vp;
  bool _termsAccepted = false;

  bool _disposed = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    meRouteObserver.subscribe(this, ModalRoute.of(context)!);
  }

  Future<void> _initializeVideo() async {
    if (_disposed) {
      return;
    }

    await _disposeVideo();

    try {
      final vp = _vp = VideoPlayerController.asset(
        Assets.media.login,
        videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
      );

      await vp.initialize();

      if (_disposed) {
        await vp.dispose();
        return;
      }

      safeSetState(() {});

      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (_disposed) {
          return;
        }
        await vp.setLooping(true);
        await vp.play();
      });
    } finally {
      safeSetState(() {});
    }
  }

  Future<void> _disposeVideo() async {
    final vp = _vp;
    _vp = null;
    if (vp != null) {
      await vp.pause();
      await vp.dispose();
    }
  }

  @override
  void didPushNext() {
    _vp?.pause();
  }

  @override
  void didPopNext() {
    _vp?.play();
  }

  @override
  void dispose() {
    _disposed = true;
    _disposeVideo();
    meRouteObserver.unsubscribe(this);
    super.dispose();
  }

  Future<void> _loginAsGuest() {
    return AppLoading.run(() async {
      final result = await privyGuestAuthenticate();
      final completer = Completer<void>();
      result.fold(
        onSuccess: (privyUser) async {
          final privyJWT = privyUser.identityToken!;
          try {
            final authorization = await ref.read(apiServiceProvider).privyLogin(token: privyJWT);
            final user = await ref
                .read(apiServiceProvider)
                .getSelfUserInfo(
                  token: authorization,
                );
            await Future.wait([
              ref.read(persistentTokenRepoProvider.notifier).update(authorization),
              ref.read(userRepoProvider.notifier).update(user),
              Boxes.initUser(user),
            ]);
            await ChainManager.instance.initialize();
            meNavigator.pushNamedAndRemoveUntil(Routes.home.name, (_) => false);
            completer.complete();
          } catch (e, s) {
            await privyClient.logout().catchError((e, s) {
              handleExceptions(error: e, stackTrace: s);
            });
            Card3ToastUtil.showToast(
              message: isNetworkError(e) ? context.l10nME.networkError : '$e',
            );
            completer.completeError(e, s);
          }
        },
        onFailure: (error) {
          if (mounted && !_disposed) {
            Card3ToastUtil.showToast(message: error.message);
          }
          completer.completeError(error);
        },
      );
      return completer.future;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final showGuestLogin = isAuditing || kDebugMode;
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: AspectRatio(
              aspectRatio: 1080 / 2200,
              child: DecoratedBox(
                position: DecorationPosition.foreground,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [theme.scaffoldBackgroundColor, Colors.transparent],
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    stops: [0.0, 0.1],
                  ),
                ),
                child: Stack(
                  children: [
                    Assets.media.loginFallback.image(fit: BoxFit.fill),
                    if (_vp case final vp? when vp.value.isInitialized)
                      ValueListenableBuilder(
                        valueListenable: vp,
                        builder: (context, value, child) => AnimatedOpacity(
                          opacity: value.isPlaying ? 1.0 : 0.0,
                          duration: const Duration(milliseconds: 100),
                          child: child,
                        ),
                        child: VideoPlayer(vp),
                      ),
                  ],
                ),
              ),
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                const Expanded(
                  child: Center(
                    child: FractionallySizedBox(
                      widthFactor: 0.6,
                      child: AppLogo(),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [theme.scaffoldBackgroundColor, Colors.transparent],
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      stops: [0.6, 1.0],
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Gap.v(60.0),
                      ThemeTextButton(
                        onPressed: () {
                          if (!_termsAccepted) {
                            Card3ToastUtil.showToast(
                              message: ToastMessages.loginTermsRequired,
                            );
                            return;
                          }
                          meNavigator.pushNamed(
                            Routes.loginEmail.name,
                          );
                        },
                        child: const Row(
                          spacing: 10.0,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.email_rounded),
                            Text('Log in / Sign up'),
                          ],
                        ),
                      ),
                      if (showGuestLogin)
                        ThemeTextButton.outlined(
                          margin: const EdgeInsets.only(top: 12.0),
                          onPressed: () {
                            if (!_termsAccepted) {
                              Card3ToastUtil.showToast(
                                message: ToastMessages.loginTermsRequired,
                              );
                              return;
                            }
                            _loginAsGuest();
                          },
                          text: 'Guest Login',
                        ),
                      const Gap.v(30.0),
                      _buildTermsText(context),
                      const Gap.v(30.0),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          'Powered by Card3',
                          style: context.textTheme.bodySmall?.copyWith(fontSize: 10.0),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsText(BuildContext context) {
    return Tapper(
      onTap: () {
        setState(() {
          _termsAccepted = !_termsAccepted;
        });
      },
      child: Row(
        spacing: 8.0,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          MECheckbox(
            dimension: 20.0,
            checked: _termsAccepted,
            iconData: Icons.check_circle_rounded,
            onChange: (value) {
              setState(() {
                _termsAccepted = value;
              });
            },
          ),
          Flexible(
            child: Text.rich(
              TextSpan(
                children: [
                  const TextSpan(text: 'I have read and agreed to '),
                  TextSpan(
                    text: 'Terms of Use',
                    style: TextStyle(
                      color: context.themeColor,
                      fontWeight: FontWeight.w700,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        launchUrlString(envUrlEULA);
                      },
                  ),
                  const TextSpan(text: ' and '),
                  TextSpan(
                    text: 'Privacy Policy',
                    style: TextStyle(
                      color: context.themeColor,
                      fontWeight: FontWeight.w700,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        launchUrlString(envUrlPP);
                      },
                  ),
                ],
              ),
              style: context.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
