import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:me_misc/me_misc.dart' show calcTextSize;

import '/feat/notification/handler.dart' show handleNotificationOpenWithPayload;
import '/models/business.dart' show Message, MessageType, MessageSayHiParams, ImageAITaskDone, MessageImage;
import '/models/user.dart' show UserFromRelationType;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/settings.dart' show selectedIndexProvider;
import '/provider/user.dart' show fetchUserInfoProvider;
import '/ui/biz/home/<USER>' show connectionsRelationTypeProvider;

final _listProvider = FutureProvider.autoDispose.family<Paged<Message>, int>((ref, page) {
  if (page <= 0) {
    return Paged.empty(page: page);
  }
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  return ref.read(apiServiceProvider).listMessages(page: page, cancelToken: ct);
});

@FFRoute(name: '/notification')
class NotificationPage extends ConsumerStatefulWidget {
  const NotificationPage({super.key});

  @override
  ConsumerState<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends ConsumerState<NotificationPage> {
  @override
  void initState() {
    super.initState();
    ref.read(_listProvider(1).future).then((_) {
      globalContainer.invalidate(fetchUserInfoProvider());
    });
  }

  Future<void> _onRefresh() async {
    ref.invalidate(_listProvider);
    await ref.read(_listProvider(1).future);
    globalContainer.invalidate(fetchUserInfoProvider());
  }

  @override
  Widget build(BuildContext context) {
    final totalResult = ref.watch(_listProvider(1));
    final Widget child;
    if (totalResult.valueOrNull?.total == 0) {
      child = SliverEmptyView(
        onTap: _onRefresh,
        message: 'No notifications yet.',
      );
    } else if (totalResult.hasError && !totalResult.isLoading) {
      final e = totalResult.error;
      child = SliverEmptyView(
        onTap: _onRefresh,
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      );
    } else {
      const size = 20;
      child = SliverPadding(
        padding: const EdgeInsets.symmetric(vertical: 14.0),
        sliver: SliverList.builder(
          itemCount: totalResult.valueOrNull?.total ?? 4,
          itemBuilder: (context, index) {
            final page = index ~/ size + 1;
            final indexInPage = index % size;
            final result = ref.watch(_listProvider(page));
            final lastInLastPage = ref.watch(_listProvider(page - 1)).valueOrNull?.list.lastOrNull;
            return result.maybeWhen(
              data: (data) {
                if (indexInPage >= data.list.length) {
                  return null;
                }
                final item = data.list[indexInPage];
                final last = indexInPage > 0 ? data.list[indexInPage - 1] : lastInLastPage;
                final showDate =
                    last == null ||
                    last.createTime.format(format: 'MMM dd, yyyy') != item.createTime.format(format: 'MMM dd, yyyy');
                return ProviderScope(
                  overrides: [
                    _itemProvider.overrideWithValue((item, showDate)),
                  ],
                  child: const _MessageItem(),
                );
              },
              orElse: () => const _MessageItemShimmer(),
            );
          },
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      displacement: MediaQuery.paddingOf(context).top,
      child: AppScaffold(
        body: CustomScrollView(
          slivers: [
            child,
          ],
        ),
      ),
    );
  }
}

final _itemProvider = Provider.autoDispose<(Message, bool)>(
  (ref) => throw UnimplementedError(),
);

class _MessageItem extends ConsumerWidget {
  const _MessageItem();

  Map<String, String> extractBracketContent(String content) {
    final RegExp bracketRegex = RegExp(r'\[(.*?)\]');
    final match = bracketRegex.firstMatch(content);
    String? extractedContent;
    String cleanedText = content;

    if (match != null) {
      extractedContent = match.group(1);
      cleanedText = content.replaceAll(bracketRegex, '').trim();
    }

    return {
      'extractedContent': extractedContent ?? '',
      'cleanedText': cleanedText,
    };
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final (message, showDate) = ref.watch(_itemProvider);
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showDate)
          Padding(
            padding: const EdgeInsets.only(top: 20.0, bottom: 10.0),
            child: Text(
              message.createTime.run((it) {
                final now = DateTime.now();
                if (it.year == now.year && it.month == now.month && it.day == now.day) {
                  return 'Today';
                } else if (now.startOfTheDay.difference(it.startOfTheDay).inDays == 1) {
                  return 'Yesterday';
                } else if (it.year == now.year) {
                  return it.format(format: 'MM-dd');
                } else {
                  return it.format(format: 'yyyy-MM-dd');
                }
              }),
              style: theme.textTheme.headlineSmall,
            ),
          ),
        RippleTap(
          onTap: () async {
            if (handleNotificationOpenWithPayload(message.appParams)) {
              return;
            }

            final type = message.messageType;
            final jsonParams = message.params;
            if (type == MessageType.sayHi) {
              meNavigator.popUntil((r) => r.settings.name == Routes.home.name);
              globalContainer.read(selectedIndexProvider.notifier).state = 1;
              globalContainer.read(connectionsRelationTypeProvider.notifier).state = UserFromRelationType.follower;
              return;
            }

            if (type == MessageType.follow) {
              final params = MessageSayHiParams.fromJson(jsonParams);
              meNavigator.pushNamed(
                Routes.socialProfile.name,
                arguments: Routes.socialProfile.d(code: params.referralCode),
              );
              return;
            }

            if (type == MessageType.airdrop) {
              AppLoading.run(
                until: 1.seconds,
                () async {
                  final chainManager = ChainManager.instance;
                  if (chainManager.initializing) {
                    await chainManager.initialize();
                  }
                  final state = await privyClient.getAuthState();
                  final user = state.user;
                  if (chainManager.initialized) {
                    final chainId = jsonParams['chainId'];
                    final networks = BoxService.getNetworksFromLocal();
                    final network = networks.firstWhere(
                      (network) => network.chainIdEvm == chainId,
                      orElse: () => networks.first,
                    );
                    await ChainManager.instance.switchNetwork(network);
                    meNavigator.pushNamed(Routes.walletPortfolio.name);
                  } else if (user != null) {
                    meNavigator.pushNamed(Routes.walletAuthenticate.name);
                  } else {
                    meNavigator.pushNamed(Routes.walletManagement.name);
                  }
                },
              );
              return;
            }

            if (type == MessageType.integral) {
              meNavigator.pushNamed(Routes.funPoints.name);
              return;
            }

            if (type == MessageType.aiTaskImage) {
              final taskResult = ImageAITaskDone.fromJson(jsonParams);
              meNavigator.removeRouteByName(Routes.socialEditProfile.name);
              meNavigator.removeRouteByName(Routes.aiGenerateImage.name);
              final result = await meNavigator.pushNamed(
                Routes.aiGenerateImage.name,
                arguments: Routes.aiGenerateImage.d(
                  taskId: taskResult.taskId,
                  generatedUrl: taskResult.imageUrl,
                ),
              );
              if (result is! String) {
                return;
              }
              meNavigator.pushNamed(
                Routes.socialEditProfile.name,
                arguments: Routes.socialEditProfile.d(pendingAvatarUrl: result),
              );
              return;
            }

            if (type == MessageType.text) {
              return;
            }
          },
          margin: const EdgeInsets.only(bottom: 10.0),
          borderRadius: BorderRadius.zero,
          child: Stack(
            children: [
              _buildContent(context, message),
              PositionedDirectional(
                end: 0.0,
                top: 0.0,
                child: _buildTime(context, message.createTime),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, Message message) {
    return Row(
      spacing: 10.0,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildIcon(context, message),
        Expanded(
          child: Container(
            width: double.infinity,
            constraints: const BoxConstraints(minHeight: 46.0),
            padding: const EdgeInsets.only(top: 4.0, bottom: 10.0),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: context.theme.colorScheme.surface),
              ),
            ),
            child: Column(
              spacing: 4.0,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (message.effectiveTitle case final title?) _buildTitle(context, title),
                _buildTypedContent(context, message),
                if (message.images case final images when images.isNotEmpty) _buildImages(context, images),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildIcon(BuildContext context, Message message) {
    final color = context.themeColor;
    final defaultIcon = Center(
      child: switch (message.messageType) {
        MessageType.follow || MessageType.sayHi => MessageSayHiParams.fromJson(message.params).run(
          (it) => UserAvatar(
            user: it,
            dimension: 46.0,
            borderRadius: BorderRadius.circular(16.0),
            placeholderPadding: const EdgeInsets.all(4.0),
          ),
        ),
        MessageType.aiTaskImage || MessageType.cosyAiTaskImage => Assets.icons.iconMessageAiImage.svg(
          width: 24.0,
          height: 24.0,
        ),
        _ => Assets.icons.iconMessageDefault.svg(
          width: 24.0,
          height: 24.0,
        ),
      },
    );
    final Widget child;
    if (message.icon case final icon?) {
      child = MEImage(
        icon.url,
        width: icon.width,
        height: icon.height,
        cacheWidth: icon.width?.toCache(context),
        fit: icon.fit ?? BoxFit.cover,
        borderRadius: BorderRadius.circular(16.0),
        backgroundColor: color,
        emptyBuilder: (context) => defaultIcon,
      );
    } else {
      child = defaultIcon;
    }
    return Container(
      width: 46.0,
      height: 46.0,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        color: color,
      ),
      child: child,
    );
  }

  Widget _buildTitle(BuildContext context, String title) {
    return Text(
      title,
      style: context.textTheme.headlineSmall?.copyWith(fontSize: 16.0),
    );
  }

  Widget _buildTypedContent(BuildContext context, Message message) {
    final content = message.message;
    switch (message.messageType) {
      case MessageType.sayHi:
        final params = MessageSayHiParams.fromJson(message.params);
        return Text('Say HI from ${params.name}');
      case MessageType.push:
        final extracted = extractBracketContent(content);
        return Column(
          spacing: 10.0,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(extracted['cleanedText']!),
            if (extracted['extractedContent']!.isNotEmpty)
              Row(
                spacing: 4.0,
                children: [
                  Text('${extracted['extractedContent']!.split('|')[0]} :'),
                  GestureDetector(
                    onTap: () {
                      // 打开链接
                      // TODO: 实现链接跳转
                    },
                    child: Text(
                      extracted['extractedContent']!.split('|')[1],
                      style: TextStyle(
                        fontSize: 16,
                        color: context.themeColor,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
          ],
        );

      case MessageType.aiTaskImage:
        final params = ImageAITaskDone.fromJson(message.params);
        return Column(
          spacing: 10.0,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(content),
            if (params.imageUrl.isNotEmpty)
              MEImage(
                params.imageUrl,
                width: 100.0,
                height: 100.0,
                cacheWidth: 100.toCache(context),
                borderRadius: BorderRadius.circular(20.0),
                fit: BoxFit.cover,
                backgroundColor: context.meTheme.listColor,
              ),
          ],
        );

      default:
        return Text(content);
    }
  }

  Widget _buildImages(BuildContext context, List<MessageImage> images) {
    return Wrap(
      spacing: 4.0,
      runSpacing: 4.0,
      children: images
          .map(
            (image) => MEImage(
              image.url,
              width: image.width,
              height: image.height,
              cacheWidth: image.width?.toCache(context),
              fit: BoxFit.fitWidth,
              borderRadius: BorderRadius.circular(10.0),
              backgroundColor: context.meTheme.listColor,
              emptyBuilder: (context) => Container(
                width: image.width ?? 100.0,
                height: image.height ?? 100.0,
                alignment: Alignment.center,
                child: const Icon(
                  Icons.question_mark_rounded,
                  size: 48.0,
                ),
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildTime(BuildContext context, DateTime time) {
    final formalized = time.run((it) {
      final now = DateTime.now();
      final diff = now.difference(it);
      if (diff < 1.minutes) {
        return 'Just now';
      } else if (it.year == now.year && it.month == now.month && it.day == now.day) {
        return it.format(format: 'HH:mm');
      } else if (now.startOfTheDay.difference(it.startOfTheDay).inDays == 1) {
        return 'Yesterday';
      } else if (it.year == now.year) {
        return it.format(format: 'MM-dd');
      } else {
        return it.format(format: 'yyyy-MM-dd');
      }
    });
    final theme = context.theme;
    final style = theme.textTheme.bodySmall!;
    final width = calcTextSize(formalized, style).width;
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 2.0,
        vertical: 5.0,
      ).add(EdgeInsetsDirectional.only(start: width * 0.2)),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.scaffoldBackgroundColor,
            theme.scaffoldBackgroundColor.withAlpha(0),
          ],
          stops: [0.8, 1.0],
          begin: AlignmentDirectional.centerEnd,
          end: AlignmentDirectional.centerStart,
        ),
      ),
      child: Text(formalized, style: style),
    );
  }
}

class _MessageItemShimmer extends StatelessWidget {
  const _MessageItemShimmer();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: context.theme.cardColor,
      ),
      child: MEShimmer(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 10.0,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Icon(Icons.message, size: 24.0),
                Container(width: 100.0, height: 12.0, color: context.theme.cardColor),
              ],
            ),
            Container(
              width: 100.0,
              height: 14.0,
              color: context.theme.dividerColor,
            ),
          ],
        ),
      ),
    );
  }
}
