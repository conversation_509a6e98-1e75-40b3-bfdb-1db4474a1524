import 'dart:async';
import 'dart:math' as math;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show SystemChannels;
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:me_analytics/me_analytics.dart' show MEAnalytics;
import 'package:url_launcher/url_launcher.dart';

import '/constants/envs.dart' as envs show isSealed;
import '/feat/bridge/module/in_house.dart';
import '/feat/bridge/module/launch.dart' show askToLaunchUrlExternally;
import '/feat/link/handler.dart' show SchemeLinkHandler;
import '/feat/link/helper.dart' show AppLinkHelper;

const _tag = '🌐 WebView';

@FFRoute(name: '/webview')
class WebViewPage extends ConsumerStatefulWidget {
  const WebViewPage({
    super.key,
    required this.url,
    this.title,
  });

  final String url;
  final String? title;

  @override
  ConsumerState<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends ConsumerState<WebViewPage> with WidgetsBindingObserver {
  final progressController = StreamController<double>.broadcast();
  late final _titleNotifier = ValueNotifier<String?>(widget.title?.trim());
  late final _currentUrl = ValueNotifier(widget.url.trim());

  String? get urlDomain => Uri.tryParse(_currentUrl.value)?.host;

  Timer? _progressCancelTimer;

  InAppWebViewController? get _webViewController {
    if (mounted) {
      return _inAppWebViewController;
    }
    return null;
  }

  InAppWebViewController? _inAppWebViewController;

  bool _canWebViewGoBack = false;
  String? _errorMessage;

  Completer<void>? _closingLock;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    SystemChannels.textInput.invokeMethod<void>('TextInput.hide');
    WidgetsBinding.instance.removeObserver(this);
    progressController.close();
    _webViewController?.dispose();
    super.dispose();
  }

  void cancelProgress([Duration duration = const Duration(seconds: 1)]) {
    _progressCancelTimer?.cancel();
    _progressCancelTimer = Timer(duration, () {
      if (mounted && !progressController.isClosed) {
        progressController.add(0.0);
      }
    });
  }

  URLRequest _getRequestByUrl(String url, {required bool withNoCache}) {
    return URLRequest(
      url: WebUri(url),
      headers: {
        'User-Agent': PackageUtil.userAgent,
        if (withNoCache) 'Cache-Control': 'no-cache, no-store, must-revalidate',
        if (withNoCache) 'Pragma': 'no-cache',
        if (withNoCache) 'Expires': '0',
      },
    );
  }

  // 重新加载WebView
  Future<void> _reloadWebView([InAppWebViewController? controller]) async {
    controller ??= _webViewController;
    if (!mounted || controller == null) {
      return;
    }

    setState(() {
      _errorMessage = null;
    });

    try {
      // 添加5秒超时
      await _performReload(controller).timeout(const Duration(seconds: 5));
    } catch (e) {
      safeSetState(() {
        _errorMessage = 'Failed to reload page: $e';
      });
      rethrow;
    }
  }

  // 执行实际的重新加载操作
  Future<void> _performReload(InAppWebViewController controller) {
    return controller.loadUrl(
      urlRequest: _getRequestByUrl(_currentUrl.value, withNoCache: true),
    );
  }

  void _onWebViewCreated(InAppWebViewController controller) {
    _inAppWebViewController = controller;
    defaultInHouseBridge.inject(context, ref, controller);
    controller.addJavaScriptHandler(
      handlerName: '__onTitleChanged__',
      callback: (JavaScriptHandlerFunctionData data) {
        final title = data.args.firstOrNull?.toString();
        _onTitleChanged(controller, title);
      },
    );
  }

  Future<void> _onLoadStart(
    InAppWebViewController controller,
    WebUri? url,
  ) async {
    safeSetState(() {
      if (url?.toString() case final url?) {
        _currentUrl.value = url;
      }
      _errorMessage = null;
    });
  }

  Future<void> _onLoadStop(
    InAppWebViewController controller,
    WebUri? url,
  ) async {
    if (!mounted) {
      return;
    }
    if (url?.toString() case final url?) {
      _currentUrl.value = url;
    }
    cancelProgress();

    if (url?.host case final host? when defaultInHouseBridgeHostRegExp.hasMatch(host)) {
      final token = BoxService.getToken() ?? '';
      await controller.evaluateJavascript(
        source: inHouseJSSourceInjectToken(token),
      );
    }
  }

  Future<void> _onCloseWindow(InAppWebViewController controller) {
    if (_closingLock case final lock?) {
      return lock.future;
    }

    final lock = _closingLock = Completer<void>();
    TinyDialog.show<bool>(
          text: 'Are you sure you want to close this page?',
          buttonsBuilder: (context) => const TinyDialogButtonGroup(),
        )
        .then(
          (result) {
            lock.complete();
            if (result == true && mounted) {
              controller.stopLoading();
              Navigator.of(context).maybePop();
            }
          },
          onError: (e, s) {
            lock.completeError(e, s);
          },
        )
        .whenComplete(() {
          _closingLock = null;
        });
    return lock.future;
  }

  Future<PermissionResponse> _onPermissionRequest(
    InAppWebViewController controller,
    PermissionRequest request,
  ) async {
    LogUtil.d('WebView permission request: ${request.resources}', tag: _tag);

    // Map WebView permission resources to system permissions
    final permissions = <Permission>[];

    for (final resource in request.resources) {
      if (resource == PermissionResourceType.CAMERA_AND_MICROPHONE) {
        permissions.add(Permission.camera);
        permissions.add(Permission.microphone);
      } else if (resource == PermissionResourceType.CAMERA) {
        permissions.add(Permission.camera);
      } else if (resource == PermissionResourceType.MICROPHONE) {
        permissions.add(Permission.microphone);
      } else if (resource == PermissionResourceType.GEOLOCATION) {
        permissions.add(Permission.location);
      } else {
        LogUtil.w('Unknown permission resource: $resource', tag: _tag);
      }
    }

    if (permissions.isEmpty) {
      // If no mappable permissions, grant all by default
      return PermissionResponse(
        resources: request.resources,
        action: PermissionResponseAction.GRANT,
      );
    }

    try {
      // Check current permission status
      final status = await permissions.request();

      // Check if all permissions are already granted
      final allGranted = status.values.every((status) => status.isGranted);

      if (allGranted) {
        return PermissionResponse(
          resources: request.resources,
          action: PermissionResponseAction.GRANT,
        );
      }

      // Request permissions that are not granted
      final notGrantedPermissions = status.entries
          .where((entry) => !entry.value.isGranted && !entry.value.isPermanentlyDenied)
          .map((entry) => entry.key)
          .toList();

      if (notGrantedPermissions.isNotEmpty) {
        final requestResult = await notGrantedPermissions.request();

        // Check if all requested permissions are now granted
        final allRequestedGranted = requestResult.values.every((s) => s.isGranted);

        if (allRequestedGranted) {
          return PermissionResponse(
            resources: request.resources,
            action: PermissionResponseAction.GRANT,
          );
        } else {
          // Check if any permission is permanently denied
          final hasPermanentlyDenied = requestResult.values.any((status) => status.isPermanentlyDenied);

          if (hasPermanentlyDenied) {
            // Show dialog to guide user to app settings
            final shouldOpenSettings = await _showPermissionDeniedDialog();
            if (shouldOpenSettings == true) {
              await openAppSettings();
            }
          }

          return PermissionResponse(
            resources: request.resources,
            action: PermissionResponseAction.DENY,
          );
        }
      } else {
        // All permissions are either granted or permanently denied
        final hasPermanentlyDenied = status.values.any((status) => status.isPermanentlyDenied);

        if (hasPermanentlyDenied) {
          final shouldOpenSettings = await _showPermissionDeniedDialog();
          if (shouldOpenSettings == true) {
            await openAppSettings();

            // Re-check permissions after potentially changing settings
            final recheckResult = <Permission, PermissionStatus>{
              for (final permission in permissions) permission: await permission.status,
            };
            final allGrantedAfterSettings = recheckResult.values.every((status) => status.isGranted);

            return PermissionResponse(
              resources: request.resources,
              action: allGrantedAfterSettings ? PermissionResponseAction.GRANT : PermissionResponseAction.DENY,
            );
          }
        }

        return PermissionResponse(
          resources: request.resources,
          action: PermissionResponseAction.DENY,
        );
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s, tag: _tag, tagWithTrace: false);
      // On error, deny the permission to be safe
      return PermissionResponse(
        resources: request.resources,
        action: PermissionResponseAction.DENY,
      );
    }
  }

  // Show dialog when permissions are permanently denied
  Future<bool?> _showPermissionDeniedDialog() async {
    if (!mounted) {
      return null;
    }

    return TinyDialog.show<bool>(
      text:
          'Permissions are required for this webpage to function properly. '
          'Please grant the necessary permissions in app settings.',
      buttonsBuilder: (context) => const TinyDialogButtonGroup(),
    );
  }

  Future<NavigationActionPolicy> _shouldOverrideUrlLoading(
    InAppWebViewController controller,
    NavigationAction navigationAction,
  ) async {
    if (!mounted || _webViewController == null) {
      return NavigationActionPolicy.CANCEL;
    }

    final uri = navigationAction.request.url;
    if (uri == null) {
      return NavigationActionPolicy.CANCEL;
    }

    LogUtil.d(
      'shouldOverrideUrlLoading '
      '[url=${navigationAction.request.url?.rawValue}] '
      '[headers=${navigationAction.request.headers}]',
    );

    if (uri.scheme == AppLinkHelper.scheme) {
      final firstPath = uri.pathSegments.firstOrNull;
      if (firstPath == SchemeLinkHandler.methodWebview) {
        final url = (uri.queryParameters['url'] ?? '').trim();

        late final String decodedUrl;
        try {
          decodedUrl = Uri.decodeComponent(url);
        } catch (e, s) {
          handleExceptions(error: e, stackTrace: s);
          return NavigationActionPolicy.CANCEL;
        }

        final parsedUrl = Uri.tryParse(decodedUrl);
        if (parsedUrl == null || !parsedUrl.hasAbsolutePath) {
          return NavigationActionPolicy.CANCEL;
        }

        controller.loadUrl(
          urlRequest: _getRequestByUrl(
            parsedUrl.toString(),
            withNoCache: false,
          ),
        );
      }

      // Try to handles other card3:// link by the helper.
      await AppLinkHelper.handleUri(uri);

      // Do not handle it anymore, otherwise the app might be opened again.
      return NavigationActionPolicy.CANCEL;
    }

    // Determine if the link can be launched natively.
    if (!RegExp(r'https?').hasMatch(uri.scheme)) {
      if (await canLaunchUrl(uri)) {
        final launch = await askToLaunchUrlExternally();
        if (launch == true) {
          launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      }

      // Cancel whether the link can be launched or not.
      return NavigationActionPolicy.CANCEL;
    }

    // Proceed with regular url.
    return NavigationActionPolicy.ALLOW;
  }

  void _onProgressChanged(InAppWebViewController controller, int progress) {
    final value = math.max(2, progress) / 100;
    progressController.add(value);
    if (value < 1) {
      _progressCancelTimer?.cancel();
    } else {
      cancelProgress();
    }
  }

  void _onTitleChanged(InAppWebViewController controller, String? title) {
    if (mounted) {
      _titleNotifier.value = title;
    }
  }

  void _onUpdateVisitedHistory(
    InAppWebViewController controller,
    WebUri? url,
    bool? isReload,
  ) {
    if (!mounted) {
      return;
    }
    LogUtil.d(
      'WebView onUpdateVisitedHistory: $url, $isReload',
      tag: _tag,
      tagWithTrace: false,
    );
    if (url?.toString() case final url?) {
      _currentUrl.value = url;
    }
    cancelProgress();
    routeDuration.delay.then((_) async {
      safeSetState(() {
        Future.wait<void>([
          controller.canGoBack().then((r) => _canWebViewGoBack = r, onError: (_) => false),
        ]);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final Widget child;
    if (_errorMessage != null) {
      child = _buildErrorWidget();
    } else {
      child = SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            // 确保WebView完全填充容器
            Positioned.fill(
              child: _InAppWebViewWithAnalytics(
                initialSettings: InAppWebViewSettings(
                  allowsInlineMediaPlayback: true,
                  isInspectable: !envs.isSealed,
                  applicationNameForUserAgent: PackageUtil.userAgent,
                  mediaPlaybackRequiresUserGesture: false,
                  builtInZoomControls: false,
                  javaScriptCanOpenWindowsAutomatically: true,
                  mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
                  incognito: false,
                ),
                initialUrlRequest: _getRequestByUrl(widget.url, withNoCache: false),
                onWebViewCreated: _onWebViewCreated,
                onLoadStart: _onLoadStart,
                onLoadStop: _onLoadStop,
                onCloseWindow: _onCloseWindow,
                onPermissionRequest: _onPermissionRequest,
                shouldOverrideUrlLoading: _shouldOverrideUrlLoading,
                onUpdateVisitedHistory: _onUpdateVisitedHistory,
                onProgressChanged: _onProgressChanged,
                onTitleChanged: _onTitleChanged,
                onRenderProcessUnresponsive: (controller, url) async {
                  await _reloadWebView(controller);
                  return null;
                },
              ),
            ),
            Positioned.fill(
              bottom: null,
              child: StreamBuilder<double>(
                initialData: 0.0,
                stream: progressController.stream,
                builder: (_, data) => LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  color: context.themeColor,
                  minHeight: 2,
                  value: data.data,
                ),
              ),
            ),
          ],
        ),
      );
    }
    return PopScope(
      canPop: _webViewController == null || !_canWebViewGoBack,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          return;
        }
        _webViewController?.goBack();
      },
      child: AppScaffold(
        title: widget.title,
        titleStyle: const TextStyle(color: Colors.white, fontSize: 18.0),
        titleBuilder: (context) => ValueListenableBuilder(
          valueListenable: _titleNotifier,
          builder: (context, value, _) => Text(
            value ?? '',
            maxLines: 1,
            overflow: TextOverflow.fade,
            softWrap: false,
          ),
        ),
        bodyPadding: EdgeInsets.zero,
        appBarPadding: const EdgeInsets.symmetric(horizontal: 12.0),
        appBarActions: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: _reloadWebView,
                padding: EdgeInsets.zero,
                constraints: BoxConstraints.tight(const Size.square(42.0)),
                tooltip: 'Refresh page',
                icon: const Icon(Icons.refresh, color: Colors.white),
              ),
              Gap.h(1, height: 24.0, color: context.meTheme.cardColor),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                padding: EdgeInsets.zero,
                constraints: BoxConstraints.tight(const Size.square(42.0)),
                tooltip: MaterialLocalizations.of(context).closeButtonTooltip,
                icon: const Icon(Icons.close, color: Colors.white),
              ),
            ],
          ),
        ],
        onBackButtonPressed: () {
          if (_canWebViewGoBack) {
            _webViewController?.goBack();
          } else {
            Navigator.maybePop(context);
          }
        },
        body: child,
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load page',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    safeSetState(() {
                      _errorMessage = null;
                    });
                    // 重新加载页面
                    _reloadWebView();
                  },
                  child: const Text('Retry'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Close'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _InAppWebViewWithAnalytics extends InAppWebView {
  _InAppWebViewWithAnalytics({
    super.key,
    this.onWebViewCreated,
    this.onLoadStart,
    this.onLoadStop,
    this.onCloseWindow,
    this.onPermissionRequest,
    this.shouldOverrideUrlLoading,
    this.onUpdateVisitedHistory,
    this.onProgressChanged,
    this.onTitleChanged,
    this.onRenderProcessUnresponsive,
    this.onReceivedError,
    this.onReceivedHttpError,
    this.onConsoleMessage,
    // Pass through all other parameters to super
    super.gestureRecognizers,
    super.windowId,
    super.headlessWebView,
    super.keepAlive,
    super.preventGestureDelay,
    super.layoutDirection,
    super.webViewEnvironment,
    super.initialData,
    super.initialFile,
    super.initialSettings,
    super.initialUrlRequest,
    super.initialUserScripts,
    super.pullToRefreshController,
    super.findInteractionController,
    super.contextMenu,
    super.onPageCommitVisible,
    super.onAjaxProgress,
    super.onAjaxReadyStateChange,
    super.onCreateWindow,
    super.onWindowFocus,
    super.onWindowBlur,
    super.onDownloadStarting,
    super.onJsAlert,
    super.onJsConfirm,
    super.onJsPrompt,
    super.onReceivedClientCertRequest,
    super.onReceivedHttpAuthRequest,
    super.onReceivedServerTrustAuthRequest,
    super.onScrollChanged,
    super.shouldInterceptAjaxRequest,
    super.shouldInterceptFetchRequest,
    super.onEnterFullscreen,
    super.onExitFullscreen,
    super.onOverScrolled,
    super.onZoomScaleChanged,
    super.onNavigationResponse,
    super.onReceivedIcon,
    super.onReceivedLoginRequest,
    super.onPermissionRequestCanceled,
    super.onRequestFocus,
    super.onReceivedTouchIconUrl,
    super.onRenderProcessGone,
    super.onRenderProcessResponsive,
    super.onSafeBrowsingHit,
    super.onWebContentProcessDidTerminate,
    super.shouldAllowDeprecatedTLS,
    super.shouldInterceptRequest,
    super.onCameraCaptureStateChanged,
    super.onMicrophoneCaptureStateChanged,
    super.onContentSizeChanged,
    super.onProcessFailed,
    super.onAcceleratorKeyPressed,
  }) : super(
          onWebViewCreated: onWebViewCreated != null ? _wrapOnWebViewCreated(onWebViewCreated) : null,
          onLoadStart: onLoadStart != null ? _wrapOnLoadStart(onLoadStart) : null,
          onLoadStop: onLoadStop != null ? _wrapOnLoadStop(onLoadStop) : null,
          onCloseWindow: onCloseWindow != null ? _wrapOnCloseWindow(onCloseWindow) : null,
          onPermissionRequest: onPermissionRequest != null ? _wrapOnPermissionRequest(onPermissionRequest) : null,
          shouldOverrideUrlLoading: shouldOverrideUrlLoading != null ? _wrapShouldOverrideUrlLoading(shouldOverrideUrlLoading) : null,
          onUpdateVisitedHistory: onUpdateVisitedHistory != null ? _wrapOnUpdateVisitedHistory(onUpdateVisitedHistory) : null,
          onProgressChanged: onProgressChanged != null ? _wrapOnProgressChanged(onProgressChanged) : null,
          onTitleChanged: onTitleChanged != null ? _wrapOnTitleChanged(onTitleChanged) : null,
          onRenderProcessUnresponsive: onRenderProcessUnresponsive != null ? _wrapOnRenderProcessUnresponsive(onRenderProcessUnresponsive) : null,
          onReceivedError: onReceivedError != null ? _wrapOnReceivedError(onReceivedError) : null,
          onReceivedHttpError: onReceivedHttpError != null ? _wrapOnReceivedHttpError(onReceivedHttpError) : null,
          onConsoleMessage: onConsoleMessage != null ? _wrapOnConsoleMessage(onConsoleMessage) : null,
        );

  // Original callback functions
  final void Function(InAppWebViewController controller)? onWebViewCreated;
  final void Function(InAppWebViewController controller, WebUri? url)? onLoadStart;
  final void Function(InAppWebViewController controller, WebUri? url)? onLoadStop;
  final void Function(InAppWebViewController controller)? onCloseWindow;
  final FutureOr<PermissionResponse?> Function(InAppWebViewController controller, PermissionRequest permissionRequest)? onPermissionRequest;
  final FutureOr<NavigationActionPolicy?> Function(InAppWebViewController controller, NavigationAction navigationAction)? shouldOverrideUrlLoading;
  final void Function(InAppWebViewController controller, WebUri? url, bool? isReload)? onUpdateVisitedHistory;
  final void Function(InAppWebViewController controller, int progress)? onProgressChanged;
  final void Function(InAppWebViewController controller, String? title)? onTitleChanged;
  final FutureOr<WebViewRenderProcessAction?> Function(InAppWebViewController controller, WebUri? url)? onRenderProcessUnresponsive;
  final void Function(InAppWebViewController controller, WebResourceRequest request, WebResourceError error)? onReceivedError;
  final void Function(InAppWebViewController controller, WebResourceRequest request, WebResourceResponse errorResponse)? onReceivedHttpError;
  final void Function(InAppWebViewController controller, ConsoleMessage consoleMessage)? onConsoleMessage;

  // Helper function to safely get URL from controller
  static void _logEventWithUrl(
    InAppWebViewController controller,
    String eventName, {
    Map<String, Object?>? additionalParameters,
  }) {
    controller.getUrl().then((url) {
      MEAnalytics.instance.logEvent(
        eventName,
        parameters: {
          'url': url?.toString(),
          ...?additionalParameters,
        },
      );
    }).catchError((_) {
      MEAnalytics.instance.logEvent(
        eventName,
        parameters: additionalParameters ?? {},
      );
    });
  }

  // Static wrapper functions for analytics
  static void Function(InAppWebViewController controller) _wrapOnWebViewCreated(
    void Function(InAppWebViewController controller) original,
  ) {
    return (controller) {
      _logEventWithUrl(controller, 'webview_created');
      original(controller);
    };
  }

  static void Function(InAppWebViewController controller, WebUri? url) _wrapOnLoadStart(
    void Function(InAppWebViewController controller, WebUri? url) original,
  ) {
    return (controller, url) {
      MEAnalytics.instance.logEvent(
        'webview_load_start',
        parameters: {
          'url': url?.toString(),
          'host': url?.host,
        },
      );
      original(controller, url);
    };
  }

  static void Function(InAppWebViewController controller, WebUri? url) _wrapOnLoadStop(
    void Function(InAppWebViewController controller, WebUri? url) original,
  ) {
    return (controller, url) {
      MEAnalytics.instance.logEvent(
        'webview_load_stop',
        parameters: {
          'url': url?.toString(),
          'host': url?.host,
        },
      );
      original(controller, url);
    };
  }

  static void Function(InAppWebViewController controller) _wrapOnCloseWindow(
    void Function(InAppWebViewController controller) original,
  ) {
    return (controller) {
      _logEventWithUrl(controller, 'webview_close_window');
      original(controller);
    };
  }

  static FutureOr<PermissionResponse?> Function(InAppWebViewController controller, PermissionRequest permissionRequest) _wrapOnPermissionRequest(
    FutureOr<PermissionResponse?> Function(InAppWebViewController controller, PermissionRequest permissionRequest) original,
  ) {
    return (controller, permissionRequest) {
      _logEventWithUrl(
        controller,
        'webview_permission_request',
        additionalParameters: {
          'resources': permissionRequest.resources.map((r) => r.toString()).toList(),
        },
      );
      return original(controller, permissionRequest);
    };
  }

  static FutureOr<NavigationActionPolicy?> Function(InAppWebViewController controller, NavigationAction navigationAction) _wrapShouldOverrideUrlLoading(
    FutureOr<NavigationActionPolicy?> Function(InAppWebViewController controller, NavigationAction navigationAction) original,
  ) {
    return (controller, navigationAction) {
      _logEventWithUrl(
        controller,
        'webview_should_override_url_loading',
        additionalParameters: {
          'target_url': navigationAction.request.url?.toString(),
          'navigation_type': navigationAction.navigationType.toString(),
        },
      );
      return original(controller, navigationAction);
    };
  }

  static void Function(InAppWebViewController controller, WebUri? url, bool? isReload) _wrapOnUpdateVisitedHistory(
    void Function(InAppWebViewController controller, WebUri? url, bool? isReload) original,
  ) {
    return (controller, url, isReload) {
      MEAnalytics.instance.logEvent(
        'webview_update_visited_history',
        parameters: {
          'url': url?.toString(),
          'host': url?.host,
          'is_reload': isReload,
        },
      );
      original(controller, url, isReload);
    };
  }

  static void Function(InAppWebViewController controller, int progress) _wrapOnProgressChanged(
    void Function(InAppWebViewController controller, int progress) original,
  ) {
    return (controller, progress) {
      // Only log significant progress milestones to avoid spam
      if (progress == 0 || progress == 25 || progress == 50 || progress == 75 || progress == 100) {
        _logEventWithUrl(
          controller,
          'webview_progress_changed',
          additionalParameters: {
            'progress': progress,
          },
        );
      }
      original(controller, progress);
    };
  }

  static void Function(InAppWebViewController controller, String? title) _wrapOnTitleChanged(
    void Function(InAppWebViewController controller, String? title) original,
  ) {
    return (controller, title) {
      _logEventWithUrl(
        controller,
        'webview_title_changed',
        additionalParameters: {
          'title': title,
        },
      );
      original(controller, title);
    };
  }

  static FutureOr<WebViewRenderProcessAction?> Function(InAppWebViewController controller, WebUri? url) _wrapOnRenderProcessUnresponsive(
    FutureOr<WebViewRenderProcessAction?> Function(InAppWebViewController controller, WebUri? url) original,
  ) {
    return (controller, url) {
      MEAnalytics.instance.logEvent(
        'webview_render_process_unresponsive',
        parameters: {
          'url': url?.toString(),
          'host': url?.host,
        },
      );
      return original(controller, url);
    };
  }

  static void Function(InAppWebViewController controller, WebResourceRequest request, WebResourceError error) _wrapOnReceivedError(
    void Function(InAppWebViewController controller, WebResourceRequest request, WebResourceError error) original,
  ) {
    return (controller, request, error) {
      MEAnalytics.instance.logEvent(
        'webview_received_error',
        parameters: {
          'url': request.url?.toString(),
          'error_code': error.type?.toString(),
          'error_description': error.description,
        },
      );
      original(controller, request, error);
    };
  }

  static void Function(InAppWebViewController controller, WebResourceRequest request, WebResourceResponse errorResponse) _wrapOnReceivedHttpError(
    void Function(InAppWebViewController controller, WebResourceRequest request, WebResourceResponse errorResponse) original,
  ) {
    return (controller, request, errorResponse) {
      MEAnalytics.instance.logEvent(
        'webview_received_http_error',
        parameters: {
          'url': request.url.toString(),
          'status_code': errorResponse.statusCode,
          'reason_phrase': errorResponse.reasonPhrase,
        },
      );
      original(controller, request, errorResponse);
    };
  }

  static void Function(InAppWebViewController controller, ConsoleMessage consoleMessage) _wrapOnConsoleMessage(
    void Function(InAppWebViewController controller, ConsoleMessage consoleMessage) original,
  ) {
    return (controller, consoleMessage) {
      // Only log error and warning console messages to avoid spam
      if (consoleMessage.messageLevel == ConsoleMessageLevel.ERROR ||
          consoleMessage.messageLevel == ConsoleMessageLevel.WARNING) {
        _logEventWithUrl(
          controller,
          'webview_console_message',
          additionalParameters: {
            'message_level': consoleMessage.messageLevel.toString(),
            'message': consoleMessage.message,
          },
        );
      }
      original(controller, consoleMessage);
    };
  }

}
