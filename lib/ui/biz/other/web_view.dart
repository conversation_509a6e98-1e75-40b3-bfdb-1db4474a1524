import 'dart:async';
import 'dart:math' as math;
import 'dart:typed_data' show Uint8List;

import 'package:card3/exports.dart';
import 'package:flutter/foundation.dart' show Factory;
import 'package:flutter/gestures.dart' show OneSequenceGestureRecognizer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show SystemChannels;
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:url_launcher/url_launcher.dart';

import '/constants/envs.dart' as envs show isSealed;
import '/feat/bridge/module/in_house.dart';
import '/feat/bridge/module/launch.dart' show askToLaunchUrlExternally;
import '/feat/link/handler.dart' show SchemeLinkHandler;
import '/feat/link/helper.dart' show AppLinkHelper;

const _tag = '🌐 WebView';

@FFRoute(name: '/webview')
class WebViewPage extends ConsumerStatefulWidget {
  const WebViewPage({
    super.key,
    required this.url,
    this.title,
  });

  final String url;
  final String? title;

  @override
  ConsumerState<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends ConsumerState<WebViewPage> with WidgetsBindingObserver {
  final progressController = StreamController<double>.broadcast();
  late final _titleNotifier = ValueNotifier<String?>(widget.title?.trim());
  late final _currentUrl = ValueNotifier(widget.url.trim());

  String? get urlDomain => Uri.tryParse(_currentUrl.value)?.host;

  Timer? _progressCancelTimer;

  InAppWebViewController? get _webViewController {
    if (mounted) {
      return _inAppWebViewController;
    }
    return null;
  }

  InAppWebViewController? _inAppWebViewController;

  bool _canWebViewGoBack = false;
  String? _errorMessage;

  Completer<void>? _closingLock;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    SystemChannels.textInput.invokeMethod<void>('TextInput.hide');
    WidgetsBinding.instance.removeObserver(this);
    progressController.close();
    _webViewController?.dispose();
    super.dispose();
  }

  void cancelProgress([Duration duration = const Duration(seconds: 1)]) {
    _progressCancelTimer?.cancel();
    _progressCancelTimer = Timer(duration, () {
      if (mounted && !progressController.isClosed) {
        progressController.add(0.0);
      }
    });
  }

  URLRequest _getRequestByUrl(String url, {required bool withNoCache}) {
    return URLRequest(
      url: WebUri(url),
      headers: {
        'User-Agent': PackageUtil.userAgent,
        if (withNoCache) 'Cache-Control': 'no-cache, no-store, must-revalidate',
        if (withNoCache) 'Pragma': 'no-cache',
        if (withNoCache) 'Expires': '0',
      },
    );
  }

  // 重新加载WebView
  Future<void> _reloadWebView([InAppWebViewController? controller]) async {
    controller ??= _webViewController;
    if (!mounted || controller == null) {
      return;
    }

    setState(() {
      _errorMessage = null;
    });

    try {
      // 添加5秒超时
      await _performReload(controller).timeout(const Duration(seconds: 5));
    } catch (e) {
      safeSetState(() {
        _errorMessage = 'Failed to reload page: $e';
      });
      rethrow;
    }
  }

  // 执行实际的重新加载操作
  Future<void> _performReload(InAppWebViewController controller) {
    return controller.loadUrl(
      urlRequest: _getRequestByUrl(_currentUrl.value, withNoCache: true),
    );
  }

  void _onWebViewCreated(InAppWebViewController controller) {
    _inAppWebViewController = controller;
    defaultInHouseBridge.inject(context, ref, controller);
    controller.addJavaScriptHandler(
      handlerName: '__onTitleChanged__',
      callback: (JavaScriptHandlerFunctionData data) {
        final title = data.args.firstOrNull?.toString();
        _onTitleChanged(controller, title);
      },
    );
  }

  Future<void> _onLoadStart(
    InAppWebViewController controller,
    WebUri? url,
  ) async {
    safeSetState(() {
      if (url?.toString() case final url?) {
        _currentUrl.value = url;
      }
      _errorMessage = null;
    });
  }

  Future<void> _onLoadStop(
    InAppWebViewController controller,
    WebUri? url,
  ) async {
    if (!mounted) {
      return;
    }
    if (url?.toString() case final url?) {
      _currentUrl.value = url;
    }
    cancelProgress();

    if (url?.host case final host? when defaultInHouseBridgeHostRegExp.hasMatch(host)) {
      final token = BoxService.getToken() ?? '';
      await controller.evaluateJavascript(
        source: inHouseJSSourceInjectToken(token),
      );
    }
  }

  Future<void> _onCloseWindow(InAppWebViewController controller) {
    if (_closingLock case final lock?) {
      return lock.future;
    }

    final lock = _closingLock = Completer<void>();
    TinyDialog.show<bool>(
          text: 'Are you sure you want to close this page?',
          buttonsBuilder: (context) => const TinyDialogButtonGroup(),
        )
        .then(
          (result) {
            lock.complete();
            if (result == true && mounted) {
              controller.stopLoading();
              Navigator.of(context).maybePop();
            }
          },
          onError: (e, s) {
            lock.completeError(e, s);
          },
        )
        .whenComplete(() {
          _closingLock = null;
        });
    return lock.future;
  }

  Future<PermissionResponse> _onPermissionRequest(
    InAppWebViewController controller,
    PermissionRequest request,
  ) async {
    LogUtil.d('WebView permission request: ${request.resources}', tag: _tag);

    // Map WebView permission resources to system permissions
    final permissions = <Permission>[];

    for (final resource in request.resources) {
      if (resource == PermissionResourceType.CAMERA_AND_MICROPHONE) {
        permissions.add(Permission.camera);
        permissions.add(Permission.microphone);
      } else if (resource == PermissionResourceType.CAMERA) {
        permissions.add(Permission.camera);
      } else if (resource == PermissionResourceType.MICROPHONE) {
        permissions.add(Permission.microphone);
      } else if (resource == PermissionResourceType.GEOLOCATION) {
        permissions.add(Permission.location);
      } else {
        LogUtil.w('Unknown permission resource: $resource', tag: _tag);
      }
    }

    if (permissions.isEmpty) {
      // If no mappable permissions, grant all by default
      return PermissionResponse(
        resources: request.resources,
        action: PermissionResponseAction.GRANT,
      );
    }

    try {
      // Check current permission status
      final status = await permissions.request();

      // Check if all permissions are already granted
      final allGranted = status.values.every((status) => status.isGranted);

      if (allGranted) {
        return PermissionResponse(
          resources: request.resources,
          action: PermissionResponseAction.GRANT,
        );
      }

      // Request permissions that are not granted
      final notGrantedPermissions = status.entries
          .where((entry) => !entry.value.isGranted && !entry.value.isPermanentlyDenied)
          .map((entry) => entry.key)
          .toList();

      if (notGrantedPermissions.isNotEmpty) {
        final requestResult = await notGrantedPermissions.request();

        // Check if all requested permissions are now granted
        final allRequestedGranted = requestResult.values.every((s) => s.isGranted);

        if (allRequestedGranted) {
          return PermissionResponse(
            resources: request.resources,
            action: PermissionResponseAction.GRANT,
          );
        } else {
          // Check if any permission is permanently denied
          final hasPermanentlyDenied = requestResult.values.any((status) => status.isPermanentlyDenied);

          if (hasPermanentlyDenied) {
            // Show dialog to guide user to app settings
            final shouldOpenSettings = await _showPermissionDeniedDialog();
            if (shouldOpenSettings == true) {
              await openAppSettings();
            }
          }

          return PermissionResponse(
            resources: request.resources,
            action: PermissionResponseAction.DENY,
          );
        }
      } else {
        // All permissions are either granted or permanently denied
        final hasPermanentlyDenied = status.values.any((status) => status.isPermanentlyDenied);

        if (hasPermanentlyDenied) {
          final shouldOpenSettings = await _showPermissionDeniedDialog();
          if (shouldOpenSettings == true) {
            await openAppSettings();

            // Re-check permissions after potentially changing settings
            final recheckResult = <Permission, PermissionStatus>{
              for (final permission in permissions) permission: await permission.status,
            };
            final allGrantedAfterSettings = recheckResult.values.every((status) => status.isGranted);

            return PermissionResponse(
              resources: request.resources,
              action: allGrantedAfterSettings ? PermissionResponseAction.GRANT : PermissionResponseAction.DENY,
            );
          }
        }

        return PermissionResponse(
          resources: request.resources,
          action: PermissionResponseAction.DENY,
        );
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s, tag: _tag, tagWithTrace: false);
      // On error, deny the permission to be safe
      return PermissionResponse(
        resources: request.resources,
        action: PermissionResponseAction.DENY,
      );
    }
  }

  // Show dialog when permissions are permanently denied
  Future<bool?> _showPermissionDeniedDialog() async {
    if (!mounted) {
      return null;
    }

    return TinyDialog.show<bool>(
      text:
          'Permissions are required for this webpage to function properly. '
          'Please grant the necessary permissions in app settings.',
      buttonsBuilder: (context) => const TinyDialogButtonGroup(),
    );
  }

  Future<NavigationActionPolicy> _shouldOverrideUrlLoading(
    InAppWebViewController controller,
    NavigationAction navigationAction,
  ) async {
    if (!mounted || _webViewController == null) {
      return NavigationActionPolicy.CANCEL;
    }

    final uri = navigationAction.request.url;
    if (uri == null) {
      return NavigationActionPolicy.CANCEL;
    }

    LogUtil.d(
      'shouldOverrideUrlLoading '
      '[url=${navigationAction.request.url?.rawValue}] '
      '[headers=${navigationAction.request.headers}]',
    );

    if (uri.scheme == AppLinkHelper.scheme) {
      final firstPath = uri.pathSegments.firstOrNull;
      if (firstPath == SchemeLinkHandler.methodWebview) {
        final url = (uri.queryParameters['url'] ?? '').trim();

        late final String decodedUrl;
        try {
          decodedUrl = Uri.decodeComponent(url);
        } catch (e, s) {
          handleExceptions(error: e, stackTrace: s);
          return NavigationActionPolicy.CANCEL;
        }

        final parsedUrl = Uri.tryParse(decodedUrl);
        if (parsedUrl == null || !parsedUrl.hasAbsolutePath) {
          return NavigationActionPolicy.CANCEL;
        }

        controller.loadUrl(
          urlRequest: _getRequestByUrl(
            parsedUrl.toString(),
            withNoCache: false,
          ),
        );
      }

      // Try to handles other card3:// link by the helper.
      await AppLinkHelper.handleUri(uri);

      // Do not handle it anymore, otherwise the app might be opened again.
      return NavigationActionPolicy.CANCEL;
    }

    // Determine if the link can be launched natively.
    if (!RegExp(r'https?').hasMatch(uri.scheme)) {
      if (await canLaunchUrl(uri)) {
        final launch = await askToLaunchUrlExternally();
        if (launch == true) {
          launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      }

      // Cancel whether the link can be launched or not.
      return NavigationActionPolicy.CANCEL;
    }

    // Proceed with regular url.
    return NavigationActionPolicy.ALLOW;
  }

  void _onProgressChanged(InAppWebViewController controller, int progress) {
    final value = math.max(2, progress) / 100;
    progressController.add(value);
    if (value < 1) {
      _progressCancelTimer?.cancel();
    } else {
      cancelProgress();
    }
  }

  void _onTitleChanged(InAppWebViewController controller, String? title) {
    if (mounted) {
      _titleNotifier.value = title;
    }
  }

  void _onUpdateVisitedHistory(
    InAppWebViewController controller,
    WebUri? url,
    bool? isReload,
  ) {
    if (!mounted) {
      return;
    }
    LogUtil.d(
      'WebView onUpdateVisitedHistory: $url, $isReload',
      tag: _tag,
      tagWithTrace: false,
    );
    if (url?.toString() case final url?) {
      _currentUrl.value = url;
    }
    cancelProgress();
    routeDuration.delay.then((_) async {
      safeSetState(() {
        Future.wait<void>([
          controller.canGoBack().then((r) => _canWebViewGoBack = r, onError: (_) => false),
        ]);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final Widget child;
    if (_errorMessage != null) {
      child = _buildErrorWidget();
    } else {
      child = SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            // 确保WebView完全填充容器
            Positioned.fill(
              child: InAppWebView(
                initialSettings: InAppWebViewSettings(
                  allowsInlineMediaPlayback: true,
                  isInspectable: !envs.isSealed,
                  applicationNameForUserAgent: PackageUtil.userAgent,
                  mediaPlaybackRequiresUserGesture: false,
                  builtInZoomControls: false,
                  javaScriptCanOpenWindowsAutomatically: true,
                  mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
                  incognito: false,
                ),
                initialUrlRequest: _getRequestByUrl(widget.url, withNoCache: false),
                onWebViewCreated: _onWebViewCreated,
                onLoadStart: _onLoadStart,
                onLoadStop: _onLoadStop,
                onCloseWindow: _onCloseWindow,
                onPermissionRequest: _onPermissionRequest,
                shouldOverrideUrlLoading: _shouldOverrideUrlLoading,
                onUpdateVisitedHistory: _onUpdateVisitedHistory,
                onProgressChanged: _onProgressChanged,
                onTitleChanged: _onTitleChanged,
                onRenderProcessUnresponsive: (controller, url) async {
                  await _reloadWebView(controller);
                  return null;
                },
              ),
            ),
            Positioned.fill(
              bottom: null,
              child: StreamBuilder<double>(
                initialData: 0.0,
                stream: progressController.stream,
                builder: (_, data) => LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  color: context.themeColor,
                  minHeight: 2,
                  value: data.data,
                ),
              ),
            ),
          ],
        ),
      );
    }
    return PopScope(
      canPop: _webViewController == null || !_canWebViewGoBack,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          return;
        }
        _webViewController?.goBack();
      },
      child: AppScaffold(
        title: widget.title,
        titleStyle: const TextStyle(color: Colors.white, fontSize: 18.0),
        titleBuilder: (context) => ValueListenableBuilder(
          valueListenable: _titleNotifier,
          builder: (context, value, _) => Text(
            value ?? '',
            maxLines: 1,
            overflow: TextOverflow.fade,
            softWrap: false,
          ),
        ),
        bodyPadding: EdgeInsets.zero,
        appBarPadding: const EdgeInsets.symmetric(horizontal: 12.0),
        appBarActions: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: _reloadWebView,
                padding: EdgeInsets.zero,
                constraints: BoxConstraints.tight(const Size.square(42.0)),
                tooltip: 'Refresh page',
                icon: const Icon(Icons.refresh, color: Colors.white),
              ),
              Gap.h(1, height: 24.0, color: context.meTheme.cardColor),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                padding: EdgeInsets.zero,
                constraints: BoxConstraints.tight(const Size.square(42.0)),
                tooltip: MaterialLocalizations.of(context).closeButtonTooltip,
                icon: const Icon(Icons.close, color: Colors.white),
              ),
            ],
          ),
        ],
        onBackButtonPressed: () {
          if (_canWebViewGoBack) {
            _webViewController?.goBack();
          } else {
            Navigator.maybePop(context);
          }
        },
        body: child,
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load page',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    safeSetState(() {
                      _errorMessage = null;
                    });
                    // 重新加载页面
                    _reloadWebView();
                  },
                  child: const Text('Retry'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Close'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _InAppWebViewWithAnalytics extends InAppWebView {
  _InAppWebViewWithAnalytics({
    super.key,
    this.onWebViewCreated,
    Set<Factory<OneSequenceGestureRecognizer>>? gestureRecognizers,
    int? windowId,
    HeadlessInAppWebView? headlessWebView,
    InAppWebViewKeepAlive? keepAlive,
    bool? preventGestureDelay,
    TextDirection? layoutDirection,
    WebViewEnvironment? webViewEnvironment,
    InAppWebViewInitialData? initialData,
    String? initialFile,
    InAppWebViewSettings? initialSettings,
    URLRequest? initialUrlRequest,
    UnmodifiableListView<UserScript>? initialUserScripts,
    PullToRefreshController? pullToRefreshController,
    FindInteractionController? findInteractionController,
    ContextMenu? contextMenu,
    void Function(InAppWebViewController controller, WebUri? url)? onPageCommitVisible,
    void Function(InAppWebViewController controller, String? title)? onTitleChanged,
    FutureOr<AjaxRequestAction> Function(InAppWebViewController controller, AjaxRequest ajaxRequest)? onAjaxProgress,
    FutureOr<AjaxRequestAction?> Function(InAppWebViewController controller, AjaxRequest ajaxRequest)?
    onAjaxReadyStateChange,
    void Function(InAppWebViewController controller, ConsoleMessage consoleMessage)? onConsoleMessage,
    FutureOr<bool?> Function(InAppWebViewController controller, CreateWindowAction createWindowAction)? onCreateWindow,
    void Function(InAppWebViewController controller)? onCloseWindow,
    void Function(InAppWebViewController controller)? onWindowFocus,
    void Function(InAppWebViewController controller)? onWindowBlur,
    FutureOr<DownloadStartResponse?> Function(
      InAppWebViewController controller,
      DownloadStartRequest downloadStartRequest,
    )?
    onDownloadStarting,
    FutureOr<JsAlertResponse?> Function(InAppWebViewController controller, JsAlertRequest jsAlertRequest)? onJsAlert,
    FutureOr<JsConfirmResponse?> Function(InAppWebViewController controller, JsConfirmRequest jsConfirmRequest)?
    onJsConfirm,
    FutureOr<JsPromptResponse?> Function(InAppWebViewController controller, JsPromptRequest jsPromptRequest)?
    onJsPrompt,
    void Function(InAppWebViewController controller, WebResourceRequest request, WebResourceError error)?
    onReceivedError,
    void Function(InAppWebViewController controller, WebResourceRequest request, WebResourceResponse errorResponse)?
    onReceivedHttpError,
    void Function(InAppWebViewController controller, LoadedResource resource)? onLoadResource,
    FutureOr<CustomSchemeResponse?> Function(InAppWebViewController controller, WebResourceRequest request)?
    onLoadResourceWithCustomScheme,
    void Function(InAppWebViewController controller, WebUri? url)? onLoadStart,
    void Function(InAppWebViewController controller, WebUri? url)? onLoadStop,
    void Function(InAppWebViewController controller, InAppWebViewHitTestResult hitTestResult)? onLongPressHitTestResult,

    void Function(InAppWebViewController controller, int progress)? onProgressChanged,
    FutureOr<ClientCertResponse?> Function(InAppWebViewController controller, ClientCertChallenge challenge)?
    onReceivedClientCertRequest,
    FutureOr<HttpAuthResponse?> Function(InAppWebViewController controller, HttpAuthenticationChallenge challenge)?
    onReceivedHttpAuthRequest,
    FutureOr<ServerTrustAuthResponse?> Function(InAppWebViewController controller, ServerTrustChallenge challenge)?
    onReceivedServerTrustAuthRequest,
    void Function(InAppWebViewController controller, int x, int y)? onScrollChanged,
    void Function(InAppWebViewController controller, WebUri? url, bool? isReload)? onUpdateVisitedHistory,
    FutureOr<AjaxRequest?> Function(InAppWebViewController controller, AjaxRequest ajaxRequest)?
    shouldInterceptAjaxRequest,
    FutureOr<FetchRequest?> Function(InAppWebViewController controller, FetchRequest fetchRequest)?
    shouldInterceptFetchRequest,
    FutureOr<NavigationActionPolicy?> Function(InAppWebViewController controller, NavigationAction navigationAction)?
    shouldOverrideUrlLoading,
    void Function(InAppWebViewController controller)? onEnterFullscreen,
    void Function(InAppWebViewController controller)? onExitFullscreen,
    void Function(InAppWebViewController controller, int x, int y, bool clampedX, bool clampedY)? onOverScrolled,
    void Function(InAppWebViewController controller, double oldScale, double newScale)? onZoomScaleChanged,

    @Deprecated('Use onReceivedLoginRequest instead')
    void Function(InAppWebViewController controller, LoginRequest loginRequest)? androidOnReceivedLoginRequest,
    void Function(InAppWebViewController controller)? onDidReceiveServerRedirectForProvisionalNavigation,
    FutureOr<FormResubmissionAction?> Function(InAppWebViewController controller, WebUri? url)? onFormResubmission,
    void Function(InAppWebViewController controller)? onGeolocationPermissionsHidePrompt,
    FutureOr<GeolocationPermissionShowPromptResponse?> Function(InAppWebViewController controller, String origin)?
    onGeolocationPermissionsShowPrompt,
    FutureOr<JsBeforeUnloadResponse?> Function(
      InAppWebViewController controller,
      JsBeforeUnloadRequest jsBeforeUnloadRequest,
    )?
    onJsBeforeUnload,
    FutureOr<NavigationResponseAction?> Function(
      InAppWebViewController controller,
      NavigationResponse navigationResponse,
    )?
    onNavigationResponse,
    FutureOr<PermissionResponse?> Function(InAppWebViewController controller, PermissionRequest permissionRequest)?
    onPermissionRequest,
    void Function(InAppWebViewController controller, Uint8List icon)? onReceivedIcon,
    void Function(InAppWebViewController controller, LoginRequest loginRequest)? onReceivedLoginRequest,
    void Function(InAppWebViewController controller, PermissionRequest permissionRequest)? onPermissionRequestCanceled,
    void Function(InAppWebViewController controller)? onRequestFocus,
    void Function(InAppWebViewController controller, WebUri url, bool precomposed)? onReceivedTouchIconUrl,
    void Function(InAppWebViewController controller, RenderProcessGoneDetail detail)? onRenderProcessGone,
    FutureOr<WebViewRenderProcessAction?> Function(InAppWebViewController controller, WebUri? url)?
    onRenderProcessResponsive,
    FutureOr<WebViewRenderProcessAction?> Function(InAppWebViewController controller, WebUri? url)?
    onRenderProcessUnresponsive,
    FutureOr<SafeBrowsingResponse?> Function(
      InAppWebViewController controller,
      WebUri url,
      SafeBrowsingThreat? threatType,
    )?
    onSafeBrowsingHit,
    void Function(InAppWebViewController controller)? onWebContentProcessDidTerminate,
    FutureOr<ShouldAllowDeprecatedTLSAction?> Function(
      InAppWebViewController controller,
      URLAuthenticationChallenge challenge,
    )?
    shouldAllowDeprecatedTLS,
    FutureOr<WebResourceResponse?> Function(InAppWebViewController controller, WebResourceRequest request)?
    shouldInterceptRequest,
    FutureOr<void> Function(
      InAppWebViewController controller,
      MediaCaptureState? oldState,
      MediaCaptureState? newState,
    )?
    onCameraCaptureStateChanged,
    FutureOr<void> Function(
      InAppWebViewController controller,
      MediaCaptureState? oldState,
      MediaCaptureState? newState,
    )?
    onMicrophoneCaptureStateChanged,
    void Function(InAppWebViewController controller, Size oldContentSize, Size newContentSize)? onContentSizeChanged,
    void Function(InAppWebViewController controller, ProcessFailedDetail detail)? onProcessFailed,
    void Function(InAppWebViewController controller, AcceleratorKeyPressedDetail detail)? onAcceleratorKeyPressed,
  });

  final void Function(InAppWebViewController controller)? onWebViewCreated;
  final void Function(InAppWebViewController controller, WebUri? url)? onLoadStart;
  final void Function(InAppWebViewController controller, WebUri? url)? onLoadStop;
}
