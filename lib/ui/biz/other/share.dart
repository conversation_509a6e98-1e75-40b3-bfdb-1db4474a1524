import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';

import '/models/card.dart' show QRCodeDynamicResult;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart';
import '/ui/biz/social/profile.dart' show SocialProfilePage;
import '/ui/widgets/open_container.dart';
import 'qr_expire_border.dart';

final _codeProvider = FutureProvider.autoDispose<QRCodeDynamicResult>((ref) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  return ref.read(apiServiceProvider).getUserQRCodeDynamic(cancelToken: ct);
});

@FFRoute(name: '/share')
class SharePage extends StatelessWidget {
  const SharePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const _MainBody();
  }
}

class _MainBody extends ConsumerStatefulWidget {
  const _MainBody();

  @override
  ConsumerState<_MainBody> createState() => _MainBodyState();
}

class _MainBodyState extends ConsumerState<_MainBody> {
  Timer? _refreshTimer;
  late Timer _elapsedTimer;

  @override
  void initState() {
    super.initState();
    _initialize(refresh: false);
    _elapsedTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      final result = ref.read(_codeProvider).valueOrNull;
      if (result == null) {
        return;
      }

      safeSetState(() {});
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _elapsedTimer.cancel();
    super.dispose();
  }

  Future<void> _initialize({required bool refresh}) async {
    _refreshTimer?.cancel();

    final QRCodeDynamicResult result;
    if (refresh) {
      result = await ref.refresh(_codeProvider.future);
    } else {
      result = await ref.read(_codeProvider.future);
    }

    if (!mounted) {
      return;
    }

    final elapsed = result.expireTime.difference(DateTime.now()) - 100.milliseconds;
    if (elapsed <= Duration.zero) {
      _initialize(refresh: true);
      return;
    }

    _refreshTimer = Timer(elapsed, () => _initialize(refresh: true));
  }

  @override
  Widget build(BuildContext context) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final codeResult = ref.watch(_codeProvider);

    final backgroundColorFilter = Colors.grey.withValues(alpha: 0.1).filter;

    return AppScaffold(
      backgroundBuilder: (context) => ListView.builder(
        itemBuilder: (context, index) => Padding(
          padding: const EdgeInsets.only(bottom: 48.0),
          child: AspectRatio(
            aspectRatio: 963 / 200,
            child: Assets.icons.logoTextDark.svg(fit: BoxFit.fitWidth, colorFilter: backgroundColorFilter),
          ),
        ),
      ),
      backgroundColor: Colors.grey[900],
      bodyPadding: const EdgeInsets.all(16.0),
      body: Center(
        child: Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 48.0),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            color: context.theme.cardColor,
          ),
          child: Column(
            spacing: 16.0,
            mainAxisSize: MainAxisSize.min,
            children: [
              MEImage(
                userInfo?.avatar ?? '',
                width: 120.0,
                height: 120.0,
                fit: BoxFit.cover,
                clipOval: true,
                emptyBuilder: (context) => Icon(
                  Icons.account_circle,
                  size: 120.0,
                  color: Colors.grey.shade400,
                ),
              ),
              Text(
                userInfo?.name ?? '',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                width: 240.0,
                height: 280.0,
                margin: const EdgeInsets.symmetric(vertical: 16.0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Builder(
                  builder: (context) {
                    if (codeResult.valueOrNull case final data?) {
                      final difference = data.expireTime.difference(DateTime.now());
                      return QrExpireBorder(
                        expireTime: data.expireTime,
                        borderWidth: 6.0,
                        borderColor: context.themeColor,
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.0),
                            color: Colors.white,
                          ),
                          child: Column(
                            children: [
                              AspectRatio(
                                aspectRatio: 1.0,
                                child: Stack(
                                  fit: StackFit.expand,
                                  children: [
                                    Tapper(
                                      onLongPress: () {
                                        copyAndToast(data.url);
                                      },
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(6.0),
                                        child: QrImageView(
                                          backgroundColor: Colors.white,
                                          data: data.url,
                                          version: QrVersions.auto,
                                          size: 200.0 - 12.0, // 留出边框宽度
                                        ),
                                      ),
                                    ),
                                    if (codeResult.isLoading) const AppLoading(size: 72.0),
                                  ],
                                ),
                              ),
                              Expanded(
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: PlaceholderText(
                                    'QR code refreshes in **'
                                    '${(difference.inSeconds + 1).max(0)}s'
                                    '**',
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontSize: 12.0,
                                    ),
                                    matchedStyle: const TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              const Gap.v(6.0),
                            ],
                          ),
                        ),
                      );
                    }
                    if (codeResult.hasError && !codeResult.isLoading) {
                      return IconButton(
                        onPressed: () => ref.refresh(_codeProvider),
                        icon: Icon(
                          Icons.refresh_rounded,
                          color: context.meTheme.failingColor,
                          size: 72.0,
                        ),
                      );
                    }
                    return const AppLoading();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

abstract class ShareDialog {
  static Future<void> show(BuildContext context) async {
    WoltModalSheet.show(
      context: context,
      showDragHandle: false,
      modalTypeBuilder: (_) => WoltModalType.bottomSheet(),
      modalDecorator: (child) => Theme(
        data: context.theme.apply(
          (it) => it.copyWith(
            extensions: [
              ...it.extensions.values,
              WoltModalSheetThemeData(
                backgroundColor: it.bottomSheetTheme.backgroundColor,
                surfaceTintColor: it.bottomSheetTheme.backgroundColor,
                topBarElevation: 0.0,
                modalElevation: 0.0,
              ),
            ],
          ),
        ),
        child: child,
      ),
      pageListBuilder: (context) => [
        _buildDialogSheet(context),
        _buildQRCodeSheet(context),
      ],
    );
  }

  static SliverWoltModalSheetPage _buildDialogSheet(BuildContext context) {
    final theme = context.theme;
    final meTheme = theme.extension<METheme>()!;
    return WoltModalSheetPage(
      useSafeArea: false,
      topBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Row(
          children: [
            BackButton(
              color: meTheme.primaryTextColor,
            ),
            Expanded(
              child: Text('Share your page', style: theme.textTheme.headlineSmall),
            ),
          ],
        ),
      ),
      hasTopBarLayer: true,
      isTopBarLayerAlwaysVisible: true,
      child: const _ShareDialogBody(),
    );
  }

  static SliverWoltModalSheetPage _buildQRCodeSheet(BuildContext context) {
    return WoltModalSheetPage(
      useSafeArea: false,
      topBar: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        alignment: AlignmentDirectional.centerStart,
        child: BackButton(
          onPressed: () => WoltModalSheet.of(context).showPrevious(),
          color: context.meTheme.primaryTextColor,
        ),
      ),
      hasTopBarLayer: true,
      isTopBarLayerAlwaysVisible: true,
      child: const _QRCodeBody(),
    );
  }
}

class _ShareDialogBody extends ConsumerStatefulWidget {
  const _ShareDialogBody();

  @override
  ConsumerState<_ShareDialogBody> createState() => _ShareDialogBodyState();
}

class _ShareDialogBodyState extends ConsumerState<_ShareDialogBody> {
  final _containerKey = GlobalKey<OpenContainerState>();

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final meTheme = theme.extension<METheme>()!;
    final user = ref.watch(userRepoProvider);
    final staticLink = user?.staticLink;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        spacing: 10.0,
        mainAxisSize: MainAxisSize.min,
        children: [
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Container(
              width: 159.0,
              height: 242.0,
              foregroundDecoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [theme.cardColor, theme.cardColor.withAlpha(0)],
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  stops: [0, 0.2],
                ),
              ),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Assets.icons.bgShareCard.svg(fit: BoxFit.fill),
                  Container(
                    alignment: Alignment.topCenter,
                    padding: const EdgeInsets.all(7.0),
                    child: Column(
                      spacing: 10.0,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        OpenContainer(
                          key: _containerKey,
                          openColor: Colors.transparent,
                          closedColor: Colors.transparent,
                          middleColor: Colors.transparent,
                          openElevation: 0.0,
                          closedElevation: 0.0,
                          openShape: const RoundedRectangleBorder(),
                          closedShape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(Radius.circular(19.0)),
                          ),
                          openBuilder: (context, _) => SocialProfilePage(
                            code: user!.referralCode,
                            profile: user,
                          ),
                          closedBuilder: (context, _) => UserAvatar(
                            user: user,
                            borderRadius: const BorderRadius.all(Radius.circular(19.0)),
                          ),
                        ),
                        Text(
                          user?.name.or('Name') ?? 'Name',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: meTheme.listColor,
            ),
            child: Row(
              spacing: 10.0,
              children: [
                const Gap.h(2.0),
                Expanded(
                  child: Text(
                    staticLink ?? '---',
                    maxLines: 1,
                    overflow: TextOverflow.fade,
                    softWrap: false,
                    style: const TextStyle(fontSize: 18.0),
                  ),
                ),
                CopyButton(
                  onCopy: () => staticLink,
                  dimension: 40.0,
                  padding: const EdgeInsets.all(8.0),
                  backgroundColor: theme.scaffoldBackgroundColor,
                ),
              ],
            ),
          ),
          _buildButton(
            context,
            const Icon(Icons.qr_code_2_rounded),
            'QR Code',
            () => WoltModalSheet.of(context).showNext(),
          ),
          _buildButton(
            context,
            Assets.icons.buttonShare.svg(
              width: 24.0,
              height: 24.0,
              colorFilter: theme.iconTheme.color?.filter,
            ),
            'Share to',
            () => ShareBuilder.shareUri(Uri.parse(staticLink!)),
          ),
          _buildButton(
            context,
            const Icon(Icons.visibility_rounded),
            'Open',
            () => _containerKey.currentState?.openContainer(),
          ),
          Gap.v(context.bottomPadding.max(12.0)),
        ],
      ),
    );
  }

  Widget _buildButton(
    BuildContext context,
    Widget icon,
    String text,
    VoidCallback onPressed,
  ) {
    final theme = context.theme;
    return ThemeTextButton.textOnly(
      onPressed: onPressed,
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Row(
        spacing: 20.0,
        children: [
          IconTheme(
            data: theme.iconTheme.copyWith(size: 24.0),
            child: icon,
          ),
          Expanded(
            child: Text(text, style: TextStyle(color: theme.textTheme.bodyMedium?.color)),
          ),
        ],
      ),
    );
  }
}

enum _QRType {
  static,
  dynamic;

  String get title => switch (this) {
    _QRType.static => 'Share Online',
    _QRType.dynamic => 'Face to Face',
  };
}

final _qrTypeProvider = StateProvider.autoDispose<_QRType>(
  (ref) => _QRType.static,
);

class _QRCodeBody extends ConsumerStatefulWidget {
  const _QRCodeBody();

  @override
  ConsumerState<_QRCodeBody> createState() => _QRCodeBodyState();
}

class _QRCodeBodyState extends ConsumerState<_QRCodeBody> {
  Timer? _refreshTimer;
  late Timer _elapsedTimer;

  @override
  void initState() {
    super.initState();
    _initialize(refresh: false);
    _elapsedTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      final result = ref.read(_codeProvider).valueOrNull;
      if (result == null) {
        return;
      }

      safeSetState(() {});
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _elapsedTimer.cancel();
    super.dispose();
  }

  Future<void> _initialize({required bool refresh}) async {
    _refreshTimer?.cancel();

    final QRCodeDynamicResult result;
    if (refresh) {
      result = await ref.refresh(_codeProvider.future);
    } else {
      result = await ref.read(_codeProvider.future);
    }

    if (!mounted) {
      return;
    }

    final elapsed = result.expireTime.difference(DateTime.now()) - 100.milliseconds;
    if (elapsed <= Duration.zero) {
      _initialize(refresh: true);
      return;
    }

    _refreshTimer = Timer(elapsed, () => _initialize(refresh: true));
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    final selectedType = ref.watch(_qrTypeProvider);
    final user = ref.watch(userRepoProvider);
    final staticLink = user?.staticLink;
    final dynamicCodeResult = ref.watch(_codeProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        spacing: 20.0,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: double.infinity,
            child: MESlidingSegmentedControl(
              padding: const EdgeInsets.all(6.0),
              backgroundColor: theme.scaffoldBackgroundColor,
              thumbColor: theme.cardColor,
              thumbRadius: const Radius.circular(16.0),
              cornerRadius: const Radius.circular(20.0),
              groupValue: selectedType,
              onValueChanged: (value) {
                if (value != null) {
                  ref.read(_qrTypeProvider.notifier).state = value;
                }
              },
              children: {
                for (final type in _QRType.values)
                  type: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10.0),
                    child: Text(
                      type.title,
                      style: TextStyle(
                        color: selectedType == type ? null : theme.textTheme.bodySmall?.color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              },
            ),
          ),
          Text(
            switch (selectedType) {
              _QRType.static =>
                'This QR code is all yours —— '
                    'share it and let your friends check out your page!',
              _QRType.dynamic =>
                'This QR code is mainly for events. '
                    'It helps you connect and collect points at events.',
            },
            textAlign: TextAlign.center,
          ),
          Container(
            width: 240.0,
            margin: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              spacing: 10.0,
              children: [
                Row(
                  spacing: 10.0,
                  children: [
                    UserAvatar(
                      user: user,
                      dimension: 56.0,
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    Expanded(
                      child: Column(
                        spacing: 1.0,
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            user?.name.or('Name') ?? 'Name',
                            style: TextStyle(
                              fontSize: user?.title.isNotEmpty == true || user?.company.isNotEmpty == true
                                  ? 16.0
                                  : 20.0,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (user?.title case final title? when title.isNotEmpty)
                            Text(
                              title,
                              style: const TextStyle(color: Colors.grey, fontSize: 10.0),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          if (user?.company case final company? when company.isNotEmpty)
                            Text(
                              company,
                              style: const TextStyle(color: Colors.grey, fontSize: 10.0),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 300.0,
                  child: switch (selectedType) {
                    _QRType.static => _QRCodeStatic(staticLink),
                    _QRType.dynamic => _QRCodeDynamic(dynamicCodeResult),
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _QRCodeStatic extends StatelessWidget {
  const _QRCodeStatic(this.link);

  final String? link;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AspectRatio(
          aspectRatio: 1.0,
          child: Tapper(
            onLongPress: () {
              copyAndToast(link);
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(27.0),
              child: QrImageView(
                data: link ?? '',
                version: QrVersions.auto,
                size: 200.0,
                padding: const EdgeInsets.all(16.0),
                backgroundColor: Colors.white,
              ),
            ),
          ),
        ),
        Expanded(
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              link ?? '---',
              style: const TextStyle(fontSize: 12.0),
            ),
          ),
        ),
        const Gap.v(6.0),
      ],
    );
  }
}

class _QRCodeDynamic extends ConsumerWidget {
  const _QRCodeDynamic(this.result);

  final AsyncValue<QRCodeDynamicResult> result;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (result.valueOrNull case final data?) {
      final difference = data.expireTime.difference(DateTime.now());
      return Column(
        children: [
          AspectRatio(
            aspectRatio: 1.0,
            child: Stack(
              fit: StackFit.expand,
              children: [
                Tapper(
                  onLongPress: () {
                    copyAndToast(data.url);
                  },
                  child: QrExpireBorder(
                    expireTime: data.expireTime,
                    borderColor: context.themeColor,
                    borderWidth: 6.0,
                    borderRadius: 30.0,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(27.0),
                      child: QrImageView(
                        backgroundColor: Colors.white,
                        data: data.url,
                        version: QrVersions.auto,
                        size: 200.0 - 12.0, // 留出边框宽度
                      ),
                    ),
                  ),
                ),
                if (result.isLoading)
                  const AppLoading(size: 72.0)
                else if (result.hasError)
                  IconButton(
                    onPressed: () => ref.refresh(_codeProvider),
                    icon: Icon(
                      Icons.refresh_rounded,
                      color: context.meTheme.failingColor,
                      size: 72.0,
                    ),
                  ),
              ],
            ),
          ),
          Expanded(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: PlaceholderText(
                'QR code refreshes in **'
                '${(difference.inSeconds + 1).max(0)}s'
                '**',
                style: const TextStyle(fontSize: 12.0),
                matchedStyle: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const Gap.v(6.0),
        ],
      );
    }
    if (result.hasError && !result.isLoading) {
      return IconButton(
        onPressed: () => ref.refresh(_codeProvider),
        icon: Icon(
          Icons.refresh_rounded,
          color: context.meTheme.failingColor,
          size: 72.0,
        ),
      );
    }
    return const AppLoading();
  }
}
