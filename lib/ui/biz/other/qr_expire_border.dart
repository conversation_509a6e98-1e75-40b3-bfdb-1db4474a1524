import 'package:flutter/material.dart';

class QrExpireBorder extends StatefulWidget {
  const QrExpireBorder({
    super.key,
    required this.expireTime,
    required this.borderColor,
    this.borderWidth = 6.0,
    this.borderRadius = 8.0,
    this.borderBackgroundColor,
    required this.child,
  });

  final DateTime expireTime;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final Color? borderBackgroundColor;
  final Widget child;

  @override
  State<QrExpireBorder> createState() => _QrExpireBorderState();
}

class _QrExpireBorderState extends State<QrExpireBorder> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Duration _duration;

  @override
  void initState() {
    super.initState();
    final now = DateTime.now();
    final expire = widget.expireTime;
    _duration = expire.isAfter(now) ? expire.difference(now) : Duration.zero;
    _controller = AnimationController(
      vsync: this,
      duration: _duration,
    )..forward();
  }

  @override
  void didUpdateWidget(covariant QrExpireBorder oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.expireTime != widget.expireTime) {
      final now = DateTime.now();
      final expire = widget.expireTime;
      _duration = expire.isAfter(now) ? expire.difference(now) : Duration.zero;
      _controller.duration = _duration;
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) => CustomPaint(
        foregroundPainter: _QrBorderPainter(
          progress: 1.0 - _controller.value,
          borderWidth: widget.borderWidth,
          borderRadius: widget.borderRadius,
          color: widget.borderColor,
          backgroundColor: widget.borderBackgroundColor,
        ),
        child: child,
      ),
      child: Padding(
        padding: EdgeInsets.all(widget.borderWidth),
        child: widget.child,
      ),
    );
  }
}

class _QrBorderPainter extends CustomPainter {
  const _QrBorderPainter({
    required this.progress,
    required this.borderWidth,
    required this.borderRadius,
    required this.color,
    required this.backgroundColor,
  });

  final double progress; // 1.0 -> 0.0
  final double borderWidth;
  final double borderRadius;
  final Color color;
  final Color? backgroundColor;

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(borderWidth / 2, borderWidth / 2, size.width - borderWidth, size.height - borderWidth);
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));

    if (backgroundColor case final color? when color.a > 0.0) {
      final backgroundPaint = Paint()
        ..color = color
        ..strokeWidth = borderWidth
        ..strokeCap = StrokeCap.round
        ..style = PaintingStyle.stroke;
      canvas.drawPath(Path()..addRRect(rrect), backgroundPaint);
    }

    if (progress == 0) {
      return;
    }

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth
      ..strokeCap = StrokeCap.round;
    final totalLength = (size.width + size.height) * 2 - 8 * borderRadius + 2 * 3.1415926 * borderRadius;
    final drawLength = totalLength * progress;
    final path = _createProgressRRectPath(rrect, drawLength);
    canvas.drawPath(path, paint);
  }

  Path _createProgressRRectPath(RRect rrect, double length) {
    final path = Path();
    final metrics = Path()..addRRect(rrect);
    final pm = metrics.computeMetrics().first;
    path.addPath(pm.extractPath(0, length), Offset.zero);
    return path;
  }

  @override
  bool shouldRepaint(covariant _QrBorderPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.borderWidth != borderWidth ||
        oldDelegate.borderRadius != borderRadius;
  }
}
