import 'dart:convert' show base64Encode;
import 'dart:io' as io show File;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_size_getter/image_size_getter.dart';

import '/models/business.dart' show ImagePickResult, ImagePickFileResult, ImagePickUrlResult;
import '/models/card.dart';
@FFAutoImport()
import '/models/user.dart' show UserInfo;
import '/provider/api.dart';
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;
import '/ui/widgets/social/profile/avatar_img.dart';

import '_shared.dart' show calculateFontSize;
import 'metal.dart';
import 'normal.dart';

enum CustomizeViewType { start, main, preview }

/// 卡片定制页面
@FFRoute(name: '/customize')
class CustomizePage extends ConsumerStatefulWidget {
  const CustomizePage({
    super.key,
    this.code,
  });

  final String? code;

  @override
  ConsumerState<CustomizePage> createState() => _CustomizePageState();
}

class _CustomizePageState extends ConsumerState<CustomizePage> {
  // 表单控制器
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _titleController = TextEditingController();
  final _companyController = TextEditingController();

  // 状态变量
  CustomizeViewType _viewType = CustomizeViewType.main;
  bool _isLoading = false;
  bool _pageLoading = true;
  bool _isMetalType = false;

  ImagePickResult? _avatarPickResult;

  CoverInfo? _coverInfo;

  // AvatarImgPicker组件的Key (仅用于normal类型)
  final GlobalKey<AvatarImgPickerState> _avatarPickerKey = GlobalKey<AvatarImgPickerState>();

  @override
  void initState() {
    super.initState();
    _viewType = CustomizeViewType.main;
    _initializeData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _titleController.dispose();
    _companyController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    setState(() {
      _pageLoading = true;
    });

    try {
      // 并行初始化用户数据和封面信息
      await Future.wait([
        _initializeUserData(),
        _initializeCoverInfo(),
      ]);
    } finally {
      safeSetState(() {
        _pageLoading = false;
      });
    }
  }

  Future<void> _initializeUserData() async {
    UserInfo? userInfo;
    try {
      userInfo = await ref.read(fetchUserInfoProvider().future);
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      userInfo ??= ref.read(userRepoProvider);
    }

    if (userInfo == null) {
      if (mounted) {
        Navigator.of(context).pop();
      }
      return;
    }

    _nameController.text = userInfo.name;
    _titleController.text = userInfo.title;
    _companyController.text = userInfo.company;
    // 只有非Metal类型才需要头像
    if (userInfo.avatar.isNotEmpty && !_isMetalType) {
      _avatarPickResult = ImagePickResult.url(userInfo.avatar);
    }
  }

  Future<void> _initializeCoverInfo() async {
    final code = widget.code;
    if (code == null || code.isEmpty) {
      return;
    }
    try {
      final coverInfo = await ref.read(apiServiceProvider).getCoverInfo(code: code);
      if (coverInfo.price != 0) {
        safeSetState(() {
          _viewType = CustomizeViewType.start;
        });
      }
      safeSetState(() {
        _coverInfo = coverInfo;
        _isMetalType = coverInfo.printType == PrintType.METAL;
      });
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToGetCoverInfo(code: code));
      if (mounted) {
        Navigator.of(context).pop();
      }
      rethrow;
    }
  }

  void _handleImageSelected(ImagePickResult result) {
    setState(() {
      _avatarPickResult = result;
    });
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }
    if (value.length > 30) {
      return 'Name length should be less than 30';
    }
    return null;
  }

  String? _validateTitle(String? value) {
    if (value != null && value.length > 30) {
      return 'Title length should be less than 30';
    }
    return null;
  }

  String? _validateCompany(String? value) {
    if (value != null && value.length > 30) {
      return 'Company length should be less than 30';
    }
    return null;
  }

  Future<void> _handleNext() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 只有非Metal类型才需要检查图片
    if (!_isMetalType && _avatarPickResult == null) {
      Card3ToastUtil.showToast(message: ToastMessages.pleaseUploadYourPicture);
      return;
    }

    setState(() {
      _viewType = CustomizeViewType.preview;
    });
  }

  // 添加方法来处理图片转base64
  Future<String?> _processImageToBase64() async {
    String? mimeType;
    final Uint8List imageBytes;
    switch (_avatarPickResult) {
      case null:
        return null;
      case ImagePickFileResult(:final path):
        imageBytes = await io.File(path).readAsBytes();
      case ImagePickUrlResult(:final url):
        final response = await ref.read(httpProvider).get(url, options: Options(responseType: ResponseType.bytes));
        imageBytes = response.data;
        final contentType = response.headers.value(Headers.contentTypeHeader);
        if (contentType != null) {
          mimeType = DioMediaType.parse(contentType).mimeType;
        }
    }

    if (mimeType == null) {
      final imageResult = ImageSizeGetter.getSizeResult(MemoryInput(imageBytes));
      mimeType = 'image/${imageResult.decoder.decoderName}';
    }

    final base64String = base64Encode(imageBytes);
    return 'data:$mimeType;base64,$base64String';
  }

  Future<void> _handleConfirm() async {
    setState(() {
      _isLoading = true;
    });

    try {
      String? fileContent;

      // 如果不是Metal类型，需要处理图片
      if (!_isMetalType) {
        fileContent = await _processImageToBase64();
        if (fileContent == null) {
          Card3ToastUtil.showToast(message: ToastMessages.failedToProcessImage);
          return;
        }
      }

      CustomizeCardOrder res = await ref
          .read(apiServiceProvider)
          .createCardCover(
            code: widget.code ?? '',
            fileContent: fileContent ?? '',
            username: _nameController.text.trim(),
            title: _titleController.text.trim(),
            company: _companyController.text.trim(),
          );
      if (!mounted) {
        return;
      }

      if (res.paymentLink.isNotEmpty) {
        final paid = await meNavigator.pushNamed(
          Routes.customizePayment.name,
          arguments: Routes.customizePayment.d(order: res),
        );
        if (paid is CustomizeCardOrder) {
          res = paid;
        }
      }
      meNavigator.pushReplacementNamed(
        Routes.customizeDone.name,
        arguments: Routes.customizeDone.d(order: res),
      );
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToCreateCardCover);
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  void _handleBack() {
    if (_viewType == CustomizeViewType.main) {
      meNavigator.pop();
    } else {
      setState(() {
        _viewType = CustomizeViewType.main;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_pageLoading) {
      return const AppScaffold(
        body: AppLoading(),
      );
    }

    switch (_viewType) {
      case CustomizeViewType.start:
        return _buildStartView();
      case CustomizeViewType.main:
        return _buildMainViewWrapper();
      case CustomizeViewType.preview:
        return _buildConfirmViewWrapper();
    }
  }

  Widget _buildMainViewWrapper() {
    if (_isMetalType) {
      return MetalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: calculateFontSize,
        isConfirmView: false,
      );
    } else {
      return NormalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        avatarPickResult: _avatarPickResult,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        avatarPickerKey: _avatarPickerKey,
        onImageSelected: _handleImageSelected,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: calculateFontSize,
        isConfirmView: false,
      );
    }
  }

  Widget _buildConfirmViewWrapper() {
    if (_isMetalType) {
      return MetalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: calculateFontSize,
        isConfirmView: true,
      );
    } else {
      return NormalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        avatarPickResult: _avatarPickResult,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        avatarPickerKey: _avatarPickerKey,
        onImageSelected: _handleImageSelected,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: calculateFontSize,
        isConfirmView: true,
      );
    }
  }

  Widget _buildStartView() {
    return Stack(
      children: [
        // 背景图片
        Positioned.fill(
          top: -300,
          child: Transform.rotate(
            angle: 0.2,
            child: Assets.icons.images.banner2.image(
              fit: BoxFit.contain,
            ),
          ),
        ),
        // 深色遮罩
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.4),
                  Colors.black.withValues(alpha: 0.7),
                ],
              ),
            ),
          ),
        ),
        AppScaffold(
          backgroundColor: Colors.transparent,
          body: SizedBox.expand(
            child: Column(
              spacing: 16.0,
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(text: 'Customize\nyour '),
                      TextSpan(
                        text: 'Card3',
                        style: TextStyle(color: context.themeColor),
                      ),
                    ],
                  ),
                  style: const TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    height: 1.2,
                  ),
                ),
                const Text(
                  'with NFT PFPs, portraits & any images!',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                if (_coverInfo?.priceDescription case final desc? when desc.isNotEmpty)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(top: 30.0),
                    child: Text(
                      desc,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
          bottomButtonBuilder: (context) => ThemeTextButton(
            onPressed: () {
              setState(() {
                _viewType = CustomizeViewType.main;
              });
            },
            text: 'Customize Now',
          ),
        ),
      ],
    );
  }
}
