import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/card.dart';

class MetalCustomizeView extends StatelessWidget {
  const MetalCustomizeView({
    super.key,
    required this.form<PERSON>ey,
    required this.nameController,
    required this.titleController,
    required this.companyController,
    required this.coverInfo,
    required this.isLoading,
    required this.onNext,
    required this.onConfirm,
    required this.onBack,
    required this.validateName,
    required this.validateTitle,
    required this.validateCompany,
    required this.calculateFontSize,
    this.isConfirmView = false,
  });

  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController titleController;
  final TextEditingController companyController;
  final CoverInfo? coverInfo;
  final bool isLoading;
  final Function() onNext;
  final Function() onConfirm;
  final Function() onBack;
  final String? Function(String?) validateName;
  final String? Function(String?) validateTitle;
  final String? Function(String?) validateCompany;
  final double Function(String) calculateFontSize;
  final bool isConfirmView;

  @override
  Widget build(BuildContext context) {
    if (isConfirmView) {
      return _buildConfirmView(context);
    } else {
      return _buildMainView(context);
    }
  }

  Widget _buildMainView(BuildContext context) {
    return AppScaffold(
      onBackButtonPressed: onBack,
      body: Form(
        key: formKey,
        child: ListView(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          children: [
            _buildMetalCardPreview(),
            const Gap.v(24.0),
            _buildMetalFormFields(context),
            const SizedBox(height: 50.0),
          ],
        ),
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        onPressed: onNext,
        text: context.l10nME.nextButton,
      ),
    );
  }

  Widget _buildConfirmView(BuildContext context) {
    return AppScaffold(
      onBackButtonPressed: onBack,
      body: ListView(
        children: [
          const SizedBox(height: 24),
          const Text(
            'Please check again before\nprinting:',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          // Front标签
          const Text(
            'Front',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          // 正面卡片
          _buildMetalCardPreview(),
          const SizedBox(height: 40),
          // Back标签
          const Text(
            'Back',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          // 背面卡片
          _buildMetalBackCard(),
          const Gap.v(40.0),
          AutoSizeText(
            'This is the preview of your card.',
            style: context.textTheme.headlineSmall,
            textAlign: TextAlign.center,
            maxLines: 1,
          ),
          const SizedBox(height: 60),
        ],
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        onPressed: isLoading ? null : onConfirm,
        child: isLoading ? const AppLoading() : Text(context.l10nME.submitButton),
      ),
    );
  }

  Widget _buildFittedBox({required Widget child}) {
    return Center(
      child: SizedBox(
        width: 280.0,
        height: 180.0,
        child: AspectRatio(aspectRatio: 318 / 200, child: child),
      ),
    );
  }

  Widget _buildMetalCardPreview() {
    return _buildFittedBox(
      child: Stack(
        children: [
          // 使用 coverMetalFront 作为背景
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12.0),
              child: Assets.icons.images.coverMetalFront.svg(
                fit: BoxFit.contain,
              ),
            ),
          ),
          // 文字内容覆盖在背景上
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ValueListenableBuilder(
                  valueListenable: nameController,
                  builder: (context, value, _) => Text(
                    value.text.isEmpty ? 'Your name' : value.text,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: calculateFontSize(value.text),
                    ),
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: titleController,
                  builder: (context, value, _) {
                    if (value.text.isEmpty) {
                      return const SizedBox.shrink();
                    }
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        value.text,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 14.0,
                          height: 1.0,
                        ),
                      ),
                    );
                  },
                ),
                ValueListenableBuilder(
                  valueListenable: companyController,
                  builder: (context, value, _) {
                    if (value.text.isEmpty) {
                      return const SizedBox.shrink();
                    }
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        value.text,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 14.0,
                          height: 1.0,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetalBackCard() {
    return _buildFittedBox(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.0),
        child: Assets.icons.images.coverMetalBack.svg(
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildMetalFormFields(BuildContext context) {
    return Column(
      spacing: 16.0,
      mainAxisSize: MainAxisSize.min,
      children: [
        TextFormField(
          controller: nameController,
          decoration: InputDecoration(
            filled: true,
            fillColor: context.theme.cardColor,
            hintText: 'Your name',
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateName,
        ),
        TextFormField(
          controller: titleController,
          decoration: InputDecoration(
            filled: true,
            fillColor: context.theme.cardColor,
            hintText: 'Title (Optional)',
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateTitle,
        ),
        TextFormField(
          controller: companyController,
          decoration: InputDecoration(
            filled: true,
            fillColor: context.theme.cardColor,
            hintText: 'Company (Optional)',
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateCompany,
        ),
      ],
    );
  }
}
