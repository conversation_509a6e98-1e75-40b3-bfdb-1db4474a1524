import 'dart:io' as io show File;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/business.dart' show ImagePickResult, ImagePickFileResult, ImagePickUrlResult;
import '/models/card.dart';
import '/ui/widgets/social/profile/avatar_img.dart';

class NormalCustomizeView extends StatelessWidget {
  const NormalCustomizeView({
    super.key,
    required this.formKey,
    required this.nameController,
    required this.titleController,
    required this.companyController,
    required this.avatarPickResult,
    required this.coverInfo,
    required this.isLoading,
    required this.avatarPicker<PERSON>ey,
    required this.onImageSelected,
    required this.onNext,
    required this.onConfirm,
    required this.onBack,
    required this.validateName,
    required this.validateTitle,
    required this.validateCompany,
    required this.calculateFontSize,
    this.isConfirmView = false,
  });

  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController titleController;
  final TextEditingController companyController;
  final ImagePickResult? avatarPickResult;
  final CoverInfo? coverInfo;
  final bool isLoading;
  final GlobalKey<AvatarImgPickerState> avatarPickerKey;
  final Function(ImagePickResult result) onImageSelected;
  final Function() onNext;
  final Function() onConfirm;
  final Function() onBack;
  final String? Function(String?) validateName;
  final String? Function(String?) validateTitle;
  final String? Function(String?) validateCompany;
  final double Function(String) calculateFontSize;
  final bool isConfirmView;

  @override
  Widget build(BuildContext context) {
    if (isConfirmView) {
      return _buildConfirmView(context);
    } else {
      return _buildMainView(context);
    }
  }

  Widget _buildMainView(BuildContext context) {
    return AppScaffold(
      body: Form(
        key: formKey,
        child: ListView(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          children: [
            // 卡片预览区域
            _buildCardPreview(context),
            // 图片上传区域
            _buildImageUploadSection(context),
            const SizedBox(height: 24),

            // 表单字段
            _buildFormFields(context),
            const Gap.v(50.0),
          ],
        ),
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        onPressed: onNext,
        text: context.l10nME.nextButton,
      ),
    );
  }

  Widget _buildConfirmView(BuildContext context) {
    return AppScaffold(
      onBackButtonPressed: onBack,
      body: ListView(
        children: [
          const SizedBox(height: 100),
          // Front/Back 标签
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      'Front',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      'Back',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // 卡片预览
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              spacing: 20.0,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(child: _buildFrontCard(context)),
                Flexible(child: _buildBackCard(context)),
              ],
            ),
          ),
          const Gap.v(40.0),
          AutoSizeText(
            'This is the preview of your card.',
            style: context.textTheme.headlineSmall,
            textAlign: TextAlign.center,
            maxLines: 1,
          ),
          const SizedBox(height: 60),
        ],
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        onPressed: isLoading ? null : onConfirm,
        child: isLoading ? const AppLoading() : Text(context.l10nME.submitButton),
      ),
    );
  }

  Widget _buildCardPreview(BuildContext context) {
    return Container(
      height: 280,
      color: context.theme.scaffoldBackgroundColor,
      alignment: Alignment.center,
      child: Container(
        width: 140,
        height: 220,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Stack(
          children: [
            // 使用 coverNormalBack 作为背景
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Assets.icons.images.coverNormalFront.image(
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Column(
              children: [
                // 图片区域
                GestureDetector(
                  onTap: () {
                    avatarPickerKey.currentState?.pickImage();
                  },
                  child: Container(
                    width: 140,
                    height: 140,
                    decoration: const BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                      child: switch (avatarPickResult) {
                        ImagePickFileResult(:final path) => Image.file(
                          io.File(path),
                          fit: BoxFit.cover,
                        ),
                        ImagePickUrlResult(:final url) => MEImage(
                          url,
                          fit: BoxFit.cover,
                          alternativeSVG: true,
                        ),
                        null => const Icon(
                          Icons.add,
                          size: 56,
                          color: ColorName.themeColorDark,
                        ),
                      },
                    ),
                  ),
                ),

                // 文字信息区域 - 移除渐变背景，使用透明背景让底层的 coverNormalBack 显示
                Expanded(
                  child: Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(12),
                        bottomRight: Radius.circular(12),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ValueListenableBuilder(
                            valueListenable: nameController,
                            builder: (context, value, _) => value.text.trim().run(
                              (it) => Text(
                                it.or('Your name'),
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: calculateFontSize(it),
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                          ValueListenableBuilder(
                            valueListenable: titleController,
                            builder: (context, value, _) {
                              if (value.text.isEmpty) {
                                return const SizedBox.shrink();
                              }
                              return Padding(
                                padding: const EdgeInsets.only(top: 2.0),
                                child: Text(
                                  value.text,
                                  style: const TextStyle(
                                    color: Colors.black45,
                                    fontSize: 10,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              );
                            },
                          ),
                          ValueListenableBuilder(
                            valueListenable: companyController,
                            builder: (context, value, _) {
                              if (value.text.isEmpty) {
                                return const SizedBox.shrink();
                              }
                              return Padding(
                                padding: const EdgeInsets.only(top: 2.0),
                                child: Text(
                                  value.text,
                                  style: const TextStyle(
                                    color: Colors.black45,
                                    fontSize: 10,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageUploadSection(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 100,
      child: CustomPaint(
        painter: const DottedBorderPainter(length: 8.0, spacing: 6.0, borderRadius: 16.0),
        child: switch (avatarPickResult) {
          _? => Center(
            child: AvatarImgPicker(
              key: avatarPickerKey,
              avatar: avatarPickResult,
              onImageSelected: onImageSelected,
              size: 80,
            ),
          ),
          _ => Tapper(
            onTap: () {
              avatarPickerKey.currentState?.pickImage();
            },
            child: Container(
              width: double.infinity,
              height: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                spacing: 16.0,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AvatarImgPicker(
                    key: avatarPickerKey,
                    avatar: avatarPickResult,
                    onImageSelected: onImageSelected,
                    size: 56,
                    backgroundColor: Colors.transparent,
                    showEditButton: false,
                    emptyBuilder: (context) => Icon(Icons.add, size: 56, color: context.themeColor),
                  ),
                  const Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Your Picture',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'NFT, Selfie, Logo...',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        },
      ),
    );
  }

  Widget _buildFormFields(BuildContext context) {
    return Column(
      spacing: 16.0,
      children: [
        // 姓名输入框
        TextFormField(
          controller: nameController,
          decoration: InputDecoration(
            filled: true,
            fillColor: context.theme.cardColor,
            hintText: 'Your name',
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateName,
        ),
        TextFormField(
          controller: titleController,
          decoration: InputDecoration(
            filled: true,
            fillColor: context.theme.cardColor,
            hintText: 'Title (Optional)',
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateTitle,
        ),
        TextFormField(
          controller: companyController,
          decoration: InputDecoration(
            filled: true,
            fillColor: context.theme.cardColor,
            hintText: 'Company (Optional)',
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateCompany,
        ),
      ],
    );
  }

  // 构建正面卡片
  Widget _buildFrontCard(BuildContext context) {
    return Container(
      width: 140,
      height: 220,
      decoration: BoxDecoration(
        border: Border.all(color: context.theme.dividerColor, width: 0.5),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 使用 coverNormalBack 作为背景
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Assets.icons.images.coverNormalFront.image(
                fit: BoxFit.cover,
              ),
            ),
          ),
          Column(
            children: [
              // 图片区域
              Container(
                width: 140,
                height: 140,
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  child: switch (avatarPickResult) {
                    ImagePickFileResult(:final path) => Image.file(
                      io.File(path),
                      fit: BoxFit.cover,
                    ),
                    ImagePickUrlResult(:final url) => MEImage(
                      url,
                      fit: BoxFit.cover,
                      clipOval: false,
                      alternativeSVG: true,
                    ),
                    null => Container(
                      color: Colors.grey[100],
                      child: const Icon(
                        Icons.person,
                        size: 60,
                        color: Colors.grey,
                      ),
                    ),
                  },
                ),
              ),
              // 文字信息区域 - 移除渐变背景
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          nameController.text.isEmpty ? 'Your name' : nameController.text,
                          style: TextStyle(
                            fontSize: calculateFontSize(nameController.text),
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (titleController.text.isNotEmpty) ...[
                          const SizedBox(height: 2),
                          Text(
                            titleController.text,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black45,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                        if (companyController.text.isNotEmpty) ...[
                          const SizedBox(height: 2),
                          Text(
                            companyController.text,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black45,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建背面卡片
  Widget _buildBackCard(BuildContext context) {
    return Container(
      width: 140,
      height: 220,
      decoration: BoxDecoration(
        border: Border.all(color: context.theme.dividerColor, width: 0.5),
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: MEImage(
        coverInfo?.backCover ?? '',
        width: 140,
        height: 220,
        fit: BoxFit.fitWidth,
        borderRadius: BorderRadius.circular(16),
        emptyBuilder: (context) => Assets.icons.images.coverNormalBack.image(
          width: 140,
          height: 220,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
