import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/card.dart' show CustomizeCardOrder;
import '/provider/card.dart' show fetchCustomizeCardOrdersProvider;

@FFRoute(name: '/customize/done')
class CustomizeDonePage extends StatelessWidget {
  const CustomizeDonePage({super.key, required this.order});

  final CustomizeCardOrder order;

  @override
  Widget build(BuildContext context) {
    final paid = order.payStatus.paid;
    return AppScaffold(
      automaticallyImplyLeading: false,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (paid) Assets.icons.check.svg(width: 100.0) else MEUIAssets.icons.warningYellow.svg(width: 100.0),
            const SizedBox(height: 32),
            Text(
              paid ? 'Submitted' : 'Pending payment',
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 48),
            if (paid) ...[
              const Text(
                'Your printing code:',
                style: TextStyle(fontSize: 24),
              ),
              const SizedBox(height: 16),
              Text(
                order.code,
                style: TextStyle(
                  color: context.themeColor,
                  fontSize: 50,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ] else
              const FittedBox(
                fit: BoxFit.scaleDown,
                child: PlaceholderText(
                  'Please complete your payment in \n'
                  'Settings > **Card Printing Orders**\n'
                  'to get your printing code.',
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
      bottomButtonBuilder: (context) => Column(
        spacing: 16.0,
        children: [
          if (paid)
            FittedBox(
              fit: BoxFit.cover,
              child: Text(
                'You may take a screenshot before pressing the button.',
                style: context.textTheme.bodySmall?.copyWith(fontSize: 16.0),
              ),
            ),
          ThemeTextButton(
            onPressed: () {
              globalContainer.invalidate(fetchCustomizeCardOrdersProvider);
              Navigator.of(context).maybePop();
            },
            text: context.l10nME.doneButton,
          ),
        ],
      ),
    );
  }
}
