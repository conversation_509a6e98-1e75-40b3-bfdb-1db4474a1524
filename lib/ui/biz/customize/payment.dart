import 'dart:async' show Completer;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/card.dart' show CustomizeCardOrder, PrintType;
import '/provider/api.dart' show apiServiceProvider;
import '_shared.dart' show calculateFontSize;

@FFRoute(name: '/customize/payment')
class CustomizePaymentPage extends StatelessWidget {
  const CustomizePaymentPage({
    super.key,
    required this.order,
  });

  final CustomizeCardOrder order;

  // 显示支付完成底部弹窗
  Future<void> _showPaymentCompleteBottomSheet(BuildContext context) async {
    if (order.paymentLink.isEmpty) {
      return;
    }

    final result = await showModalBottomSheet(
      context: context,
      scrollControlDisabledMaxHeightRatio: 0.4,
      builder: (context) => _CompletePaymentSheet(order),
    );
    if (result is CustomizeCardOrder) {
      Navigator.of(context).maybePop(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    final subTotal = order.payInfo.priceUnit;
    final shipping = order.payInfo.shippingUnit;

    // 计算总价
    final subtotalAmount = Decimal.tryParse(subTotal?.amount ?? '0') ?? Decimal.zero;
    final shippingAmount = Decimal.tryParse(shipping?.amount ?? '0') ?? Decimal.zero;
    final totalAmount = subtotalAmount + shippingAmount;

    return AppScaffold(
      body: ListView(
        children: [
          // 卡片预览区域
          SizedBox(
            height: 220,
            child: Center(
              child: order.printType == PrintType.METAL ? _buildMetalCardPreview() : _buildNormalCardPreview(),
            ),
          ),
          const Gap.v(16.0),
          // 账单信息
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: context.theme.scaffoldBackgroundColor.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
              color: context.theme.cardColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Bill',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),

                // Subtotal
                _buildBillRow(
                  'Subtotal',
                  [
                    subTotal?.symbol,
                    subTotal?.amount,
                    subTotal?.currency,
                  ].join(' '),
                ),
                const SizedBox(height: 16),

                // Shipping Fee
                _buildBillRow(
                  'Shipping Fee',
                  [
                    shipping?.symbol,
                    shipping?.amount,
                    shipping?.currency,
                  ].join(' '),
                ),
                const SizedBox(height: 24),

                // 分割线
                const Divider(height: 1.0),
                const SizedBox(height: 24),

                // Total
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Total',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${subTotal?.symbol} $totalAmount ${subTotal?.currency}',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        onPressed: () => _showPaymentCompleteBottomSheet(context),
        text: 'Proceed to checkout',
      ),
    );
  }

  Widget _buildMetalCardPreview() {
    return Center(
      child: SizedBox(
        width: 280.0,
        height: 180.0,
        child: AspectRatio(aspectRatio: 318 / 200, child: _buildMetalCardPreviewContent()),
      ),
    );
  }

  Widget _buildMetalCardPreviewContent() {
    return Container(
      width: 280,
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          // 使用 coverMetalFront 作为背景
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12.0),
              child: Assets.icons.images.coverMetalFront.svg(
                fit: BoxFit.contain,
              ),
            ),
          ),
          // 文字内容覆盖在背景上
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order.name,
                  style: TextStyle(
                    fontSize: calculateFontSize(order.name) * 1.3,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                if (order.title.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      order.title,
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                  ),
                if (order.company.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      order.company,
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNormalCardPreview() {
    return Container(
      width: 140,
      height: 220,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
        color: Colors.white,
      ),
      child: Stack(
        children: [
          // 使用 coverNormalFront 作为背景
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Assets.icons.images.coverNormalFront.image(
                fit: BoxFit.cover,
              ),
            ),
          ),
          // 头像区域
          Column(
            children: [
              Container(
                width: 140,
                height: 140,
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: ClipRRect(
                  child: MEImage(
                    order.image,
                    fit: BoxFit.cover,
                    alternativeSVG: true,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                ),
              ),
              // 文字内容
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          order.name,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: calculateFontSize(order.name),
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (order.title.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 2.0),
                            child: Text(
                              order.title,
                              style: const TextStyle(
                                color: Colors.black45,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        if (order.company.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 2.0),
                            child: Text(
                              order.company,
                              style: const TextStyle(
                                color: Colors.black45,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBillRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 18,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

class _CompletePaymentSheet extends ConsumerStatefulWidget {
  const _CompletePaymentSheet(this.order);

  final CustomizeCardOrder order;

  @override
  ConsumerState createState() => _CompletePaymentSheetState();
}

class _CompletePaymentSheetState extends ConsumerState<_CompletePaymentSheet> with WidgetsBindingObserver {
  CustomizeCardOrder get order => widget.order;

  final _cancelToken = CancelToken();
  Completer<void>? _lock;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _launchLink(ask: true);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      _checkOrder();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cancelToken.cancel();
    super.dispose();
  }

  Future<bool> _launchLink({required bool ask}) {
    return launchUrlString(
      order.paymentLink,
      askToOpenText: ask ? 'You are about to open the link to proceed the payment.' : null,
      askToOpenCaptionText: ask ? order.paymentLink : null,
    );
  }

  Future<void> _checkOrder({bool showToastUnpaid = false}) {
    if (_lock case final lock?) {
      return lock.future;
    }

    final lock = _lock = Completer<void>();
    safeSetState(() {});
    Future(() async {
      final latest = await ref.read(apiServiceProvider).getCustomizeCardOrderByCode(code: order.code);
      if (!latest.payStatus.paid) {
        if (showToastUnpaid) {
          Card3ToastUtil.showToast(
            message:
                'Your order is still not paid. '
                'Try again later if you have already made the payment.',
            duration: const Duration(seconds: 3),
          );
        }
        return;
      }
      Card3ToastUtil.showToast(message: 'Your card order has been paid!');
      if (mounted) {
        Navigator.of(context).maybePop(latest);
      }
    }).then(lock.complete).catchError(lock.completeError).whenComplete(() {
      _lock = null;
      safeSetState(() {});
    });
    return lock.future;
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return Padding(
      padding: const EdgeInsets.all(24.0).copyWith(
        bottom: context.bottomPadding.max(24.0),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Complete Payment',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              CloseButton(onPressed: () => Navigator.of(context).maybePop()),
            ],
          ),
          Expanded(
            child: Column(
              children: [
                const Gap.v(16.0),
                Row(
                  spacing: 12.0,
                  children: [
                    Expanded(
                      child: RippleTap(
                        onTap: () => _launchLink(ask: false),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.0),
                          side: BorderSide(color: theme.dividerColor),
                        ),
                        color: theme.colorScheme.surface,
                        child: Text(
                          order.paymentLink,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    CopyButton(
                      onCopy: () => order.paymentLink,
                      dimension: 32.0,
                      backgroundColor: theme.primaryColor,
                      color: theme.textTheme.bodyMedium?.color,
                    ),
                  ],
                ),
                const Gap.v(16.0),
                const Text(
                  'Click the link to open, or copy and open this link '
                  'in your browser to complete the payment.',
                ),
              ],
            ),
          ),
          ThemeTextButton(
            onPressed: _lock == null ? () => _checkOrder(showToastUnpaid: true) : null,
            child: _lock == null ? const Text("I've completed the payment") : const AppLoading(),
          ),
        ],
      ),
    );
  }
}
