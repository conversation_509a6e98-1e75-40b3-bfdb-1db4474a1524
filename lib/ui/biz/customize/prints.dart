import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/feat/link/handler.dart' show WebsiteLinkHandler;
import '/models/card.dart' show CustomizeCardOrder, PrintType;
import '/provider/card.dart' show fetchCustomizeCardOrdersProvider;

@FFRoute(name: '/customize/prints')
class CustomizeCardPrintsPage extends ConsumerWidget {
  const CustomizeCardPrintsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final totalResult = ref.watch(fetchCustomizeCardOrdersProvider(page: 1));
    final Widget child;
    if (totalResult.valueOrNull?.total == 0) {
      child = RefreshableEmptyView(
        onTap: () => ref.invalidate(fetchCustomizeCardOrdersProvider),
        message: 'No orders yet',
      );
    } else if (totalResult.hasError && !totalResult.isLoading) {
      final e = totalResult.error;
      child = RefreshableEmptyView(
        onTap: () => ref.invalidate(fetchCustomizeCardOrdersProvider),
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      );
    } else {
      const size = 20;
      child = ListView.builder(
        padding: const EdgeInsets.only(top: 14.0, bottom: 50.0),
        itemCount: totalResult.valueOrNull?.total ?? 4,
        itemBuilder: (context, index) {
          final page = index ~/ size + 1;
          final indexInPage = index % size;
          final result = ref.watch(fetchCustomizeCardOrdersProvider(page: page));
          return result.maybeWhen(
            data: (data) {
              if (indexInPage >= data.list.length) {
                return null;
              }
              final item = data.list[indexInPage];
              return ProviderScope(
                overrides: [
                  _itemProvider.overrideWithValue(item),
                ],
                child: const _OrderItem(),
              );
            },
            orElse: () => const _OrderItemShimmer(),
          );
        },
      );
    }
    return AppScaffold(
      title: 'Card Printing Orders',
      appBarActions: [
        IconButton(
          onPressed: () {
            Navigator.of(context).pushNamed(
              Routes.scan.name,
              arguments: Routes.scan.d(
                manager: ScanManager({
                  AppLinkScanHandler(handlers: const {WebsiteLinkHandler()}),
                }),
              ),
            );
          },
          icon: Center(
            child: Container(
              width: 36.0,
              height: 36.0,
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: context.meTheme.primaryTextColor,
                shape: BoxShape.circle,
              ),
              child: Assets.icons.scan.scanner.svg(
                width: 32.0,
                height: 32.0,
                colorFilter: context.theme.cardColor.filter,
              ),
            ),
          ),
        ),
      ],
      body: RefreshIndicator(
        onRefresh: () {
          ref.invalidate(fetchCustomizeCardOrdersProvider);
          return ref.read(fetchCustomizeCardOrdersProvider(page: 1).future);
        },
        child: child,
      ),
    );
  }
}

final _itemProvider = Provider.autoDispose<CustomizeCardOrder>(
  (ref) => throw UnimplementedError(),
);

class _OrderItem extends ConsumerWidget {
  const _OrderItem();

  ({String status, Color? borderColor, Color? backgroundColor, Color? statusColor}) _getStatus(
    CustomizeCardOrder item,
  ) {
    final String status;
    final Color? borderColor;
    final Color? backgroundColor;
    final Color? statusColor;
    if (item.payStatus.paid) {
      status = item.printed ? 'Printed' : 'Pending Print';
      borderColor = item.printed ? null : Colors.red;
      backgroundColor = item.printed ? Colors.green : null;
      statusColor = item.printed ? Colors.white : Colors.red;
    } else {
      status = 'Pending Payment';
      borderColor = Colors.red;
      backgroundColor = null;
      statusColor = Colors.red;
    }
    return (status: status, borderColor: borderColor, backgroundColor: backgroundColor, statusColor: statusColor);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_itemProvider);
    return RippleTap(
      onTap: !item.payStatus.paid && item.paymentLink.isNotEmpty
          ? () async {
              final latest = await Navigator.of(context).pushNamed(
                Routes.customizePayment.name,
                arguments: Routes.customizePayment.d(order: item),
              );
              if (latest is CustomizeCardOrder && latest.payStatus.paid) {
                ref.invalidate(fetchCustomizeCardOrdersProvider);
              }
            }
          : null,
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.all(10.0),
      height: 160.0,
      color: context.theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: context.theme.dividerColor),
      ),
      child: Row(
        spacing: 10.0,
        children: [
          AspectRatio(
            aspectRatio: 205 / 150,
            child: switch (item.printType) {
              PrintType.METAL => const Stack(
                fit: StackFit.expand,
                clipBehavior: Clip.none,
                children: [
                  Positioned.fill(
                    left: 48.0,
                    top: 50.0,
                    child: _MetalCoverBack(),
                  ),
                  Positioned.fill(
                    right: 48.0,
                    bottom: 50.0,
                    child: _MetalCover(),
                  ),
                ],
              ),
              PrintType.NORMAL => const Row(
                spacing: 5.0,
                children: [
                  Expanded(child: FittedBox(child: _NormalCover())),
                  Expanded(child: FittedBox(child: _NormalCoverBack())),
                ],
              ),
            },
          ),
          const CustomPaint(
            painter: DashedVerticalLinePainter(),
            child: SizedBox(height: double.infinity),
          ),
          const Gap.h(4.0),
          Expanded(
            child: Column(
              spacing: 12.0,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    item.code,
                    style: TextStyle(
                      color: context.themeColor,
                      fontFamily: FontFamily.harmonyOSSans,
                      fontSize: 30.0,
                      fontWeight: FontWeight.w900,
                      height: 1.0,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: _getStatus(item).run(
                    (r) => Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
                      decoration: BoxDecoration(
                        border: r.borderColor?.run((it) => Border.all(color: it)),
                        borderRadius: 20.rCircular,
                        color: r.backgroundColor,
                      ),
                      child: Text(
                        r.status,
                        style: TextStyle(color: r.statusColor, fontSize: 10.0),
                      ),
                    ),
                  ),
                ),
                // if (item.payStatus != CardPayStatus.FREE)
                //   FittedBox(
                //     fit: BoxFit.scaleDown,
                //     child: Text.rich(
                //       TextSpan(
                //         children: [
                //           TextSpan(
                //             text: item.payInfo.priceUnit?.symbol ?? '-',
                //           ),
                //           TextSpan(
                //             text: Decimal.fromInt(item.payPrice).shift(-2).toNumerical(),
                //           ),
                //           TextSpan(
                //             text: item.payInfo.priceUnit?.currency ?? '-',
                //           ),
                //         ],
                //       ),
                //       style: const TextStyle(
                //         fontSize: 20.0,
                //         fontWeight: FontWeight.bold,
                //         height: 1.0,
                //       ),
                //       textAlign: TextAlign.center,
                //     ),
                //   ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _OrderItemShimmer extends StatelessWidget {
  const _OrderItemShimmer();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(19),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: MEShimmer(
        child: Column(
          spacing: 4.0,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 140,
              height: 16,
              color: context.theme.cardColor,
            ),
            Container(
              width: 80,
              height: 14,
              color: context.theme.cardColor,
            ),
          ],
        ),
      ),
    );
  }
}

class _MetalCover extends ConsumerWidget {
  const _MetalCover();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_itemProvider);
    return AspectRatio(
      aspectRatio: 318 / 200,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.0),
          boxShadow: [
            BoxShadow(
              color: context.theme.scaffoldBackgroundColor.withValues(alpha: 0.2),
              blurRadius: 10,
            ),
          ],
        ),
        child: Stack(
          fit: StackFit.expand,
          clipBehavior: Clip.none,
          children: [
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6.0),
                child: Assets.icons.images.coverMetalFront.svg(
                  fit: BoxFit.contain,
                ),
              ),
            ),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Container(
                width: 220.0,
                height: 140.0,
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: _calculateFontSize(item.name),
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (item.title.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 2.0),
                        child: Text(
                          item.title,
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (item.company.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 2.0),
                        child: Text(
                          item.company,
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _MetalCoverBack extends ConsumerWidget {
  const _MetalCoverBack();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_itemProvider);
    final fit = BoxFit.contain;
    return AspectRatio(
      aspectRatio: 318 / 200,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.0),
          boxShadow: [
            BoxShadow(
              color: context.theme.scaffoldBackgroundColor.withValues(alpha: 0.2),
              blurRadius: 10,
            ),
          ],
        ),
        child: MEImage(
          item.backCover,
          fit: fit,
          borderRadius: BorderRadius.circular(6.0),
          emptyBuilder: (context) => Assets.icons.images.coverMetalBack.svg(fit: fit),
        ),
      ),
    );
  }
}

class _NormalCover extends ConsumerWidget {
  const _NormalCover();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_itemProvider);
    return Container(
      width: 140,
      height: 220,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
        color: Colors.white,
      ),
      child: Stack(
        children: [
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Assets.icons.images.coverNormalFront.image(
                fit: BoxFit.cover,
              ),
            ),
          ),
          Column(
            children: [
              AspectRatio(
                aspectRatio: 1.0,
                child: MEImage(
                  item.image,
                  fit: BoxFit.cover,
                  clipOval: false,
                  alternativeSVG: true,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          item.name,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: _calculateFontSize(item.name),
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (item.title.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 2.0),
                            child: Text(
                              item.title,
                              style: const TextStyle(
                                color: Colors.black45,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        if (item.company.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 2.0),
                            child: Text(
                              item.company,
                              style: const TextStyle(
                                color: Colors.black45,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _NormalCoverBack extends ConsumerWidget {
  const _NormalCoverBack();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_itemProvider);
    final fit = BoxFit.cover;
    return Container(
      width: 140,
      height: 220,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: MEImage(
        item.backCover,
        fit: fit,
        borderRadius: BorderRadius.circular(16),
        emptyBuilder: (context) => Assets.icons.images.coverNormalBack.image(fit: fit),
      ),
    );
  }
}

double _calculateFontSize(String text) {
  if (text.isEmpty) {
    return 16.0;
  }
  if (text.length <= 10) {
    return 16.0;
  }
  if (text.length <= 15) {
    return 14.0;
  }
  if (text.length <= 20) {
    return 14.0;
  }
  return 12.0;
}

class DashedVerticalLinePainter extends CustomPainter {
  const DashedVerticalLinePainter({
    this.thickness = 1.0,
    this.length = 10.0,
    this.spacing = 10.0,
  });

  final double length;
  final double thickness;
  final double spacing;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey
      ..strokeWidth = thickness
      ..style = PaintingStyle.stroke;

    final path = Path()..lineTo(0, size.height);

    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final extractPath = pathMetric.extractPath(
          distance,
          distance + length,
        );
        canvas.drawPath(extractPath, paint);
        distance += length + spacing;
      }
    }
  }

  @override
  bool shouldRepaint(DashedVerticalLinePainter oldDelegate) => false;
}
