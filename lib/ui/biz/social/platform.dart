import 'dart:io' as io show File;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart' as picker;

import '/models/business.dart' show ImagePickResult, ImagePickFileResult, ImagePickUrlResult;
@FFAutoImport()
import '/models/card.dart' show Social, SocialPlatform;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/card.dart';
import '/ui/widgets/social/data.dart';

@FFRoute(name: '/social/platform')
class SocialPlatformPage extends ConsumerStatefulWidget {
  const SocialPlatformPage({
    super.key,
    required this.platform,
    this.social,
    this.currentHandle,
    this.specialGitHub = false,
  });

  final SocialPlatform platform;
  final Social? social;
  final String? currentHandle;
  final bool specialGitHub;

  @override
  ConsumerState<SocialPlatformPage> createState() => _SocialPlatformPageState();
}

class _SocialPlatformPageState extends ConsumerState<SocialPlatformPage> {
  late final SocialPlatform platform = widget.platform;
  late final Social? _social = widget.social;
  late final int? _socialId = _social?.id;

  final _cancelToken = CancelToken();
  final _formKey = GlobalKey<FormState>();
  final _picker = picker.ImagePicker();

  String? get _effectiveHandle => (widget.currentHandle?.orNull(_social?.handleName) ?? _social?.handleName)?.trim();

  late final TextEditingController _handleController;
  ImagePickResult? _imagePickResult;

  bool _isDeleting = false;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _handleController = TextEditingController(text: _effectiveHandle);
    if (widget.social?.imageUrl case final imageUrl?) {
      _imagePickResult = ImagePickResult.url(imageUrl);
    }
  }

  @override
  void dispose() {
    _handleController.dispose();
    _cancelToken.cancel();
    super.dispose();
  }

  Future<void> _handleSubmit() async {
    final text = _handleController.text.trim();
    if (platform.validateHandle(text) != null) {
      return;
    }
    if (platform.validateImage(_imagePickResult) != null) {
      return;
    }

    final handle = platform.formalizeHandle(text);
    final imageFilePath = switch (_imagePickResult) {
      ImagePickFileResult(:final path) => path,
      _ => null,
    };

    setState(() {
      _isSubmitting = true;
    });

    try {
      String? imageUrl;
      if (imageFilePath != null) {
        imageUrl = await ref
            .read(apiServiceProvider)
            .uploadImage(
              imageFilePath: imageFilePath,
              cancelToken: _cancelToken,
            );
      }
      if (widget.specialGitHub) {
        await ref
            .read(apiServiceProvider)
            .updateExtendGithubHandle(
              githubHandle: handle,
              cancelToken: _cancelToken,
            );
        globalContainer.invalidate(fetchExtendProfileProvider);
      } else {
        if (_socialId != null) {
          await ref
              .read(apiServiceProvider)
              .socialUpdate(
                socialId: _socialId.toString(),
                handleName: handle,
                platformName: platform.name,
                platformUrl: platform.url,
                imageUrl: imageUrl,
                cancelToken: _cancelToken,
              );
        } else {
          await ref
              .read(apiServiceProvider)
              .socialAdd(
                handleName: handle,
                platformName: platform.name,
                platformUrl: platform.url,
                imageUrl: imageUrl,
                cancelToken: _cancelToken,
              );
        }
        globalContainer.invalidate(fetchSocialsProvider);
      }
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToSubmit);
      rethrow;
    } finally {
      safeSetState(() {
        _isSubmitting = false;
      });
    }
  }

  Future<void> _handleDelete() async {
    setState(() {
      _isDeleting = true;
    });

    try {
      if (widget.specialGitHub) {
        await ref
            .read(apiServiceProvider)
            .updateExtendGithubHandle(
              githubHandle: '',
              cancelToken: _cancelToken,
            );
        // 这里可以添加刷新profile的逻辑
        ref.invalidate(fetchExtendProfileProvider);
      } else if (_socialId != null) {
        await ref
            .read(apiServiceProvider)
            .socialDelete(
              socialId: _socialId,
              cancelToken: _cancelToken,
            );

        ref.invalidate(fetchSocialsProvider);
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToDelete);
      rethrow;
    } finally {
      safeSetState(() {
        _isDeleting = false;
      });
    }
  }

  Future<void> _handleImagePick() async {
    try {
      final result = await _picker.pickImage(
        source: picker.ImageSource.gallery,
        requestFullMetadata: false,
      );
      if (result != null) {
        _imagePickResult = ImagePickResult.file(result.path);
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToOpenImagePicker);
      rethrow;
    } finally {
      safeSetState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final meTheme = theme.extension<METheme>()!;
    return AppScaffold(
      body: Padding(
        padding: const EdgeInsets.only(top: 40),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              spacing: 16.0,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    border: platform.demoImageKey != null
                        ? Border.all(color: Colors.grey[300]!)
                        : Border.all(color: Colors.transparent),
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  child: switch (platform.demoImageKey) {
                    final key? => SocialDemoImage(name: key),
                    _ => null,
                  },
                ),
                Row(
                  spacing: 12.0,
                  children: [
                    SocialSvgIcon(
                      platform: platform,
                      width: 40.0,
                      height: 40.0,
                      borderRadius: BorderRadius.circular(8.0),
                      clipOval: false,
                    ),
                    Expanded(
                      child: AutoSizeText(
                        platform.alias ?? platform.name,
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
                if (platform.allowHandle) ...[
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: theme.cardColor,
                    ),
                    child: TextFormField(
                      controller: _handleController,
                      autocorrect: false,
                      autofocus: _effectiveHandle?.isEmpty != false,
                      keyboardType: platform.keyboardType,
                      decoration: InputDecoration(
                        hintText: platform.placeholder ?? 'Enter username',
                        prefixIcon: switch (platform.addon) {
                          final addon? when addon.isNotEmpty => Container(
                            padding: const EdgeInsets.only(
                              left: 16,
                              right: 16,
                              top: 8,
                            ),
                            child: Text(
                              addon,
                              style: theme.textTheme.bodySmall?.copyWith(fontSize: 26),
                            ),
                          ),
                          _ => null,
                        },
                      ),
                      errorBuilder: (context, error) => Semantics(
                        container: true,
                        child: AnimatedSize(
                          duration: kThemeAnimationDuration,
                          curve: Curves.easeOutCubic,
                          child: error.isEmpty
                              ? const SizedBox.shrink()
                              : Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Text(
                                    error,
                                    style: TextStyle(color: meTheme.failingColor),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                        ),
                      ),
                      onChanged: (_) => _formKey.currentState?.validate(),
                      validator: platform.validateHandle,
                      onFieldSubmitted: (_) => _handleSubmit(),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (platform.url.or('') case final url)
                    Padding(
                      padding: const EdgeInsetsDirectional.only(start: 4.0),
                      child: ValueListenableBuilder(
                        valueListenable: _handleController,
                        builder: (context, value, _) {
                          String handle = platform.formalizeHandle(value.text.trim());
                          if (url.isNotEmpty) {
                            handle = handle.replaceAll(url, '');
                          }
                          return Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(text: url),
                                TextSpan(
                                  text: handle,
                                  style: TextStyle(color: theme.textTheme.bodyMedium?.color),
                                ),
                              ],
                            ),
                            style: theme.textTheme.bodySmall?.copyWith(fontSize: 16),
                          );
                        },
                      ),
                    ),
                ],
                if (platform.allowImage) ...[
                  const Gap.v(34.0),
                  if (_imagePickResult case final pickResult?)
                    Container(
                      constraints: const BoxConstraints(
                        minWidth: 640.0,
                        maxWidth: 640.0,
                        minHeight: 100.0,
                        maxHeight: 600.0,
                      ),
                      child: RippleTap(
                        onTap: _handleImagePick,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16.0),
                          side: BorderSide(color: theme.dividerColor),
                        ),
                        child: switch (pickResult) {
                          ImagePickFileResult(:final path) => Image.file(io.File(path)),
                          ImagePickUrlResult(:final url) => MEImage(
                            url,
                            fit: BoxFit.contain,
                            borderRadius: BorderRadius.circular(16.0),
                            backgroundColor: theme.colorScheme.surface,
                            errorBuilder: (context, _, _) => Padding(
                              padding: const EdgeInsets.all(30.0),
                              child: Assets.icons.placeholderBroken.svg(height: 42.0, fit: BoxFit.contain),
                            ),
                          ),
                        },
                      ),
                    )
                  else
                    Container(
                      height: 200.0,
                      margin: const EdgeInsets.only(bottom: 16.0),
                      alignment: Alignment.center,
                      child: AspectRatio(
                        aspectRatio: 1.0,
                        child: CustomPaint(
                          painter: const DottedBorderPainter(
                            length: 4.0,
                            spacing: 4.0,
                            borderRadius: 16.0,
                          ),
                          child: RippleTap(
                            onTap: _handleImagePick,
                            child: Column(
                              spacing: 8.0,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Flexible(
                                  child: FittedBox(
                                    fit: BoxFit.contain,
                                    child: Icon(Icons.image_outlined, size: 48.0),
                                  ),
                                ),
                                Text(platform.imageUploadTip ?? 'Upload Image'),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  if (platform.imageExtraTip case final extraTip?)
                    Center(
                      child: Text(
                        extraTip,
                        style: theme.textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  if (_imagePickResult != null)
                    ThemeTextButton.outlined(
                      onPressed: _handleImagePick,
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        spacing: 10.0,
                        children: [
                          Icon(Icons.photo_library_outlined),
                          Text('Change Another Image'),
                        ],
                      ),
                    ),
                ],
              ],
            ),
          ),
        ),
      ),
      bottomButtonBuilder: (context) => ValueListenableBuilder(
        valueListenable: _handleController,
        builder: (context, value, _) => ThemeTextButtonGroup(
          showCancel: _socialId != null || (widget.specialGitHub && value.text.trim().isNotEmpty),
          fallbackToDefaultCancel: false,
          onCancel: _isDeleting || _isSubmitting ? null : _handleDelete,
          cancelThemeColor: context.meTheme.failingColor,
          cancelChild: _isDeleting ? const AppLoading() : Text(context.l10nME.deleteButton),
          fallbackToDefaultConfirm: false,
          onConfirm:
              _isSubmitting ||
                  _isDeleting ||
                  platform.validateHandle(value.text) != null ||
                  platform.validateImage(_imagePickResult) != null
              ? null
              : _handleSubmit,
          confirmChild: _isSubmitting ? const AppLoading() : Text(context.l10nME.submitButton),
        ),
      ),
    );
  }
}
