import 'dart:convert';
import 'dart:io' as io show File;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:image_size_getter/image_size_getter.dart';

import '/models/business.dart' show ImagePickResult, ImagePickFileResult, ImagePickUrlResult;
import '/models/card.dart';
import '/provider/api.dart' show apiServiceProvider;
import '/provider/card.dart'
    show
        fetchExtendProfileProvider,
        fetchExtendProfileTopicsProvider,
        fetchExtendProfileRolesProvider,
        fetchSocialsProvider;
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;
import '/ui/widgets/social/data.dart';
import '/ui/widgets/social/profile/avatar_img.dart';
import '/ui/widgets/social/profile/description.dart';
import '/ui/widgets/social/social_grid.dart';
import '/ui/widgets/topic.dart' show TopicsSheet;

@FFRoute(name: '/social/edit-profile')
class SocialEditProfilePage extends StatelessWidget {
  const SocialEditProfilePage({
    super.key,
    this.pendingAvatarUrl,
  });

  final String? pendingAvatarUrl;

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      body: ListView(
        children: [
          _BasicProfile(pendingAvatarUrl: pendingAvatarUrl),
          const Divider(height: 82.0, thickness: 1.5),
          const _Socials(),
          const Gap.v(40.0),
          const _Optionals(),
          const Gap.v(200.0),
        ],
      ),
    );
  }
}

class _BasicProfile extends ConsumerStatefulWidget {
  const _BasicProfile({required this.pendingAvatarUrl});

  final String? pendingAvatarUrl;

  @override
  ConsumerState<_BasicProfile> createState() => _AvatarState();
}

class _AvatarState extends ConsumerState<_BasicProfile> {
  final _cancelToken = CancelToken();

  ImagePickResult? _avatarPickResult;

  @override
  void initState() {
    super.initState();
    _fetchUserInfo();
    if (widget.pendingAvatarUrl case final url?) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _handleSubmit(withAvatarOnly: true, pendingAvatarUrl: url);
      });
    }
  }

  @override
  void dispose() {
    _cancelToken.cancel();
    super.dispose();
  }

  Future<void> _fetchUserInfo() async {
    final localUser = ref.read(userRepoProvider);
    if (localUser?.avatar case final avatar? when avatar.isNotEmpty) {
      _avatarPickResult = ImagePickResult.url(avatar);
    }

    final userInfo = await ref.read(fetchUserInfoProvider().future);
    safeSetState(() {
      if (userInfo.avatar.isNotEmpty) {
        _avatarPickResult = ImagePickResult.url(userInfo.avatar);
      }
    });
  }

  Future<void> _handleImageSelected(ImagePickResult result) async {
    if (!mounted) {
      return;
    }
    setState(() {
      _avatarPickResult = result;
    });
    await _handleSubmit(withAvatarOnly: true);
  }

  Future<void> _handleSubmit({
    bool withAvatarOnly = false,
    String? pendingAvatarUrl,
  }) async {
    try {
      String? avatarUrl = pendingAvatarUrl;
      if (avatarUrl == null) {
        switch (_avatarPickResult) {
          case ImagePickFileResult(:final path):
            final bytes = await io.File(path).readAsBytes();
            final imageResult = ImageSizeGetter.getSizeResult(MemoryInput(bytes));
            final mimeType = 'image/${imageResult.decoder.decoderName}';
            final encoded = base64Encode(bytes);
            final result = 'data:$mimeType;base64,$encoded';
            avatarUrl = await ref
                .read(apiServiceProvider)
                .uploadAvatar(
                  fileContent: result,
                  cancelToken: _cancelToken,
                );
          case ImagePickUrlResult(:final url):
            avatarUrl = url;
          case null:
            break;
        }
      }
      if (!mounted) {
        return;
      }

      await ref
          .read(apiServiceProvider)
          .updateUserInfo(
            avatar: avatarUrl,
            dynamicAvatar: '',
            cancelToken: _cancelToken,
          );

      if (mounted) {
        _avatarPickResult = avatarUrl != null ? ImagePickResult.url(avatarUrl) : null;
        ref.invalidate(fetchUserInfoProvider);
        Card3ToastUtil.showToast(message: ToastMessages.updated);
        if (!withAvatarOnly) {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToUpdate);
      }
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    final localUser = ref.watch(userRepoProvider);
    final userResult = ref.watch(fetchUserInfoProvider());
    final user = userResult.valueOrNull ?? localUser;
    final theme = context.theme;
    final textTheme = theme.textTheme;
    return Center(
      child: Tapper(
        onTap: () {
          showModalBottomSheet(
            context: context,
            scrollControlDisabledMaxHeightRatio: 0.8,
            builder: (context) => const DescriptionActionSheet(),
          );
        },
        child: Column(
          spacing: 6.0,
          mainAxisSize: MainAxisSize.min,
          children: [
            AvatarImgPicker(
              avatar: _avatarPickResult,
              onImageSelected: _handleImageSelected,
              size: 150.0,
              borderRadius: BorderRadius.circular(20.0),
            ),
            const Gap.v(24.0),
            Row(
              spacing: 6.0,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  user?.name.or('Name') ?? 'Name',
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                CircleAvatar(
                  backgroundColor: theme.iconTheme.color,
                  radius: 12.0,
                  child: Icon(
                    Icons.edit_rounded,
                    color: theme.scaffoldBackgroundColor,
                    size: 16.0,
                  ),
                ),
              ],
            ),
            Text(
              user?.title.or('Title') ?? 'Title',
              style: TextStyle(
                color: (user?.title ?? '').isEmpty ? textTheme.bodySmall?.color : null,
                fontSize: 16.0,
                height: 1.0,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            Text(
              user?.company.or('Company') ?? 'Company',
              style: TextStyle(
                color: (user?.company ?? '').isEmpty ? textTheme.bodySmall?.color : null,
                fontSize: 16.0,
                height: 1.0,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ],
        ),
      ),
    );
  }
}

final _socialsReorderingListProvider = StateProvider.autoDispose<List<Social>?>((ref) => null);

class _Socials extends ConsumerStatefulWidget {
  const _Socials();

  @override
  ConsumerState<_Socials> createState() => _SocialsState();
}

class _SocialsState extends ConsumerState<_Socials> {
  CancelToken? _cancelToken;

  void _add() {
    final socialsResult = ref.read(fetchSocialsProvider());
    if (socialsResult.hasError && !socialsResult.isLoading) {
      ref.invalidate(fetchSocialsProvider());
      return;
    }
    showModalBottomSheet(
      context: context,
      scrollControlDisabledMaxHeightRatio: 0.7,
      builder: (context) => const _SocialDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final socialsResult = ref.watch(fetchSocialsProvider());
    final socialsReordering = ref.watch(_socialsReorderingListProvider);
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Social Links',
          style: context.textTheme.headlineSmall,
        ),
        socialsResult.when(
          data: (data) {
            if (data.isEmpty) {
              return _buildSocialsPlaceholder(context);
            }
            final List<Social> list;
            if (socialsReordering != null) {
              list = socialsReordering;
            } else {
              list = data;
            }
            return ReorderableList(
              padding: const EdgeInsets.only(top: 8.0),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemExtent: 64.0,
              itemCount: list.length,
              itemBuilder: (context, index) {
                final social = list[index];
                return ReorderableDelayedDragStartListener(
                  enabled: list.length > 1,
                  index: index,
                  key: ValueKey('social-reorderable-${social.id}'),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: ProviderScope(
                      overrides: [
                        _socialItemProvider.overrideWithValue(social),
                      ],
                      child: const _SocialItem(),
                    ),
                  ),
                );
              },
              proxyDecorator: (child, index, animation) {
                final social = list[index];
                return ScaleTransition(
                  scale: animation.drive(Tween(begin: 1.0, end: 1.06)),
                  child: ProviderScope(
                    overrides: [
                      _socialItemProvider.overrideWithValue(social),
                    ],
                    child: const _SocialItem(),
                  ),
                );
              },
              onReorder: (int oldIndex, int newIndex) async {
                if (oldIndex == newIndex) {
                  return;
                }

                final newList = list.toList();
                final item = newList.removeAt(oldIndex);
                newList.insert(
                  newIndex - oldIndex > 0 ? newIndex - 1 : newIndex,
                  item,
                );
                ref.read(_socialsReorderingListProvider.notifier).state = newList;
                final reorderedMap = Map.fromEntries(
                  newList.mapIndexed(
                    (i, e) => MapEntry<String, int>(e.id.toString(), newList.length - i),
                  ),
                );
                // Cancel the previous sort request.
                _cancelToken?.cancel();
                final cancelToken = _cancelToken = CancelToken();
                await ref
                    .read(apiServiceProvider)
                    .socialsReorder(
                      idInOrders: reorderedMap,
                      cancelToken: cancelToken,
                    );
                if (mounted) {
                  final _ = await ref.refresh(fetchSocialsProvider().future);
                }
                if (mounted) {
                  ref.read(_socialsReorderingListProvider.notifier).state = null;
                }
              },
            );
          },
          loading: () => const Padding(
            padding: EdgeInsets.symmetric(vertical: 24.0),
            child: Center(child: CircularProgressIndicator()),
          ),
          error: (error, stack) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Text(
              isNetworkError(error) ? context.l10nME.networkError : context.l10nME.clickToRetryButton,
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ),
        if (socialsResult.valueOrNull case final value? when value.isNotEmpty)
          ThemeTextButton.outlined(
            onPressed: _add,
            margin: const EdgeInsets.only(top: 5.0),
            height: 50.0,
            borderSideWidth: 2.0,
            text: '+ Add New Link',
            textStyle: context.textTheme.bodyMedium?.copyWith(fontSize: 16.0),
          ),
      ],
    );
  }

  Widget _buildSocialsPlaceholder(BuildContext context) {
    // Take the first 8 platforms from all.
    final platforms = SocialPlatform.values
        .where((o) => o.events == null)
        .run((it) => it.take(it.length.min(8)))
        .toList()
        .reversed;
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Row(
        spacing: 12.0,
        children: [
          Expanded(
            child: Container(
              height: 40.0,
              padding: const EdgeInsetsDirectional.only(end: 1.0),
              foregroundDecoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    switch (context.brightness) {
                      Brightness.dark => const Color(0x00000000),
                      Brightness.light => const Color(0x00ffffff),
                    },
                    context.theme.scaffoldBackgroundColor,
                  ],
                  stops: [0.7, 1.0],
                ),
              ),
              child: Stack(
                children: List.generate(
                  platforms.length,
                  (index) => Positioned(
                    left: (platforms.length - index - 1) * 30.0,
                    child: SocialSvgIcon(
                      platform: platforms.elementAt(index),
                      width: 40.0,
                      height: 40.0,
                      clipOval: false,
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                ),
              ),
            ),
          ),
          ThemeTextButton.outlined(
            onPressed: _add,
            alignment: null,
            width: null,
            height: 48.0,
            padding: const EdgeInsets.symmetric(horizontal: 14.0),
            borderRadius: BorderRadius.circular(20.0),
            borderSideWidth: 2.0,
            text: '+ Add',
            textStyle: context.textTheme.bodyMedium?.copyWith(fontSize: 16.0),
          ),
        ],
      ),
    );
  }
}

final _socialItemProvider = Provider.autoDispose<Social>(
  (ref) => throw UnimplementedError(),
);

class _SocialItem extends ConsumerWidget {
  const _SocialItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final social = ref.watch(_socialItemProvider);
    final platform = SocialPlatform.values.firstWhere(
      (option) => option.name == social.platformName,
    );

    final socialsResult = ref.watch(fetchSocialsProvider());
    final socials = socialsResult.valueOrNull ?? <Social>[];

    return RippleTap(
      onTap: () {
        meNavigator.pushNamed(
          Routes.socialPlatform.name,
          arguments: Routes.socialPlatform.d(
            social: social,
            platform: platform,
          ),
        );
      },
      height: 50.0,
      padding: const EdgeInsets.symmetric(vertical: 2),
      borderRadius: BorderRadius.circular(20.0),
      color: ColorName.primaryTextColorLight,
      child: Row(
        spacing: 10.0,
        children: [
          if (socials.length > 1)
            Container(
              width: 20.0,
              padding: const EdgeInsetsDirectional.only(start: 10.0),
              child: Assets.icons.dragHandle.svg(colorFilter: Colors.grey.filter),
            )
          else
            const SizedBox.shrink(),
          SocialSvgIcon(
            platform: platform,
            width: 30.0,
            height: 30.0,
            clipOval: false,
            borderRadius: BorderRadius.circular(10.0),
          ),
          Expanded(
            child: Row(
              spacing: 4.0,
              children: [
                Flexible(
                  child: Text.rich(
                    TextSpan(
                      children: [
                        if (platform.addon case final addon? when addon.isNotEmpty)
                          TextSpan(
                            text: '$addon ',
                            style: const TextStyle(
                              color: Colors.grey,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        if (platform.allowHandle)
                          TextSpan(text: social.handleName)
                        else
                          TextSpan(text: platform.alias ?? platform.name),
                      ],
                    ),
                    style: context.textTheme.headlineSmall,
                    maxLines: 1,
                    overflow: TextOverflow.fade,
                    softWrap: false,
                  ),
                ),
                if (social.imageUrl.isNotEmpty) const Icon(Icons.photo_outlined, size: 24.0),
              ],
            ),
          ),
          if (social.verified)
            Padding(
              padding: const EdgeInsetsDirectional.only(end: 12),
              child: Assets.icons.socialVerified.svg(width: 24, height: 24),
            ),
        ],
      ),
    );
  }
}

class _SocialDialog extends ConsumerWidget {
  const _SocialDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0).copyWith(bottom: 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 不要占用全部空间
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Add Links',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, size: 24),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),
          const Expanded(child: SocialGrid()),
        ],
      ),
    );
  }
}

final _extendProfileProvider = Provider.autoDispose<ExtendProfile>(
  (ref) => throw UnimplementedError(),
);

class _Optionals extends ConsumerWidget {
  const _Optionals();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(fetchExtendProfileProvider());
    return Column(
      spacing: 10.0,
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Optional',
          style: context.textTheme.headlineSmall,
        ),
        result.when(
          data: (data) => ProviderScope(
            overrides: [_extendProfileProvider.overrideWithValue(data)],
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 10.0,
              children: [
                _ItemsWrapped(
                  buildItems: (profile) => profile.roles,
                  provider: fetchExtendProfileRolesProvider,
                  addText: 'I am a/an',
                  addTextShort: 'I am',
                  onSubmit: (selected) => ref
                      .watch(apiServiceProvider)
                      .updateExtendRoles(
                        roles: selected.join(','),
                      ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.0),
                    gradient: const LinearGradient(
                      colors: [
                        Color.fromRGBO(136, 88, 255, 0.2),
                        Color.fromRGBO(149, 86, 255, 0.7),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                ),
                _ItemsWrapped(
                  buildItems: (profile) => profile.topics,
                  provider: fetchExtendProfileTopicsProvider,
                  addText: 'Talk to me about',
                  addTextShort: 'Talk',
                  onSubmit: (selected) => ref
                      .watch(apiServiceProvider)
                      .updateExtendTopics(
                        topics: selected.join(','),
                      ),
                ),
                const _GitHubContributions(),
              ],
            ),
          ),
          loading: () => MEShimmer(
            child: Column(
              spacing: 10.0,
              children: List.generate(
                3,
                (_) => Container(
                  width: double.infinity,
                  height: kMinInteractiveDimension,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.0),
                    color: context.theme.cardColor,
                  ),
                ),
              ),
            ),
          ),
          error: (e, s) => RippleTap(
            width: double.infinity,
            height: kMinInteractiveDimension,
            borderRadius: BorderRadius.circular(20.0),
            color: context.theme.cardColor,
            child: Text(context.l10nME.clickToRetryButton),
          ),
        ),
      ],
    );
  }
}

class _ItemsWrapped extends ConsumerWidget {
  const _ItemsWrapped({
    required this.buildItems,
    required this.provider,
    required this.addText,
    required this.addTextShort,
    required this.onSubmit,
    this.decoration,
  });

  final List<String> Function(ExtendProfile profile) buildItems;
  final AutoDisposeFutureProvider<List<String>> provider;
  final String addText;
  final String addTextShort;
  final Future<void> Function(List<String> selected) onSubmit;
  final BoxDecoration? decoration;

  void showSelections(WidgetRef ref, List<String> selected) {
    ScrollableBottomSheet.show(
      builder: (context) => TopicsSheet(
        title: addText,
        provider: provider,
        selected: selected.toList(),
        onSubmit: (selected) async {
          await onSubmit(selected);
          ref.invalidate(fetchExtendProfileProvider);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final items = buildItems(ref.watch(_extendProfileProvider));
    if (items.isEmpty) {
      return ThemeTextButton.outlined(
        onPressed: () => showSelections(ref, items),
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        borderSideWidth: 2,
        childAlignment: AlignmentDirectional.centerStart,
        textStyle: context.textTheme.bodyMedium?.copyWith(fontSize: 16.0),
        text: '+ $addText',
      );
    }

    final theme = context.theme;
    return Wrap(
      spacing: 10.0,
      runSpacing: 10.0,
      children: [
        ...items.map(
          (item) => IntrinsicWidth(
            child: Container(
              height: kMinInteractiveDimension,
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              decoration:
                  decoration ??
                  BoxDecoration(
                    borderRadius: BorderRadius.circular(20.0),
                    color: theme.cardColor,
                  ),
              child: Text(
                item,
                style: const TextStyle(fontSize: 16.0),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ),
        ThemeTextButton.outlined(
          onPressed: () => showSelections(ref, items),
          width: null,
          alignment: null,
          intrinsicWidth: true,
          height: kMinInteractiveDimension,
          borderSideWidth: 2,
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          text: '+ $addTextShort',
          textStyle: context.textTheme.bodyMedium?.copyWith(fontSize: 16.0),
        ),
      ],
    );
  }
}

class _GitHubContributions extends ConsumerWidget {
  const _GitHubContributions();

  final platform = SocialPlatform.github;

  void edit(String handle) {
    meNavigator.pushNamed(
      Routes.socialPlatform.name,
      arguments: Routes.socialPlatform.d(
        platform: platform,
        currentHandle: handle,
        specialGitHub: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final handle = ref.watch(_extendProfileProvider).githubHandle;
    if (handle.isEmpty) {
      return ThemeTextButton.outlined(
        onPressed: () => edit(handle),
        height: kMinInteractiveDimension,
        borderSideWidth: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        childAlignment: AlignmentDirectional.centerStart,
        text: '+ GitHub Contributions',
        textStyle: context.textTheme.bodyMedium?.copyWith(fontSize: 16.0),
      );
    }

    final platform = SocialPlatform.github;
    return RippleTap(
      onTap: () => edit(handle),
      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 2.0),
      height: 50,
      borderRadius: BorderRadius.circular(20.0),
      color: ColorName.primaryTextColorLight,
      child: Row(
        spacing: 8.0,
        children: [
          SocialSvgIcon(
            platform: platform,
            width: 32.0,
            height: 32.0,
            clipOval: true,
          ),
          Expanded(
            child: Text.rich(
              TextSpan(
                children: [
                  if (platform.addon case final addon? when addon.isNotEmpty)
                    TextSpan(
                      text: '$addon ',
                      style: const TextStyle(
                        color: Colors.grey,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                  TextSpan(text: handle),
                ],
              ),
              style: context.textTheme.headlineSmall,
              maxLines: 1,
              overflow: TextOverflow.fade,
              softWrap: false,
            ),
          ),
          Text(
            'GitHub Contributions',
            style: context.textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}
