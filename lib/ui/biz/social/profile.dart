import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '/feat/video/player.dart' show AppNetworkVideoPlayer;
@FFAutoImport()
import '/models/card.dart' show ExtendProfile, Social, SocialPlatform;
@FFAutoImport()
import '/models/user.dart';
import '/provider/api.dart' show apiServiceProvider;
import '/provider/card.dart';
import '/provider/user.dart' show fetchUserRelationProvider;
import '/ui/biz/other/share.dart' show ShareDialog;
import '/ui/widgets/animated_colors_border.dart';
import '/ui/widgets/social/data.dart' show SocialSvgIcon;

final _dataProvider = Provider.autoDispose<(String, UserInfo?)>(
  (ref) => throw UnimplementedError(),
);

@FFRoute(name: '/social/profile')
class SocialProfilePage extends ConsumerStatefulWidget {
  const SocialProfilePage({
    super.key,
    required this.code,
    this.profile,
    this.avatar,
  });

  final String code;
  final UserInfo? profile;
  final UserWithAvatar? avatar;

  @override
  ConsumerState<SocialProfilePage> createState() => _SocialProfilePageState();
}

class _SocialProfilePageState extends ConsumerState<SocialProfilePage> {
  @override
  Widget build(BuildContext context) {
    final code = widget.profile?.referralCode ?? widget.code;
    final localUser = ref.watch(userRepoProvider);
    final profileResult = ref.watch(fetchPublicProfileProvider(code: code)).valueOrNull;
    final showRelation =
        localUser != null && profileResult != null && localUser.referralCode != profileResult.referralCode;
    return ProviderScope(
      overrides: [
        _dataProvider.overrideWithValue((code, widget.profile)),
      ],
      child: Scaffold(
        body: Stack(
          fit: StackFit.expand,
          children: [
            CustomScrollView(
              slivers: [
                SliverGap.topPadding(context),
                _AvatarWithUser(widget.profile ?? widget.avatar),
                const _Header(),
                const _ExtendProfile(),
                const _SocialLinkList(),
                const SliverGap.v(160.0),
              ],
            ),
            PositionedDirectional(
              top: MediaQuery.paddingOf(context).top + 12.0,
              bottom: null,
              start: 16.0,
              end: 16.0,
              child: Row(
                children: [
                  const AppBackButton(),
                  const Spacer(),
                  const _LoadingIndicator(),
                  if (widget.profile != null) const _EditButton(),
                ],
              ),
            ),
            if (showRelation) const Positioned.fill(top: null, child: _Relation()),
            if (widget.profile != null) const Positioned.fill(top: null, child: _ShareButton()),
            if (localUser == null) const Positioned.fill(top: null, child: _JoinButton()),
          ],
        ),
      ),
    );
  }
}

class _LoadingIndicator extends ConsumerWidget {
  const _LoadingIndicator();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, profile) = ref.watch(_dataProvider);
    final userCode = ref.watch(userRepoProvider)?.referralCode;

    final profileResult = ref.watch(fetchPublicProfileProvider(code: code));
    final socialsResult = ref.watch(fetchSocialsProvider(code: code));
    final relationResult = ref.watch(fetchUserRelationProvider(code: code));

    final loading =
        profileResult.isLoading || socialsResult.isLoading || (code != userCode && relationResult.isLoading);

    final error = profileResult.hasError || socialsResult.hasError || relationResult.hasError;

    if (loading) {
      return const Padding(
        padding: EdgeInsetsDirectional.only(end: 4.0),
        child: AppLoading(size: 44.0),
      );
    }

    if (error) {
      return IconButton(
        onPressed: () {
          if (profileResult.hasError) {
            ref.invalidate(fetchPublicProfileProvider(code: code));
          }
          if (socialsResult.hasError) {
            ref.invalidate(fetchSocialsProvider(code: code));
          }
          if (relationResult.hasError) {
            ref.invalidate(fetchUserRelationProvider(code: code));
          }
        },
        icon: Icon(Icons.refresh_rounded, color: context.meTheme.failingColor),
      );
    }

    return const SizedBox.shrink();
  }
}

class _EditButton extends StatelessWidget {
  const _EditButton();

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () => context.navigator.pushNamed(Routes.socialEditProfile.name),
      icon: Container(
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          color: ColorName.cardColorDark,
          shape: BoxShape.circle,
        ),
        child: Assets.icons.buttonEdit.svg(
          width: 14.0,
          height: 14.0,
          colorFilter: context.textTheme.bodyMedium?.color?.filter,
        ),
      ),
    );
  }
}

class _AvatarWithUser extends ConsumerWidget {
  const _AvatarWithUser(this.user);

  final UserWithAvatar? user;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, profile) = ref.watch(_dataProvider);
    final profileResult = ref.watch(fetchPublicProfileProvider(code: code));
    final effectiveProfile = profileResult.valueOrNull ?? profile;

    final maxExtent = MediaQuery.sizeOf(context).width;
    final loading = user == null && profile == null && profileResult.isLoading;
    final dynamicAvatar = (user ?? effectiveProfile)?.dynamicAvatar;
    final showVideo = dynamicAvatar != null && dynamicAvatar.isNotEmpty && !loading;
    return SliverToBoxAdapter(
      child: RepaintBoundary(
        child: AspectRatio(
          aspectRatio: 1.0,
          child: Stack(
            children: [
              if (user == null && profile == null && profileResult.isLoading)
                const AppLoading()
              else
                UserAvatar(
                  user: user ?? effectiveProfile,
                  dimension: maxExtent,
                  borderRadius: BorderRadius.circular(50.0),
                ),
              if (showVideo) AppNetworkVideoPlayer(url: dynamicAvatar, pauseWhenInvisible: false),
              if (effectiveProfile?.firstConnectDate case final first?)
                PositionedDirectional(
                  start: 16.0,
                  end: showVideo ? 66.0 : 16.0,
                  bottom: 16.0,
                  child: Align(
                    alignment: AlignmentDirectional.centerStart,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
                      decoration: BoxDecoration(
                        border: Border.all(color: context.theme.dividerColor),
                        borderRadius: RadiusConstants.max,
                        color: context.theme.colorScheme.surface.withValues(alpha: 0.8),
                      ),
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          '🤝 Met on ${first.format(format: 'yMMMd')}',
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, profile) = ref.watch(_dataProvider);
    final profileResult = ref.watch(fetchPublicProfileProvider(code: code));
    final effectiveProfile = profileResult.valueOrNull ?? profile;

    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      sliver: SliverToBoxAdapter(
        child: Column(
          spacing: 6.0,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Gap.v(24.0),
            Text(
              effectiveProfile?.run((it) => it.name.isNotEmpty ? it.name : '(Name)') ?? '',
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            if (effectiveProfile?.title case final title? when title.isNotEmpty)
              Text(
                title,
                style: const TextStyle(fontSize: 16.0, height: 1.0),
                textAlign: TextAlign.center,
              ),
            if (effectiveProfile?.company case final company? when company.isNotEmpty)
              Text(
                company,
                style: const TextStyle(fontSize: 16.0, height: 1.0),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      ),
    );
  }
}

Widget _buildTitle(BuildContext context, String title) {
  return Padding(
    padding: const EdgeInsets.only(top: 40.0),
    child: Text(
      title,
      style: context.textTheme.bodySmall?.copyWith(fontSize: 14.0),
    ),
  );
}

class _ExtendProfile extends ConsumerWidget {
  const _ExtendProfile();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, _) = ref.watch(_dataProvider);
    final result = ref.watch(fetchExtendProfileProvider(code: code));
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      sliver: SliverToBoxAdapter(
        child: AnimatedSize(
          duration: kThemeAnimationDuration,
          curve: Curves.easeInOutCubic,
          child: result.maybeWhen(
            data: (data) {
              if (data.isEmpty) {
                return const SizedBox.shrink();
              }
              return _buildProfile(context, data);
            },
            orElse: () => const SizedBox.shrink(),
          ),
        ),
      ),
    );
  }

  Widget _buildProfile(BuildContext context, ExtendProfile profile) {
    return Column(
      spacing: 6.0,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.only(top: 10.0),
          child: Wrap(
            spacing: 6.0,
            runSpacing: 6.0,
            alignment: WrapAlignment.center,
            children: profile.roles.map((role) => _buildRole(context, role)).toList(),
          ),
        ),
        if (profile.topics.isNotEmpty) ...[
          _buildTitle(context, 'Talk to Me About'),
          _buildItems(context, profile.topics),
        ],
        if (profile.githubHandle.isNotEmpty) ...[
          _buildTitle(context, 'GitHub Contributions'),
          _GitHubContributions(profile.githubHandle),
        ],
      ],
    );
  }

  Widget _buildItems(
    BuildContext context,
    List<String> items, {
    void Function(String item)? onItemTap,
  }) {
    return Wrap(
      spacing: 10.0,
      runSpacing: 10.0,
      children: items
          .map(
            (item) => IntrinsicWidth(
              child: RippleTap(
                onTap: () => onItemTap?.call(item),
                height: kMinInteractiveDimension,
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                  side: BorderSide(color: context.themeColor, width: 2.0),
                ),
                child: Text(
                  item,
                  style: const TextStyle(fontSize: 16.0),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildRole(BuildContext context, String role) {
    return IntrinsicWidth(
      child: Container(
        height: 30.0,
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 12.0),
        decoration: const BoxDecoration(
          borderRadius: RadiusConstants.max,
          gradient: LinearGradient(
            colors: [
              Color.fromRGBO(136, 88, 255, 0.2),
              Color.fromRGBO(149, 86, 255, 0.7),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Text(
          role,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}

class _GitHubContributions extends ConsumerWidget {
  const _GitHubContributions(this.handle);

  final String handle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(fetchGitHubContributionsProvider(handle: handle));
    final noDataWidget = Container(
      height: 129.0,
      alignment: Alignment.center,
      child: const Text(
        'No data available.',
        style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
      ),
    );
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.0),
        color: context.theme.cardColor,
      ),
      child: Column(
        spacing: 12.0,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: AutoSizeText.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: result.maybeWhen(
                      data: (data) {
                        if (data == null) {
                          return "$handle's";
                        }
                        return data.calendar.total.toString();
                      },
                      orElse: () => "$handle's",
                    ),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(text: ' public contributions in the last year'),
                ],
              ),
              maxLines: 1,
            ),
          ),
          result.when(
            data: (data) {
              if (data == null) {
                return noDataWidget;
              }
              return GitHubContributionGraph(collection: data);
            },
            loading: () => const SizedBox(height: 129.0, child: AppLoading()),
            error: (e, s) => noDataWidget,
          ),
        ],
      ),
    );
  }
}

class _SocialItem {
  const _SocialItem({
    required this.platform,
    required this.social,
  });

  final SocialPlatform platform;
  final Social social;

  String get formalizedHandle => platform.formalizeHandle(social.handleName);

  String? get formalizedUrl {
    String? url;
    if (formalizedHandle.trim() case final handle when handle.isNotEmpty) {
      if (platform.url.trim() case final u when u.isNotEmpty) {
        url = '$u${handle.replaceAll(u, '')}';
      } else {
        url = handle;
      }
      url = url.trim();
    }

    return url;
  }

  Future<bool> launchIfApplicable() async {
    if (!platform.allowHandle && platform.allowImage) {
      final imageUrl = social.imageUrl;
      if (imageUrl.isEmpty) {
        Card3ToastUtil.showToast(message: ToastMessages.couldNotLaunch('empty URL'));
        return false;
      }
      meNavigator.pushNamed(
        Routes.imageViewer.name,
        arguments: Routes.imageViewer.d(imageUrl: imageUrl),
      );
      return true;
    }

    final url = formalizedUrl;
    if (url == null || url.isEmpty) {
      Card3ToastUtil.showToast(message: ToastMessages.couldNotLaunch(url?.toString() ?? 'empty URL'));
      return false;
    }

    final uri = Uri.tryParse(url);
    if (uri == null) {
      Card3ToastUtil.showToast(message: ToastMessages.couldNotLaunch(url.toString()));
      return false;
    }

    final parsedUrl = uri.toString();
    final toastIfCannotLaunch =
        '${ToastMessages.couldNotLaunch(parsedUrl)}. '
        'The original link was copied to your clipboard.';
    if (uri.scheme.isEmpty || (RegExp(r'^https?$').hasMatch(uri.scheme) && uri.host.isEmpty)) {
      Card3ToastUtil.showToast(message: toastIfCannotLaunch);
      copy(parsedUrl);
      return false;
    }

    if (!await canLaunchUrl(uri)) {
      Card3ToastUtil.showToast(message: toastIfCannotLaunch);
      copy(parsedUrl);
      return false;
    }

    if (platform == SocialPlatform.link) {
      final ask = await TinyDialog.show<bool>(
        text: 'You are about to open an external link, proceed with caution.',
        captionText: parsedUrl,
        buttonsBuilder: (context) => TinyDialogButtonGroup(
          cancelText: context.l10nME.laterButton,
        ),
      );
      if (ask != true) {
        return false;
      }
    }

    return launchUrl(uri, mode: LaunchMode.externalApplication);
  }
}

class _SocialLinkList extends ConsumerWidget {
  const _SocialLinkList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, _) = ref.watch(_dataProvider);
    final socialsAsync = ref.watch(fetchSocialsProvider(code: code));
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      sliver: SliverToBoxAdapter(
        child: AnimatedSize(
          duration: kThemeAnimationDuration,
          curve: Curves.easeInOutCubic,
          child: socialsAsync.maybeWhen(
            data: (data) => _buildList(context, data),
            orElse: () => const SizedBox.shrink(),
          ),
        ),
      ),
    );
  }

  Widget _buildList(BuildContext context, List<Social> socials) {
    final socialItems = <_SocialItem>[];
    for (final social in socials) {
      final platform = SocialPlatform.fromName(social.platformName);
      if (platform != null) {
        socialItems.add(
          _SocialItem(
            platform: platform,
            social: social,
          ),
        );
      }
    }
    if (socialItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      spacing: 10.0,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(context, 'My Links'),
        ...socialItems.map(
          (item) => ProviderScope(
            overrides: [_socialItemProvider.overrideWithValue(item)],
            child: const _SocialItemWidget(),
          ),
        ),
      ],
    );
  }
}

final _socialItemProvider = Provider.autoDispose<_SocialItem>(
  (ref) => throw UnimplementedError(),
);

class _SocialItemWidget extends ConsumerWidget {
  const _SocialItemWidget();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_socialItemProvider);
    final platform = item.platform;
    final social = item.social;
    return RippleTap(
      onTap: () {
        if (ref.read(userRepoProvider) == null) {
          Card3ToastUtil.showToast(message: ToastMessages.joinToAccess);
          return;
        }
        item.launchIfApplicable();
      },
      onLongPress: () {
        if (ref.read(userRepoProvider) == null) {
          Card3ToastUtil.showToast(message: ToastMessages.joinToAccess);
          return;
        }
        copyAndToast(item.formalizedUrl);
      },
      height: 50.0,
      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 2.0),
      borderRadius: BorderRadius.circular(20.0),
      color: context.theme.cardColor,
      child: Row(
        spacing: 10.0,
        children: [
          SocialSvgIcon(
            platform: platform,
            width: 30.0,
            height: 30.0,
            clipOval: false,
            borderRadius: BorderRadius.circular(10.0),
          ),
          Expanded(
            child: Row(
              spacing: 4.0,
              children: [
                Flexible(
                  child: Text.rich(
                    TextSpan(
                      children: [
                        if (platform.addon case final addon? when addon.isNotEmpty)
                          TextSpan(
                            text: '$addon ',
                            style: const TextStyle(
                              color: Colors.grey,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        if (platform.allowHandle)
                          TextSpan(text: item.formalizedHandle)
                        else
                          TextSpan(text: platform.alias ?? platform.name),
                      ],
                    ),
                    style: context.textTheme.headlineSmall,
                    maxLines: 1,
                    overflow: TextOverflow.fade,
                    softWrap: false,
                  ),
                ),
                if (social.imageUrl.isNotEmpty) const Icon(Icons.photo_outlined, size: 24.0),
              ],
            ),
          ),
          if (social.verified) Assets.icons.socialVerified.svg(width: 24, height: 24),
        ],
      ),
    );
  }
}

class _GradientBottom extends StatelessWidget {
  const _GradientBottom({required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 24.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            switch (context.brightness) {
              Brightness.dark => const Color(0x00000000),
              Brightness.light => const Color(0x00ffffff),
            },
            context.theme.scaffoldBackgroundColor,
          ],
          stops: [0, 0.22],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: child,
    );
  }
}

final _relationLoadingProvider = StateProvider.autoDispose<bool>((ref) => false);

class _Relation extends ConsumerStatefulWidget {
  const _Relation();

  @override
  ConsumerState<_Relation> createState() => _RelationState();
}

class _RelationState extends ConsumerState<_Relation> {
  late UserRelation? _userRelation;

  @override
  void initState() {
    super.initState();
    final code = ref.read(_dataProvider).$1;
    final relationResult = ref.read(fetchUserRelationProvider(code: code));
    final result = relationResult.valueOrNull;
    _userRelation = result?.$1;
  }

  Future<void> _toggleFollowing(UserRelation relation) async {
    final (code, _) = ref.read(_dataProvider);
    final result = ref.read(fetchUserRelationProvider(code: code)).valueOrNull;
    final referralCode = result?.$2;
    if (referralCode == null) {
      return;
    }
    ref.read(_relationLoadingProvider.notifier).state = true;
    try {
      final newRelation = await ref
          .read(apiServiceProvider)
          .toggleUserFollow(
            referralCode: referralCode,
            follow: !relation.following,
          );
      safeSetState(() {
        _userRelation = newRelation;
      });
    } finally {
      if (mounted) {
        ref.read(_relationLoadingProvider.notifier).state = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final (code, _) = ref.watch(_dataProvider);
    final result = ref.watch(fetchUserRelationProvider(code: code));
    return _GradientBottom(
      child: AnimatedSize(
        duration: kThemeAnimationDuration,
        curve: Curves.easeInOutCubic,
        child: result.maybeWhen(
          skipLoadingOnRefresh: false,
          data: (data) {
            final relation = _userRelation ?? data?.$1;
            if (relation == null) {
              return const SizedBox.shrink();
            }
            return _buildButton(context, relation);
          },
          orElse: () => _userRelation != null ? _buildButton(context, _userRelation!) : const SizedBox.shrink(),
        ),
      ),
    );
  }

  Widget _buildButton(BuildContext context, UserRelation relation) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        24.0,
        0.0,
        24.0,
        MediaQuery.paddingOf(context).bottom.max(24.0),
      ),
      child: Consumer(
        builder: (context, ref, child) {
          final text = switch (relation) {
            UserRelation(following: true, followedBy: true) => 'Mutual Following',
            UserRelation(following: true, followedBy: _) => 'Following',
            UserRelation(following: false, followedBy: _) => 'Follow',
          };
          if (relation.following) {
            return ThemeTextButton(
              onPressed: () => _toggleFollowing(relation),
              borderSide: BorderSide(color: context.themeColor),
              color: context.theme.scaffoldBackgroundColor,
              child: ref.watch(_relationLoadingProvider) ? const AppLoading() : Text(text),
            );
          }
          return ThemeTextButton(
            onPressed: () => _toggleFollowing(relation),
            child: ref.watch(_relationLoadingProvider) ? const AppLoading() : Text(text),
          );
        },
      ),
    );
  }
}

class _ShareButton extends StatelessWidget {
  const _ShareButton();

  @override
  Widget build(BuildContext context) {
    return _GradientBottom(
      child: ThemeTextButton(
        onPressed: () => ShareDialog.show(context),
        margin: EdgeInsets.fromLTRB(
          24.0,
          0.0,
          24.0,
          MediaQuery.paddingOf(context).bottom.max(24.0),
        ),
        child: Row(
          spacing: 10.0,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Assets.icons.buttonShare.svg(width: 16.0, height: 16.0),
            const Text('Share'),
          ],
        ),
      ),
    );
  }
}

class _JoinButton extends StatelessWidget {
  const _JoinButton();

  @override
  Widget build(BuildContext context) {
    return _GradientBottom(
      child: Padding(
        padding: EdgeInsets.fromLTRB(
          24.0,
          0.0,
          24.0,
          MediaQuery.paddingOf(context).bottom.max(24.0),
        ),
        child: AnimatedColorsBorder(
          animating: true,
          borderRadius: MEUIConfig.themeTextButtonConfig.borderRadius ?? MEUIThemeTextButtonConfig.defaultBorderRadius,
          child: ThemeTextButton(
            onPressed: () => context.navigator.maybePop(),
            text: 'Join Card3 🚀',
          ),
        ),
      ),
    );
  }
}
