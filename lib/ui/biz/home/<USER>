import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/user.dart' show UserFromRelation, UserFromRelationType;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart';
import '/ui/biz/social/profile.dart' show SocialProfilePage;
import '/ui/widgets/open_container.dart';

final _totalResultProvider = FutureProvider.autoDispose.family<int?, UserFromRelationType>(
  (ref, type) async {
    final result = await ref.watch(fetchUsersFromRelationProvider(type: type, page: 1).future);
    return result.total;
  },
);

final connectionsRelationTypeProvider = StateProvider<UserFromRelationType>((ref) {
  return UserFromRelationType.following;
});

class Connections extends ConsumerWidget {
  const Connections({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBlurBackground(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0),
        child: Column(
          children: [
            Gap.topPadding(context, 16.0),
            Row(
              spacing: 8.0,
              children: [
                Expanded(child: _buildTypes(context, ref)),
                Padding(
                  padding: const EdgeInsetsDirectional.only(end: 12.0),
                  child: Tapper(
                    onTap: () => context.navigator.pushNamed(Routes.funReferral.name),
                    child: Column(
                      spacing: 2.0,
                      children: [
                        Icon(
                          Icons.person_add_rounded,
                          size: 32.0,
                          color: context.meTheme.primaryTextColor,
                        ),
                        const Text(
                          'Referral',
                          style: TextStyle(fontSize: 10.0),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const Expanded(child: _List()),
          ],
        ),
      ),
    );
  }

  Widget _buildTypes(BuildContext context, WidgetRef ref) {
    final type = ref.watch(connectionsRelationTypeProvider);
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Row(
        spacing: 18.0,
        children: UserFromRelationType.values.map((e) {
          final total = ref.watch(_totalResultProvider(e));
          return Tapper(
            onTap: () => ref.read(connectionsRelationTypeProvider.notifier).state = e,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(text: e.displayName(context)),
                        if (total.valueOrNull case final total? when total > 0)
                          TextSpan(
                            text: ' ${total.toNumerical(fractionDigits: 1)}',
                            style: const TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                      ],
                    ),
                    style: TextStyle(
                      fontSize: 18.0,
                      color: type == e ? null : context.textTheme.bodySmall?.color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  AnimatedContainer(
                    width: 40.0,
                    height: 4.0,
                    duration: kThemeAnimationDuration,
                    decoration: BoxDecoration(
                      borderRadius: RadiusConstants.max,
                      color: type == e ? context.themeColor : Colors.transparent,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

class _List extends ConsumerStatefulWidget {
  const _List();

  @override
  ConsumerState<_List> createState() => _ListState();
}

class _ListState extends ConsumerState<_List> with AutomaticKeepAliveClientMixin {
  final _pageStorageBucket = PageStorageBucket();

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _userRelationFollowingPatch.clear();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    final type = ref.read(connectionsRelationTypeProvider);
    ref.invalidate(fetchUsersFromRelationProvider);
    await ref.read(
      fetchUsersFromRelationProvider(type: type, page: 1).future,
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final type = ref.watch(connectionsRelationTypeProvider);
    final totalResult = ref.watch(fetchUsersFromRelationProvider(type: type, page: 1));
    final Widget child;
    if (totalResult.valueOrNull?.total == 0) {
      child = CustomScrollView(
        slivers: [
          const SliverGap.v(160.0),
          SliverEmptyView(onTap: _onRefresh, fillRemaining: false),
          const SliverGap.v(12.0),
          SliverToBoxAdapter(
            child: Column(
              spacing: 6.0,
              children: [
                Text('No connections yet', style: context.textTheme.headlineSmall),
                Text(
                  'When someone follows you from your card or joins via your referral, they will show here.',
                  style: context.textTheme.bodySmall?.copyWith(fontSize: 14.0),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SliverGap.v(40.0),
          SliverToBoxAdapter(
            child: ThemeTextButton(
              onPressed: () => context.navigator.pushNamed(Routes.funReferral.name),
              width: 260.0,
              alignment: Alignment.center,
              borderRadius: RadiusConstants.max,
              text: 'Refer a friend',
            ),
          ),
        ],
      );
    } else if (totalResult.hasError && !totalResult.isLoading) {
      final e = totalResult.error;
      child = RefreshableEmptyView(
        onTap: _onRefresh,
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      );
    } else {
      const size = 20;
      child = PageStorage(
        bucket: _pageStorageBucket,
        child: ListView.builder(
          key: PageStorageKey('connection-list-${type.name}-page-storage'),
          padding: const EdgeInsets.symmetric(vertical: 14.0),
          itemCount: totalResult.valueOrNull?.total ?? 4,
          itemBuilder: (context, index) {
            final page = index ~/ size + 1;
            final indexInPage = index % size;
            final result = ref.watch(
              fetchUsersFromRelationProvider(type: type, page: page),
            );
            return result.maybeWhen(
              data: (data) {
                if (indexInPage >= data.list.length) {
                  return null;
                }
                final item = data.list[indexInPage];
                return ProviderScope(
                  overrides: [
                    _userItemProvider.overrideWithValue(item),
                  ],
                  child: const _UserItem(),
                );
              },
              orElse: () => const _UserItemShimmer(),
            );
          },
        ),
      );
    }
    return RefreshIndicator(onRefresh: _onRefresh, child: child);
  }
}

typedef _RelationFamily = (String, UserFromRelationType);

final _userItemProvider = Provider.autoDispose<UserFromRelation>(
  (ref) => throw UnimplementedError(),
);
final _userItemLoadingProvider = StateProvider.autoDispose.family<bool, _RelationFamily>(
  (ref, code) => false,
);
final _userRelationFollowingPatch = <_RelationFamily, bool>{};

class _UserItem extends ConsumerStatefulWidget {
  const _UserItem();

  @override
  ConsumerState<_UserItem> createState() => _UserItemState();
}

class _UserItemState extends ConsumerState<_UserItem> {
  final _containerKey = GlobalKey<OpenContainerState>();

  Future<void> _toggleFollowing(
    BuildContext context,
    WidgetRef ref,
    bool following,
  ) async {
    final type = ref.read(connectionsRelationTypeProvider);
    final item = ref.read(_userItemProvider);
    final family = (item.referralCode, type);
    final provider = _userItemLoadingProvider(family);

    ref.read(provider.notifier).state = true;
    try {
      await ref
          .read(apiServiceProvider)
          .toggleUserFollow(
            referralCode: item.referralCode,
            follow: following,
          );
      _userRelationFollowingPatch[family] = following;
    } finally {
      if (context.mounted) {
        ref.read(provider.notifier).state = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final item = ref.watch(_userItemProvider);
    final type = ref.watch(connectionsRelationTypeProvider);
    final code = item.referralCode;
    final family = (code, type);
    final loading = ref.watch(_userItemLoadingProvider(family));
    final following = _userRelationFollowingPatch[family] ?? item.following;
    return RippleTap(
      onTap: () => _containerKey.currentState?.openContainer(),
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
      child: Row(
        spacing: 10.0,
        children: [
          OpenContainer(
            key: _containerKey,
            openColor: Colors.transparent,
            closedColor: Colors.transparent,
            middleColor: Colors.transparent,
            openElevation: 0.0,
            closedElevation: 0.0,
            openShape: const RoundedRectangleBorder(),
            closedShape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(15.0)),
            ),
            openBuilder: (context, _) => SocialProfilePage(
              code: item.referralCode,
              avatar: item,
            ),
            closedBuilder: (context, _) => UserAvatar(user: item, dimension: 60.0),
          ),
          Expanded(
            child: Column(
              spacing: 4.0,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: TextStyle(
                    fontSize: item.company.isEmpty && item.title.isEmpty ? 24.0 : 18.0,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (item.company.isNotEmpty || item.title.isNotEmpty)
                  Text(
                    () {
                      final buffer = StringBuffer();
                      if (item.company.isNotEmpty) {
                        buffer.write(item.company);
                        if (item.title.isNotEmpty) {
                          buffer.write('\n');
                        }
                      }
                      if (item.title.isNotEmpty) {
                        buffer.write(item.title);
                      }
                      return buffer.toString();
                    }(),
                    style: const TextStyle(color: Colors.grey, fontSize: 14.0),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          AnimatedSwitcher(
            duration: kThemeAnimationDuration,
            switchInCurve: Curves.easeInOutCubic,
            switchOutCurve: Curves.easeInOutCubic,
            child: switch (loading) {
              true => const AppLoading(size: kMinInteractiveDimension),
              false => OutlinedButton(
                onPressed: () => _toggleFollowing(context, ref, !following),
                style: OutlinedButton.styleFrom(
                  backgroundColor: following ? null : Colors.white,
                  shape: const RoundedRectangleBorder(
                    borderRadius: RadiusConstants.max,
                  ),
                  side: BorderSide(color: context.meTheme.captionTextColor),
                ),
                child: Text(
                  switch (type) {
                    UserFromRelationType.following => following ? 'Following' : 'Follow',
                    UserFromRelationType.follower => following ? 'Mutuals' : 'Follow Back',
                  },
                  style: TextStyle(
                    color: following ? context.textTheme.bodyMedium?.color : Colors.black,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
            },
          ),
        ],
      ),
    );
  }
}

class _UserItemShimmer extends StatelessWidget {
  const _UserItemShimmer();

  @override
  Widget build(BuildContext context) {
    return MEShimmer(
      child: Container(
        height: 60.0,
        margin: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
        child: Row(
          spacing: 10.0,
          children: [
            AspectRatio(
              aspectRatio: 1.0,
              child: CircleAvatar(backgroundColor: context.theme.dividerColor),
            ),
            Expanded(
              child: Column(
                spacing: 8.0,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 160.0,
                    height: 18.0,
                    color: context.theme.cardColor,
                  ),
                  Flexible(
                    child: Column(
                      spacing: 2.0,
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 60.0,
                          height: 14.0,
                          color: context.theme.cardColor,
                        ),
                        Container(
                          width: 30.0,
                          height: 14.0,
                          color: context.theme.cardColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: 84.0,
              height: 36.0,
              decoration: BoxDecoration(
                borderRadius: RadiusConstants.max,
                color: context.theme.cardColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
