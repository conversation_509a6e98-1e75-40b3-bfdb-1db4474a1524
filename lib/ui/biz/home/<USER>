import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:me_misc/me_misc.dart' show meRouteObserver;

import '/feat/nfc/helper.dart';
import '/feat/video/player.dart';
import '/models/card.dart' show SocialPlatform, Social;
import '/models/user.dart' show UserInfo;
import '/provider/card.dart'
    show fetchMyCardsProvider, fetchExtendProfileProvider, fetchSocialsProvider, isActiveGuideProvider;
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;
import '/ui/biz/other/share.dart' show ShareDialog;
import '/ui/biz/social/profile.dart' show SocialProfilePage;
import '/ui/widgets/open_container.dart';
import '/ui/widgets/social/data.dart' show SocialSvgIcon;

class Cards extends ConsumerStatefulWidget {
  const Cards({super.key});

  @override
  ConsumerState<Cards> createState() => _CardState();
}

class _CardState extends ConsumerState<Cards> with RouteAware {
  bool _hasShownActivationGuide = false;

  @override
  void initState() {
    super.initState();
    _checkActivationGuide();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    meRouteObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void didPopNext() {
    _checkActivationGuide();
  }

  @override
  void dispose() {
    meRouteObserver.unsubscribe(this);
    super.dispose();
  }

  // 检查是否需要显示激活引导
  void _checkActivationGuide() {
    if (_hasShownActivationGuide || !mounted) {
      return;
    }

    final cards = ref.read(fetchMyCardsProvider).valueOrNull;
    final shouldShowGuide = ref.read(isActiveGuideProvider);

    // 只有当cards数据加载完成且需要显示引导时才显示
    if (shouldShowGuide == true && cards != null && !_hasShownActivationGuide) {
      _hasShownActivationGuide = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _showActivationGuideSheet();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppBlurBackground(
      child: RefreshIndicator(
        onRefresh: () => _refreshData(ref),
        child: const CustomScrollView(
          physics: AlwaysScrollableScrollPhysics(),
          slivers: [
            SliverFillRemaining(
              hasScrollBody: false,
              fillOverscroll: false,
              child: Column(
                children: [
                  _HeaderNew(),
                  Expanded(child: _MainBodyNew()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showActivationGuideSheet() {
    if (!mounted) {
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => const _ActivationGuideSheet(),
    );
  }
}

class _ActivationGuideSheet extends StatelessWidget {
  const _ActivationGuideSheet();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0).copyWith(bottom: 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Got a Card3 NFC item?',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 60.0),
            child: Assets.icons.images.activateGuide.svg(width: 200, height: 212),
          ),
          ThemeTextButton(
            onPressed: () {
              Future.delayed(const Duration(milliseconds: 300), () {
                NfcHelper.startPollingManually();
              });
              Navigator.pop(context);
            },
            text: 'Activate Now',
          ),
          Gap.v(MediaQuery.paddingOf(context).bottom.max(24.0)),
        ],
      ),
    );
  }
}

Future<void> _refreshData(WidgetRef ref) async {
  ref.invalidate(fetchUserInfoProvider());
  ref.invalidate(fetchMyCardsProvider);
  ref.invalidate(fetchExtendProfileProvider());
  ref.invalidate(fetchSocialsProvider());
  await Future.wait([
    ref.read(fetchUserInfoProvider().future),
    ref.read(fetchMyCardsProvider.future),
    ref.read(fetchExtendProfileProvider().future),
    ref.read(fetchSocialsProvider().future),
  ]);
}

class _HeaderNew extends ConsumerWidget {
  const _HeaderNew();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0).copyWith(
        top: MediaQuery.paddingOf(context).top + 20.0,
        bottom: 8.0,
      ),
      child: Row(
        spacing: 20.0,
        children: [
          Stack(
            clipBehavior: Clip.none,
            children: [
              Tapper(
                onTap: () {
                  meNavigator.pushNamed(Routes.notification.name);
                },
                child: Assets.icons.homeMessages.svg(
                  height: 36.0,
                  colorFilter: context.meTheme.primaryTextColor.filter,
                ),
              ),
              if (userInfo case final user? when user.lastMessageId != user.latestMessageId)
                Positioned(
                  right: -2,
                  top: -2,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
            ],
          ),
          const Spacer(),
          Tapper(
            onTap: () {
              NfcHelper.startPollingManually();
            },
            child: Assets.icons.homeCardNfc.svg(
              height: 36.0,
              colorFilter: context.meTheme.primaryTextColor.filter,
            ),
          ),
        ],
      ),
    );
  }
}

class _MainBodyNew extends ConsumerWidget {
  const _MainBodyNew();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Column(
      spacing: 20.0,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Flexible(
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: _ProfileCardNew(),
          ),
        ),
        _ShareButton(),
      ],
    );
  }
}

class _ProfileCardNew extends ConsumerStatefulWidget {
  const _ProfileCardNew();

  @override
  ConsumerState<_ProfileCardNew> createState() => _ProfileCardNewState();
}

class _ProfileCardNewState extends ConsumerState<_ProfileCardNew> {
  final _containerKey = GlobalKey<OpenContainerState>();

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final textTheme = theme.textTheme;

    final userInfo = ref.watch(userRepoProvider);
    final socialsResult = ref.watch(fetchSocialsProvider());
    return RippleTap(
      onTap: () => _containerKey.currentState?.openContainer(),
      clipBehavior: Clip.antiAlias,
      width: 300.0,
      height: 470.0,
      borderRadius: BorderRadius.circular(40.0),
      color: theme.cardColor,
      child: Column(
        children: [
          _buildAvatar(context, userInfo),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      userInfo?.name.or('Name') ?? 'Name',
                      style: textTheme.headlineMedium?.copyWith(
                        color: switch (userInfo?.name) {
                          final n? when n.isNotEmpty => null,
                          _ => Colors.grey,
                        },
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                const Gap.v(10.0),
                if (userInfo?.title case final title? when title.isNotEmpty)
                  Flexible(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        title,
                        style: const TextStyle(fontSize: 16),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ),
                if (userInfo?.company case final company? when company.isNotEmpty)
                  Flexible(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        company,
                        style: const TextStyle(fontSize: 16),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ),
                AnimatedSize(
                  duration: kThemeAnimationDuration,
                  curve: Curves.easeOutCubic,
                  child: switch (socialsResult.valueOrNull) {
                    final socials? when socials.isNotEmpty => _buildSocials(context, socials),
                    _ => const SizedBox(width: double.infinity),
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context, UserInfo? userInfo) {
    final theme = context.theme;
    return AspectRatio(
      aspectRatio: 1.0,
      child: Stack(
        children: [
          OpenContainer(
            key: _containerKey,
            openColor: Colors.transparent,
            closedColor: Colors.transparent,
            middleColor: Colors.transparent,
            openElevation: 0.0,
            closedElevation: 0.0,
            openShape: const RoundedRectangleBorder(),
            closedShape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(40.0)),
            ),
            openBuilder: (context, _) => SocialProfilePage(
              code: userInfo!.referralCode,
              profile: userInfo,
            ),
            closedBuilder: (context, _) => const _UserAvatar(),
          ),
          PositionedDirectional(
            top: 16.0,
            end: 16.0,
            child: RippleTap(
              onTap: () => context.navigator.pushNamed(Routes.socialEditProfile.name),
              padding: const EdgeInsets.all(10.0),
              shape: const CircleBorder(),
              color: theme.scaffoldBackgroundColor.withValues(alpha: 0.5),
              child: Assets.icons.buttonEdit.svg(
                width: 16.0,
                height: 16.0,
                colorFilter: theme.textTheme.bodyMedium?.color?.filter,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocials(BuildContext context, List<Social> socials) {
    final theme = context.theme;
    return Container(
      height: 26.0,
      margin: const EdgeInsets.only(top: 26.0),
      child: DecoratedBox(
        position: DecorationPosition.foreground,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              theme.cardColor,
              theme.cardColor.withAlpha(0),
              theme.cardColor.withAlpha(0),
              theme.cardColor,
            ],
            stops: [0.0, 0.12, 0.88, 1.0],
            begin: AlignmentDirectional.centerStart,
            end: AlignmentDirectional.centerEnd,
          ),
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Row(
            spacing: 10.0,
            children: socials
                .map((e) => SocialPlatform.fromName(e.platformName))
                .nonNulls
                .map(
                  (social) => SocialSvgIcon(
                    platform: social,
                    width: 26.0,
                    height: 26.0,
                    borderRadius: BorderRadius.circular(10.0),
                    clipOval: false,
                  ),
                )
                .toList(),
          ),
        ),
      ),
    );
  }
}

class _ShareButton extends StatelessWidget {
  const _ShareButton();

  @override
  Widget build(BuildContext context) {
    return ThemeTextButton(
      onPressed: () => ShareDialog.show(context),
      width: 260.0,
      alignment: Alignment.center,
      borderRadius: RadiusConstants.max,
      child: Row(
        spacing: 10.0,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Assets.icons.buttonShare.svg(width: 16.0, height: 16.0),
          const Text('Share'),
        ],
      ),
    );
  }
}

class _UserAvatar extends ConsumerWidget {
  const _UserAvatar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userInfo = ref.watch(userRepoProvider);
    Widget child = UserAvatar(
      user: userInfo,
      dimension: MediaQuery.sizeOf(context).width,
      borderRadius: const BorderRadius.vertical(top: Radius.circular(40.0)),
      placeholderPadding: const EdgeInsets.all(10.0),
    );
    if (userInfo?.dynamicAvatar case final dynamicAvatar? when dynamicAvatar.isNotEmpty) {
      child = Stack(
        fit: StackFit.expand,
        children: [
          child,
          AppNetworkVideoPlayer(url: dynamicAvatar, pauseWhenInvisible: true),
        ],
      );
    }
    return child;
  }
}
