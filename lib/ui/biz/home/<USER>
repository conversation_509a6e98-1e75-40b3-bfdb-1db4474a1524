import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/provider/settings.dart' show selectedIndexProvider;
import '/ui/biz/social/profile.dart';
import '/ui/widgets/open_container.dart';

class Settings extends ConsumerWidget {
  const Settings({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final meTheme = theme.extension<METheme>()!;

    return CustomScrollView(
      slivers: [
        SliverGap.topPadding(context),
        const SliverPadding(
          padding: EdgeInsets.symmetric(horizontal: 24.0, vertical: 24.0),
          sliver: SliverToBoxAdapter(
            child: _UserBar(),
          ),
        ),
        const SliverToBoxAdapter(child: Divider(thickness: 1.25, indent: 24.0, endIndent: 24.0)),
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 24.0),
          sliver: SliverList.list(
            children: [
              _buildSettingItem(
                context,
                icon: Assets.icons.setting.wallet.svg(
                  colorFilter: meTheme.themeColor.filter,
                ),
                title: 'Go Web3',
                onTap: () => AppLoading.run(
                  until: 1.seconds,
                  () async {
                    final chainManager = ChainManager.instance;
                    if (chainManager.initializing) {
                      await chainManager.initialize();
                    }
                    final state = await privyClient.getAuthState();
                    final user = state.user;
                    if (chainManager.initialized) {
                      meNavigator.pushNamed(Routes.walletPortfolio.name);
                    } else if (user != null) {
                      meNavigator.pushNamed(Routes.walletAuthenticate.name);
                    } else {
                      meNavigator.pushNamed(Routes.walletManagement.name);
                    }
                  },
                ),
              ),
              _buildSettingItem(
                context,
                icon: Assets.icons.setting.personShield.svg(
                  colorFilter: meTheme.themeColor.filter,
                ),
                title: 'Account Security',
                onTap: () {
                  meNavigator.pushNamed(Routes.settingAccount.name);
                },
              ),
              _buildSettingItem(
                context,
                icon: Assets.icons.setting.myCards.svg(
                  colorFilter: meTheme.themeColor.filter,
                ),
                title: 'My Card3 Collections',
                onTap: () {
                  meNavigator.pushNamed(Routes.settingCardsCollection.name);
                },
              ),
              _buildSettingItem(
                context,
                icon: Assets.icons.setting.printCards.svg(
                  colorFilter: meTheme.themeColor.filter,
                ),
                title: 'Card Printing Orders',
                onTap: () {
                  meNavigator.pushNamed(Routes.customizePrints.name);
                },
              ),
              if (!isSealed) ...[
                _buildSettingItem(
                  context,
                  icon: FittedBox(
                    child: Icon(
                      Icons.text_snippet,
                      color: meTheme.themeColor,
                    ),
                  ),
                  title: 'WebView test',
                  onTap: () async {
                    String url = '';
                    final result = await TinyDialog.show(
                      text: 'Enter URL',
                      childrenBuilder: (context) => [
                        TextField(
                          autofocus: true,
                          autofillHints: [AutofillHints.url],
                          onChanged: (value) => url = value,
                          onSubmitted: (value) => Navigator.of(context).maybePop(true),
                        ),
                      ],
                      buttonsBuilder: (context) => const TinyDialogButtonGroup(),
                    );
                    if (result != true || url.isEmpty) {
                      return;
                    }
                    meNavigator.pushNamed(
                      Routes.webview.name,
                      arguments: Routes.webview.d(url: url, title: 'WebView'),
                    );
                  },
                ),
              ],
              _buildSettingItem(
                context,
                icon: Assets.icons.setting.about.svg(
                  colorFilter: Colors.grey.filter,
                ),
                title: 'About Card3',
                onTap: () {
                  meNavigator.pushNamed(Routes.settingAbout.name);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSettingItem(
    BuildContext context, {
    required Widget icon,
    required String title,
    required VoidCallback onTap,
    Widget? iconWidget,
  }) {
    return RippleTap(
      onTap: onTap,
      margin: const EdgeInsets.only(bottom: 10.0),
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
      borderRadius: BorderRadius.circular(20.0),
      color: context.colorScheme.surface,
      child: Row(
        children: [
          iconWidget ?? SizedBox.square(dimension: 24.0, child: icon),
          const SizedBox(width: 16),
          Expanded(
            child: AutoSizeText(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              maxLines: 1,
            ),
          ),
          const Icon(Icons.arrow_forward_ios, size: 20),
        ],
      ),
    );
  }
}

class _UserBar extends ConsumerStatefulWidget {
  const _UserBar();

  @override
  ConsumerState<_UserBar> createState() => _UserBarState();
}

class _UserBarState extends ConsumerState<_UserBar> {
  final _containerKey = GlobalKey<OpenContainerState>();

  Future<void> _logout() async {
    final result = await TinyDialog.show(
      text: 'Are you sure you want to logout?',
      buttonsBuilder: (context) => const TinyDialogButtonGroup(),
    );
    if (result != true) {
      return;
    }
    ChainManager.instance.dispose();
    privyClient.logout();
    await meNavigator.removeNamedAndPushAndRemoveUntil(
      Routes.login.name,
      predicate: (_) => false,
    );
    globalContainer.read(userRepoProvider.notifier).reset();
    globalContainer.read(selectedIndexProvider.notifier).state = 0;
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userRepoProvider);

    final theme = context.theme;
    final textTheme = theme.textTheme;
    return Tapper(
      onTap: () => _containerKey.currentState?.openContainer(),
      child: SizedBox(
        height: 80.0,
        child: Row(
          spacing: 10.0,
          children: [
            OpenContainer(
              key: _containerKey,
              openColor: Colors.transparent,
              closedColor: Colors.transparent,
              middleColor: Colors.transparent,
              openElevation: 0.0,
              closedElevation: 0.0,
              openShape: const RoundedRectangleBorder(),
              closedShape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(20.0)),
              ),
              openBuilder: (context, _) => SocialProfilePage(
                code: user!.referralCode,
                profile: user,
              ),
              closedBuilder: (context, _) => UserAvatar(
                user: user,
                dimension: 80.0,
                borderRadius: const BorderRadius.all(Radius.circular(20.0)),
              ),
            ),
            Expanded(
              child: Column(
                spacing: 2.0,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user?.name.or('Name') ?? 'Name',
                    style: textTheme.headlineSmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Flexible(
                    child: AutoSizeText(
                      user?.userEmail ?? 'Email',
                      style: textTheme.bodySmall?.copyWith(fontSize: 16.0),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            RippleTap(
              onTap: _logout,
              padding: const EdgeInsets.all(10.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
                side: BorderSide(color: context.theme.dividerColor),
              ),
              child: Assets.icons.buttonLogout.svg(width: 20.0, height: 20.0),
            ),
          ],
        ),
      ),
    );
  }
}
