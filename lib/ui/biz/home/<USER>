import 'dart:ui' as ui;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:marqueer/marqueer.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '/models/business.dart' show EventItem, EventItemDisplayEnv, EventItemType;
import '/models/card.dart' show CardInfo;
import '/provider/business.dart' show fetchDiscoveryEventsProvider;
import '/provider/card.dart';
import '/provider/user.dart';
import '/ui/widgets/animated_colors_border.dart';

class _EventCategory {
  const _EventCategory({
    required this.title,
    required this.items,
  });

  final String title;
  final List<EventItem> items;
}

final _listProvider = FutureProvider.autoDispose<Map<EventItemType, List<_EventCategory>>>((ref) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });

  late final List<EventItem> events;
  late final List<CardInfo> cards;
  await Future.wait([
    ref.watch(fetchDiscoveryEventsProvider.future).then((r) => events = r),
    ref.watch(fetchMyCardsProvider.future).then((r) => cards = r),
  ]);

  final cardEventIds = cards.map((e) => e.eventId);

  final groupedRegulars = <String, List<EventItem>>{};
  final groupedBanners = <String, List<EventItem>>{};

  for (final event in events) {
    final eventType = event.type;
    if (eventType == EventItemType.unknown) {
      continue;
    }

    void add() {
      if (event.eventIds.isEmpty || event.eventIds.any(cardEventIds.contains)) {
        final category = event.category.or('Other');
        switch (eventType) {
          case EventItemType.unknown:
            break;
          case EventItemType.regular:
            groupedRegulars.putIfAbsent(category, () => <EventItem>[]).add(event);
          case EventItemType.banner:
            groupedBanners.putIfAbsent(category, () => <EventItem>[]).add(event);
        }
      }
    }

    if (!isSealed) {
      add();
      continue;
    }

    if (event.displayEnvs case final envs when envs.isNotEmpty) {
      if (isAuditing) {
        if (envs.contains(EventItemDisplayEnv.auditing)) {
          add();
        }
        continue;
      }

      if (envs.contains(EventItemDisplayEnv.production)) {
        add();
        continue;
      }
    }
  }

  final categories = {
    EventItemType.regular: groupedRegulars.entries.map<_EventCategory>((entry) {
      return _EventCategory(title: entry.key, items: entry.value);
    }).toList(),
    EventItemType.banner: groupedBanners.entries.map<_EventCategory>((entry) {
      return _EventCategory(title: entry.key, items: entry.value);
    }).toList(),
  };

  return categories;
});

class Fun extends ConsumerStatefulWidget {
  const Fun({super.key});

  @override
  ConsumerState<Fun> createState() => _FunState();
}

class _FunState extends ConsumerState<Fun> {
  final _refreshKey = GlobalKey<RefreshIndicatorState>();

  Future<void> _onRefresh() async {
    ref.invalidate(fetchUserInfoProvider);
    ref.invalidate(fetchMyCardsProvider);
    ref.invalidate(fetchDiscoveryEventsProvider);
    ref.invalidate(_listProvider);
    await Future.wait([
      ref.read(fetchUserInfoProvider().future),
      ref.read(_listProvider.future),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final result = ref.watch(_listProvider);
    return AppScaffold(
      withBlurBackground: true,
      bodyPadding: EdgeInsets.zero,
      body: RefreshIndicator(
        key: _refreshKey,
        onRefresh: _onRefresh,
        displacement: MediaQuery.paddingOf(context).top,
        child: CustomScrollView(
          clipBehavior: Clip.none,
          slivers: [
            SliverGap.topPadding(context),
            if (result.valueOrNull case final data?) ...[
              ...data[EventItemType.banner]!.map(
                (category) => ProviderScope(
                  overrides: [_categoryItemProvider.overrideWithValue(category)],
                  child: const _BannerCategoryItem(),
                ),
              ),
              ...data[EventItemType.regular]!.map(
                (category) => ProviderScope(
                  overrides: [_categoryItemProvider.overrideWithValue(category)],
                  child: const _RegularCategoryItem(),
                ),
              ),
            ] else if (result.isLoading)
              const SliverToBoxAdapter(child: AppLoading())
            else
              SliverEmptyView(
                onTap: () {
                  if (result.hasValue) {
                    _refreshKey.currentState?.show();
                  } else {
                    _onRefresh();
                  }
                },
                message: switch (result.error) {
                  final e when isNetworkError(e) => context.l10nME.networkError,
                  _ => context.l10nME.clickToRetryButton,
                },
              ),
            const SliverGap.v(24.0),
          ],
        ),
      ),
    );
  }
}

final _categoryItemProvider = Provider.autoDispose<_EventCategory>(
  (ref) => throw UnimplementedError(),
);

class _BannerCategoryItem extends ConsumerStatefulWidget {
  const _BannerCategoryItem();

  @override
  ConsumerState<_BannerCategoryItem> createState() => _BannerCategoryItemState();
}

class _BannerCategoryItemState extends ConsumerState<_BannerCategoryItem> {
  late final _pageController = PageController()..addListener(_pageListener);
  late final _pageNotifier = ValueNotifier<double>(0.0);

  @override
  void dispose() {
    _pageController.dispose();
    _pageNotifier.dispose();
    super.dispose();
  }

  void _pageListener() {
    if (mounted && _pageController.hasClients && _pageController.positions.isNotEmpty && _pageController.page != null) {
      _pageNotifier.value = _pageController.page!;
    }
  }

  @override
  Widget build(BuildContext context) {
    final category = ref.watch(_categoryItemProvider);
    final themeColor = context.themeColor;
    return MultiSliver(
      pushPinnedChildren: true,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Padding(
            padding: const EdgeInsets.only(top: 24.0),
            child: Text(
              category.title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ),
        const SliverGap.v(8.0),
        AspectRatio(
          aspectRatio: 3.0,
          child: PageView(
            controller: _pageController,
            clipBehavior: Clip.none,
            scrollDirection: Axis.horizontal,
            children: category.items
                .map(
                  (event) => ProviderScope(
                    overrides: [_eventItemProvider.overrideWithValue(event)],
                    child: const _BannerEventItem(),
                  ),
                )
                .toList(),
          ),
        ),
        if (category.items.length case final length when length > 1)
          SliverPadding(
            padding: const EdgeInsets.only(top: 8.0),
            sliver: SliverToBoxAdapter(
              child: ValueListenableBuilder(
                valueListenable: _pageNotifier,
                builder: (context, page, _) => Row(
                  spacing: 4.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    length,
                    (index) {
                      final lerp = (page - index).abs();
                      return Container(
                        width: 4.0 + 8.0 * (1 - lerp),
                        height: 4.0,
                        decoration: BoxDecoration(
                          borderRadius: RadiusConstants.max,
                          color: Color.lerp(themeColor, Colors.white12, lerp),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

class _RegularCategoryItem extends ConsumerWidget {
  const _RegularCategoryItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final category = ref.watch(_categoryItemProvider);
    return MultiSliver(
      pushPinnedChildren: true,
      children: [
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          sliver: SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.only(top: 24.0),
              child: Text(
                category.title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
        const SliverGap.v(8.0),
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 10.0,
              mainAxisSpacing: 10.0,
              childAspectRatio: 89 / 65,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final item = category.items[index];
                return ProviderScope(
                  overrides: [_eventItemProvider.overrideWithValue(item)],
                  child: _RegularEventItem(key: ValueKey(item.hashCode)),
                );
              },
              childCount: category.items.length,
            ),
          ),
        ),
      ],
    );
  }
}

final _eventItemProvider = Provider.autoDispose<EventItem>(
  (ref) => throw UnimplementedError(),
);

class _RegularEventItem extends ConsumerStatefulWidget {
  const _RegularEventItem({super.key});

  @override
  ConsumerState<_RegularEventItem> createState() => _RegularEventItemState();
}

class _RegularEventItemState extends ConsumerState<_RegularEventItem> {
  final _titleController = MarqueerController();
  final _descController = MarqueerController();

  @override
  Widget build(BuildContext context) {
    final textScaler = MediaQuery.textScalerOf(context);
    final event = ref.watch(_eventItemProvider);
    final borderRadius = BorderRadius.circular(20.0);
    Widget child = RippleTap(
      onTap: event.open,
      color: context.meTheme.primaryTextColor.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius,
        side: event.highlighted ? BorderSide.none : BorderSide(color: context.theme.dividerColor),
      ),
      child: Stack(
        fit: StackFit.expand,
        clipBehavior: Clip.none,
        children: [
          ImageFiltered(
            imageFilter: ui.ImageFilter.blur(sigmaX: 45.0, sigmaY: 45.0),
            child: FractionallySizedBox(
              widthFactor: 0.6,
              heightFactor: 0.6,
              alignment: const AlignmentDirectional(1.5, 0.0),
              child: MEImage(event.image, fit: BoxFit.contain),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Stack(
              children: [
                Positioned.fill(
                  top: null,
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxHeight: 50.0),
                    child: MEImage(
                      event.image,
                      fit: BoxFit.contain,
                      alignment: AlignmentDirectional.bottomEnd,
                    ),
                  ),
                ),
                Column(
                  spacing: 4.0,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: textScaler.scale(22.0),
                      transform: Transform.translate(offset: const Offset(0, -3.0)).transform,
                      child: Marqueer(
                        controller: _titleController,
                        pps: textScaler.scale(20.0),
                        padding: const EdgeInsets.only(right: 8.0),
                        infinity: false,
                        direction: MarqueerDirection.rtl,
                        edgeDuration: const Duration(seconds: 2),
                        interaction: true,
                        restartAfterInteractionDuration: const Duration(seconds: 3),
                        restartAfterInteraction: true,
                        child: Text(
                          event.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      height: textScaler.scale(18.0),
                      transform: Transform.translate(offset: const Offset(0, -6.0)).transform,
                      child: Marqueer(
                        controller: _descController,
                        pps: textScaler.scale(16.0),
                        padding: const EdgeInsets.only(right: 8.0),
                        infinity: false,
                        direction: MarqueerDirection.rtl,
                        edgeDuration: const Duration(seconds: 2),
                        interaction: true,
                        restartAfterInteractionDuration: const Duration(seconds: 3),
                        restartAfterInteraction: true,
                        child: Text(
                          event.desc,
                          style: const TextStyle(color: Colors.grey),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
    if (event.highlighted) {
      child = AnimatedColorsBorder(
        animating: true,
        borderRadius: borderRadius,
        width: 3.0,
        child: child,
      );
    }
    return child;
  }
}

class _BannerEventItem extends ConsumerWidget {
  const _BannerEventItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final event = ref.watch(_eventItemProvider);
    final theme = context.theme;
    final borderRadius = BorderRadius.circular(16.0);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: CustomPaint(
        painter: _BorderPainter(
          themeColor: theme.primaryColor,
          borderRadius: borderRadius,
          borderWidth: 1.5,
        ),
        child: RippleTap(
          onTap: event.open,
          padding: const EdgeInsets.all(16.0),
          borderRadius: borderRadius,
          child: Row(
            spacing: 10.0,
            children: [
              Expanded(
                child: Column(
                  spacing: 6.0,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: PlaceholderText(
                        event.title,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.normal,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      constraints: const BoxConstraints(minWidth: 90.0),
                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                      decoration: BoxDecoration(
                        borderRadius: RadiusConstants.max,
                        color: theme.primaryColor,
                      ),
                      child: Text(
                        event.buttonText ?? 'Visit',
                        style: const TextStyle(fontSize: 14.0, fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              ConstrainedBox(
                constraints: const BoxConstraints(maxHeight: 75.0),
                child: MEImage(event.image, fit: BoxFit.contain),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

extension on EventItem {
  void open() {
    if (isNativeLink) {
      if (routeNames.contains(link)) {
        meNavigator.pushNamed(link);
      } else {
        Card3ToastUtil.showToast(message: 'No matched event: $link');
      }
    } else {
      final String url;
      if (Uri.tryParse(link) case final u?) {
        if (u.scheme.isEmpty || u.host.isEmpty) {
          url = '$envUrlWebsite$link';
        } else {
          url = link;
        }
      } else {
        url = '$envUrlWebsite$link';
      }
      meNavigator.pushNamed(
        Routes.webview.name,
        arguments: Routes.webview.d(url: url, title: title),
      );
    }
  }
}

class _BorderPainter extends CustomPainter {
  const _BorderPainter({
    required this.themeColor,
    required this.borderWidth,
    this.borderRadius,
  });

  final Color themeColor;
  final double borderWidth;
  final BorderRadius? borderRadius;

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final path = Path();
    if (borderRadius case final br?) {
      path.addRRect(
        RRect.fromRectAndCorners(
          rect,
          topLeft: br.topLeft,
          topRight: br.topRight,
          bottomLeft: br.bottomLeft,
          bottomRight: br.bottomRight,
        ),
      );
    } else {
      path.addRect(rect);
    }

    final gradient = LinearGradient(
      begin: const Alignment(-0.1, -1.0),
      end: const Alignment(0.1, 1.0),
      colors: [const Color(0xffff41b3), themeColor],
    );

    final paint = Paint()
      ..shader = gradient.createShader(rect)
      ..strokeWidth = borderWidth
      ..style = PaintingStyle.stroke;

    for (final metric in path.computeMetrics()) {
      final extractPath = metric.extractPath(0, metric.length);
      canvas.drawPath(extractPath, paint);
    }
  }

  @override
  bool shouldRepaint(_BorderPainter oldDelegate) => false;
}
