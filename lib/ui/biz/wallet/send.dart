import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/business.dart';
import '/provider/chain.dart';
import '/provider/token.dart';
import '/services/select_token.dart';

final _decimalsProvider = FutureProvider.autoDispose.family<int, IToken>((ref, token) {
  return ChainManager.instance.getTokenDecimals(token);
});

@FFRoute(name: '/wallet/send')
class SendPage extends ConsumerStatefulWidget {
  const SendPage({
    super.key,
    required this.token,
  });

  final IToken token;

  @override
  ConsumerState<SendPage> createState() => _SendPageState();
}

class _SendPageState extends ConsumerState<SendPage> {
  late IToken _selectedToken = widget.token;

  final _addressController = TextEditingController();
  final _amountController = TextEditingController();

  String _errorMessage = '';
  String? _txHash;
  bool _showSuccessView = false;

  @override
  void dispose() {
    _addressController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _handleSend() async {
    // 清除之前的错误
    setState(() {
      _errorMessage = '';
    });

    final address = _addressController.text.trim();
    if (address.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter the receiving address';
      });
      return;
    }
    final amount = Decimal.tryParse(_amountController.text);
    if (amount == null || amount <= Decimal.zero) {
      setState(() {
        _errorMessage = 'Please enter a valid amount';
      });
      return;
    }

    return AppLoading.run(() async {
      try {
        await _sendTransaction(address, amount);
        _showSuccessView = true;
      } catch (e) {
        _errorMessage = e.toString();
        rethrow;
      } finally {
        safeSetState(() {});
      }
    });
  }

  Future<void> _sendTransaction(String address, Decimal amount) async {
    try {
      final chainManager = ChainManager.instance;
      final token = _selectedToken;

      final decimalsProvider = _decimalsProvider(token);
      final int decimals;
      if (token.decimals case final d?) {
        decimals = d;
      } else if (ref.read(decimalsProvider).hasError) {
        decimals = await ref.refresh(decimalsProvider.future);
      } else {
        decimals = await ref.read(decimalsProvider.future);
      }

      final amountAfterShifted = (amount * Decimal.ten.pow(decimals).toDecimal()).toBigInt();

      _txHash = await chainManager.sendTransaction(
        token: token,
        to: address,
        value: amountAfterShifted,
      );

      LogUtil.d('Transaction sent: $_txHash');

      refreshTokenBalances(ref);
    } catch (e) {
      safeSetState(() {
        _errorMessage = 'Transaction failed: ${e.toString()}';
      });
      rethrow;
    }
  }

  void _checkStatus() {
    if (_txHash case final tx? when tx.isNotEmpty) {
      final currentNetwork = ref.read(networkProvider);
      if (currentNetwork.blockExplorers.isNotEmpty) {
        final explorerUrl = '${currentNetwork.blockExplorers.first.url}/tx/$_txHash';
        launchUrlString(explorerUrl);
      }
    }
  }

  void _succeedAndBackToWallet() {
    refreshTokenBalances(ref);
    Navigator.of(context).popUntil(
      (r) => r.settings.name == Routes.walletPortfolio.name || r.settings.name == Routes.home.name,
    );
  }

  @override
  Widget build(BuildContext context) {
    final selectedToken = _selectedToken;
    final displayBalance = selectedToken.realBalance.toString();
    final tokens = ref.watch(fetchWalletPortfolioProvider).valueOrNull?.tokens;
    final decimalsResult = ref.watch(_decimalsProvider(selectedToken));

    if (_showSuccessView) {
      return _buildSuccessView();
    }

    return AppScaffold(
      body: GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: ListView(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          children: [
            Row(
              children: [
                const Expanded(
                  child: Text(
                    'To',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                RippleTap(
                  onTap: () async {
                    final result = await Navigator.of(context).pushNamed(
                      Routes.scan.name,
                      arguments: Routes.scan.d(
                        manager: ScanManager(
                          {
                            SolanaAddressScanHandler(),
                            EthereumAddressScanHandler(),
                          },
                        ),
                      ),
                    );
                    if (result is QR) {
                      _addressController.text = result.value.payload;
                    }
                  },
                  onLongPress: () {
                    _addressController.text = ChainManager.instance.walletAddress!;
                  },
                  padding: const EdgeInsets.all(12.0),
                  borderRadius: BorderRadius.circular(8.0),
                  child: Assets.icons.scan.scanner.svg(width: 24, height: 24),
                ),
              ],
            ),
            DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey.shade300, width: 1),
              ),
              child: TextField(
                controller: _addressController,
                maxLines: 2,
                decoration: InputDecoration(
                  filled: true,
                  fillColor: context.theme.cardColor,
                  hintText: 'Enter Receiving Address',
                  hintStyle: const TextStyle(color: Colors.grey, fontSize: 16),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 50),

            DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.0),
                color: context.themeColor,
              ),
              child: Column(
                children: [
                  GestureDetector(
                    onTap: () => showTokenSelection(context, tokens, (tokenBalance) {
                      setState(() {
                        _selectedToken = tokenBalance;
                        _amountController.clear();
                      });
                    }),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      child: Row(
                        spacing: 6.0,
                        children: [
                          Container(
                            width: 30,
                            height: 30,
                            decoration: BoxDecoration(
                              color: context.theme.scaffoldBackgroundColor,
                              shape: BoxShape.circle,
                            ),
                            child: MEImage(
                              selectedToken.logo,
                              width: 30.0,
                              height: 30.0,
                              clipOval: true,
                              emptyBuilder: (context) => Center(
                                child: FittedBox(
                                  child: Text(
                                    selectedToken.symbol.substring(0, 1).toUpperCase(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Text(
                              selectedToken.symbol,
                              style: context.textTheme.headlineSmall,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.all(2.0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.0),
                      color: context.theme.cardColor,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _amountController,
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            style: const TextStyle(
                              fontFamily: FontFamily.mMMMono,
                              fontSize: 48,
                              height: 1.0,
                            ),
                            decoration: const InputDecoration(
                              hintText: '0',
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 10,
                              ),
                              filled: false,
                              border: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              errorBorder: InputBorder.none,
                              disabledBorder: InputBorder.none,
                              focusedBorder: InputBorder.none,
                              focusedErrorBorder: InputBorder.none,
                            ),
                            inputFormatters: [
                              if (decimalsResult.valueOrNull case final decimals?)
                                AmountInputFormatter(decimalDigits: decimals),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.only(end: 8.0),
                          child: ElevatedButton(
                            onPressed: () {
                              // 设置为最大可用余额
                              _amountController.text = displayBalance;
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: ColorName.themeColorDark,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(24),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 10,
                              ),
                              elevation: 0,
                            ),
                            child: const Text(
                              'MAX',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // 可用余额
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              child: DefaultTextStyle.merge(
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                child: Row(
                  spacing: 4.0,
                  children: [
                    const Text(
                      'Available:',
                      style: TextStyle(color: Colors.grey, fontWeight: FontWeight.normal),
                    ),
                    Flexible(
                      child: Text(
                        displayBalance,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text(selectedToken.symbol),
                  ],
                ),
              ),
            ),
            if (_errorMessage.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  _errorMessage,
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            const SizedBox(height: 24),
          ],
        ),
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        onPressed: decimalsResult.hasValue
            ? _handleSend
            : decimalsResult.hasError && !decimalsResult.isLoading
            ? () => ref.invalidate(_decimalsProvider(selectedToken))
            : null,
        child: decimalsResult.when(
          data: (_) => Text(context.l10nME.sendButton),
          loading: () => const AppLoading(),
          error: (e, s) => Text(context.l10nME.clickToRetryButton),
        ),
      ),
    );
  }

  Widget _buildSuccessView() {
    return AppScaffold(
      body: SizedBox(
        width: double.infinity,
        child: Column(
          spacing: 24.0,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 成功图标
            Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: Assets.icons.check.svg(
                width: 80,
                height: 80,
                colorFilter: ColorName.successColor.filter,
              ),
            ),
            // Sent 文本
            const Text(
              'Sent',
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      bottomButtonBuilder: (context) => Column(
        spacing: 24.0,
        children: [
          if (_txHash != null && _txHash!.isNotEmpty)
            ThemeTextButton.outlined(
              onPressed: _checkStatus,
              text: 'Check status',
            ),
          ThemeTextButton(
            onPressed: _succeedAndBackToWallet,
            text: 'Back to wallet',
          ),
        ],
      ),
    );
  }
}
