import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/business.dart';
import '/provider/chain.dart' show networkProvider, networksProvider;

class SelectNetwork extends ConsumerWidget {
  const SelectNetwork({super.key});

  void _switchNetwork(
    BuildContext context,
    WidgetRef ref,
    Network network,
  ) {
    AppLoading.run(() async {
      await ChainManager.instance.switchNetwork(network);
      Navigator.of(context).maybePop();
    });
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final network = ref.watch(networkProvider);
    final networks = ref.watch(networksProvider);
    return ScrollableBottomSheet(
      title: 'Select Network',
      sliversBuilder: (context) => [
        SliverList.builder(
          itemCount: networks.length,
          itemBuilder: (context, index) {
            final item = networks[index];
            final isSelected = item.chainIdEvm == network.chainIdEvm;
            return RippleTap(
              onTap: () => _switchNetwork(context, ref, item),
              margin: const EdgeInsets.symmetric(horizontal: 24.0).copyWith(bottom: 12),
              padding: const EdgeInsets.all(10.0),
              color: isSelected ? ColorName.themeColorDark.withValues(alpha: 0.1) : context.meTheme.listColor,
              shape: RoundedRectangleBorder(
                borderRadius: RadiusConstants.max,
                side: isSelected ? BorderSide(color: context.themeColor, width: 2.0) : BorderSide.none,
              ),
              child: Row(
                spacing: 20.0,
                children: [
                  MEImage(
                    item.iconUrl,
                    width: 60.0,
                    height: 60.0,
                    clipOval: true,
                    fit: BoxFit.cover,
                    alternativeSVG: true,
                    emptyBuilder: (context) => const Icon(
                      Icons.account_balance_wallet,
                      size: 24,
                    ),
                  ),
                  Expanded(
                    child: AutoSizeText(
                      item.name,
                      style: context.textTheme.headlineSmall,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }
}
