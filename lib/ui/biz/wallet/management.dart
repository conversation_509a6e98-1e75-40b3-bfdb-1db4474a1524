import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:privy_flutter/privy_flutter.dart';

import '/provider/chain.dart' show networksProvider, networkByNameProvider, walletCreateProvider;

final _privyAuthProvider = FutureProvider.autoDispose<AuthState>((ref) => privyClient.getAuthState());

@FFRoute(name: '/wallet/management')
class WalletManagementPage extends ConsumerStatefulWidget {
  const WalletManagementPage({super.key});

  @override
  ConsumerState<WalletManagementPage> createState() => _WalletManagementPageState();
}

class _WalletManagementPageState extends ConsumerState<WalletManagementPage> {
  late final ethereumNetworks = ref.read(networksProvider).where((e) => e.networkType == 'evm').toList();

  @override
  Widget build(BuildContext context) {
    final result = ref.watch(_privyAuthProvider);
    return AppScaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppScaffold.buildTitleWidget(context, 'Go Web3'),
          const Text('with your Card3 wallet'),
          Expanded(
            child: result.when(
              skipLoadingOnRefresh: false,
              data: (state) {
                final user = state.user;
                if (user == null) {
                  return Column(
                    spacing: 48.0,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      EmptyView(
                        icon: Assets.lottie.placeholderWallet.lottie(width: 200.0, fit: BoxFit.cover),
                        message: 'Log in to view your wallets.',
                      ),
                      ThemeTextButton(
                        onPressed: () => Navigator.of(context).pushReplacementNamed(Routes.walletAuthenticate.name),
                        text: 'Log In',
                      ),
                    ],
                  );
                }
                return ListView(
                  padding: const EdgeInsets.symmetric(vertical: 12.0),
                  children: [
                    ...user.embeddedEthereumWallets.map((wallet) => _buildEthereumWallet(context, wallet)),
                    ...user.embeddedSolanaWallets.map((wallet) => _buildSolanaWallet(context, wallet)),
                    if (!isSealed)
                      ThemeTextButton(
                        onPressed: () => AppLoading.run(() async {
                          await privyClient.logout();
                          ref.invalidate(walletCreateProvider);
                          ChainManager.instance.dispose();
                          meNavigator.popUntil((route) => route.settings.name == Routes.home.name);
                        }),
                        text: '(Debug) Privy Logout',
                      ),
                  ],
                );
              },
              loading: () => ListView.builder(
                itemCount: 2,
                itemBuilder: (context, _) => const _WalletShimmer(),
              ),
              error: (e, s) => EmptyView(
                onTap: () => ref.invalidate(_privyAuthProvider),
                message: isNetworkError(e) ? context.l10nME.networkError : context.l10nME.clickToRetryButton,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEthereumWallet(
    BuildContext context,
    EmbeddedEthereumWallet wallet,
  ) {
    return RippleTap(
      onTap: () => copyAndToast(wallet.address),
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.all(16),
      borderRadius: BorderRadius.circular(20),
      color: context.theme.cardColor,
      child: Column(
        spacing: 12.0,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'EVM Wallet',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: ethereumNetworks
                    .take(3)
                    .map(
                      (network) => Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: MEImage(
                          network.iconUrl,
                          width: 32.0,
                          height: 32.0,
                          clipOval: true,
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
          ),
          FittedBox(
            child: Text(
              wallet.address,
              style: const TextStyle(color: Colors.grey),
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSolanaWallet(
    BuildContext context,
    EmbeddedSolanaWallet wallet,
  ) {
    return RippleTap(
      onTap: () => copyAndToast(wallet.address),
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.all(16),
      borderRadius: BorderRadius.circular(20),
      color: context.theme.cardColor,
      child: Column(
        spacing: 12.0,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Solana Wallet',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: MEImage(
                  ref.watch(networkByNameProvider('solana')).iconUrl,
                  width: 32.0,
                  height: 32.0,
                  clipOval: true,
                ),
              ),
            ],
          ),
          FittedBox(
            child: Text(
              wallet.address,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }
}

class _WalletShimmer extends StatelessWidget {
  const _WalletShimmer();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: context.theme.cardColor,
      ),
      child: Column(
        spacing: 12.0,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Wallet',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: List.generate(
                  3,
                  (_) => Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: CircleAvatar(
                      radius: 16.0,
                      backgroundColor: context.theme.cardColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const Text(
            '0xabcdefghijklmnopqrstuvwxyz1234567890',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
