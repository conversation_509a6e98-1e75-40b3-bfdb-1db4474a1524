import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:privy_flutter/privy_flutter.dart';

import '/provider/chain.dart' show walletCreateProvider;
import '/ui/widgets/pin_code.dart';

const _pinCodeLength = 6;

@FFRoute(name: '/wallet/authenticate')
class WalletAuthenticatePage extends ConsumerStatefulWidget {
  const WalletAuthenticatePage({super.key});

  @override
  ConsumerState createState() => _WalletAuthenticatePageState();
}

class _WalletAuthenticatePageState extends ConsumerState<WalletAuthenticatePage> {
  late final _email = ref.read(userRepoProvider)!.userEmail;

  bool _isLoading = true;
  PrivyUser? _savedPrivyUser;

  final _codeController = TextEditingController();
  bool _isEmailVerifying = false;
  final _resendSeconds = 60;
  late int _resendCountdown = _resendSeconds;
  Timer? _resendTimer;
  bool _shouldRetryVerifyEmail = false;

  @override
  void initState() {
    super.initState();
    privyClient
        .getAuthState()
        .then(
          (state) {
            final user = _savedPrivyUser = state.user;
            if (user == null) {
              _sendCodeThroughPrivy(_email);
            } else if (user.hasAllWallets) {
              ChainManager.instance.initialize();
              if (mounted) {
                meNavigator.popUntil((route) => route.settings.name == Routes.walletPortfolio.name);
              }
            }
          },
          onError: (e, s) {
            handleExceptions(error: e, stackTrace: s);
          },
        )
        .whenComplete(() {
          safeSetState(() {
            _isLoading = false;
          });
        });
  }

  @override
  void dispose() {
    _codeController.dispose();
    _resendTimer?.cancel();
    super.dispose();
  }

  void _setResendTimer() {
    _resendTimer?.cancel();
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        _resendTimer = null;
        return;
      }

      setState(() {
        if (_resendCountdown == 1) {
          timer.cancel();
          _resendTimer = null;
          _resendCountdown = _resendSeconds;
        } else {
          _resendCountdown--;
        }
      });
    });
    safeSetState(() {});
  }

  Future<void> _sendCodeThroughPrivy(String email) async {
    safeSetState(() {
      _isEmailVerifying = true;
    });

    try {
      final result = await privyClient.email.sendCode(email);
      result.fold(
        onSuccess: (_) {
          _setResendTimer();
          safeSetState(() {
            _savedPrivyUser = null;
            Card3ToastUtil.showToast(message: ToastMessages.emailCodeResent);
            FocusManager.instance.primaryFocus?.unfocus();
          });
        },
        onFailure: (error) {
          throw error;
        },
      );
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToSendEmailCode);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isEmailVerifying = false;
      });
    }
  }

  // 验证邮箱验证码
  Future<void> _verifyEmailCode() async {
    final code = _codeController.text;
    if (code.length < 6) {
      Card3ToastUtil.showToast(message: ToastMessages.invalidOrExpiredLoginCode);
      return;
    }

    safeSetState(() {
      _isEmailVerifying = true;
      _shouldRetryVerifyEmail = false;
    });

    try {
      final userResult = await privyClient.email.loginWithCode(
        code: code,
        email: _email,
      );

      if (!mounted) {
        return;
      }

      userResult.fold(
        onFailure: (error) {
          _isEmailVerifying = false;
          _shouldRetryVerifyEmail = false;
          _savedPrivyUser = null;
          safeSetState(() {
            Card3ToastUtil.showToast(message: ToastMessages.invalidOrExpiredLoginCode);
          });
        },
        onSuccess: (user) async {
          try {
            _savedPrivyUser = user;
          } finally {
            safeSetState(() {
              _isEmailVerifying = false;
            });
          }
        },
      );
    } catch (e) {
      final String message;
      if (e is ApiException) {
        message = '${e.message} (${e.code})';
      } else if (e is PrivyException) {
        message = e.message;
      } else {
        message = isNetworkError(e) ? context.l10nME.networkError : '$e';
      }

      safeSetState(() {
        Card3ToastUtil.showToast(message: ToastMessages.loginFailed(message));
        _shouldRetryVerifyEmail = true;
      });

      rethrow;
    } finally {
      safeSetState(() {
        _isEmailVerifying = false;
      });
    }
  }

  // 重新发送验证码
  Future<void> _resendCode() async {
    safeSetState(() {
      _isEmailVerifying = true;
    });
    try {
      await _sendCodeThroughPrivy(_email);
      safeSetState(() {
        _codeController.clear();
        Card3ToastUtil.showToast(message: ToastMessages.emailCodeResent);
      });
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToResendEmailCode);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isEmailVerifying = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const AppScaffold(body: AppLoading());
    }
    if (_savedPrivyUser case final user?) {
      return _WalletCreation(user);
    }
    return PopScope(
      canPop: !_isEmailVerifying,
      child: AppScaffold(
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 40.0, bottom: 20.0),
              child: Text(
                'Enter login code for your wallet',
                style: context.textTheme.headlineSmall,
              ),
            ),
            _buildPinInput(context),
            if (_isEmailVerifying) const AppLoading(size: 64.0),
          ],
        ),
        bottomButtonBuilder: (context) => Column(
          spacing: 16.0,
          children: [
            if (_shouldRetryVerifyEmail)
              ThemeTextButton.outlined(
                onPressed: _verifyEmailCode,
                themeColor: context.meTheme.failingColor,
                text: context.l10nME.retryButton,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPinInput(BuildContext context) {
    return Column(
      spacing: 24.0,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 16.0),
          child: Text.rich(
            TextSpan(
              children: [
                const TextSpan(text: 'Please check '),
                TextSpan(
                  text: _email,
                  style: TextStyle(
                    color: context.themeColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const TextSpan(
                  text: ' for an email from privy.io and enter your code below.',
                ),
              ],
            ),
            style: const TextStyle(fontSize: 16),
          ),
        ),
        PinCodeInputWidget(
          length: _pinCodeLength,
          controller: _codeController,
          enabled: !_isEmailVerifying,
          onCompleted: (value) {
            if (mounted) {
              _verifyEmailCode();
            }
          },
          onChanged: (value) {
            if (mounted) {
              _shouldRetryVerifyEmail = false;
              _savedPrivyUser = null;
            }
          },
        ),
        Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                "Didn't get the email? ",
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF757575),
                ),
              ),
              GestureDetector(
                onTap: _isEmailVerifying || _resendTimer != null ? null : _resendCode,
                child: Text(
                  'Resend code',
                  style: TextStyle(
                    fontSize: 16,
                    color: context.themeColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (_resendTimer != null)
                Text(
                  ' (${_resendCountdown}s)',
                  style: const TextStyle(fontSize: 16, color: Colors.grey),
                ),
            ],
          ),
        ),
      ],
    );
  }
}

class _WalletCreation extends ConsumerWidget {
  const _WalletCreation(this.user);

  final PrivyUser user;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(walletCreateProvider(user.id));
    return AppScaffold(
      title: 'Creating Wallets',
      body: EmptyView(
        icon: Assets.lottie.placeholderWallet.lottie(width: 200.0, fit: BoxFit.cover),
        message: result.when(
          skipLoadingOnRefresh: false,
          data: (_) => 'Your wallet is ready!',
          loading: () => 'Creating wallets, it might takes minutes, please wait...',
          error: (e, s) => isNetworkError(e) ? context.l10nME.networkError : '$e',
        ),
      ),
      bottomButtonBuilder: (context) => result.when(
        skipLoadingOnRefresh: false,
        data: (_) => ThemeTextButton(
          onPressed: () => Navigator.of(context).pushReplacementNamed(Routes.walletPortfolio.name),
          text: context.l10nME.doneButton,
        ),
        error: (e, s) => ThemeTextButton.outlined(
          onPressed: () => ref.invalidate(walletCreateProvider(user.id)),
          text: context.l10nME.retryButton,
        ),
        loading: () => const SizedBox.shrink(),
      ),
    );
  }
}
