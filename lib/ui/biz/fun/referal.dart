import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:sliver_tools/sliver_tools.dart' show MultiSliver, SliverPinnedHeader;

import '/models/business.dart';
import '/provider/api.dart';
import '/provider/user.dart';

final _listProvider = FutureProvider.autoDispose.family<Paged<ReferralLog>, int>((ref, page) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  final apiService = ref.read(apiServiceProvider);
  final logsPage = await apiService.getReferralLogs(
    pageNum: page,
    pageSize: 20,
    cancelToken: ct,
  );
  return logsPage;
});

@FFRoute(name: '/fun/referral')
class ReferralPage extends ConsumerStatefulWidget {
  const ReferralPage({super.key});

  @override
  ConsumerState<ReferralPage> createState() => _ReferralPageState();
}

class _ReferralPageState extends ConsumerState<ReferralPage> {
  Future<void> _refresh() async {
    ref.invalidate(_listProvider);
    await ref.read(_listProvider(1).future);
  }

  @override
  Widget build(BuildContext context) {
    final totalResult = ref.watch(_listProvider(1));
    final titleBuffer = StringBuffer('Referred');
    if (totalResult.valueOrNull?.total case final total? when total > 0) {
      titleBuffer.write(' (${total.toNumerical(fractionDigits: 1)})');
    }
    final title = SliverPinnedHeader(
      child: Container(
        color: context.theme.scaffoldBackgroundColor,
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        alignment: AlignmentDirectional.centerStart,
        child: Text(
          titleBuffer.toString(),
          style: context.textTheme.headlineSmall,
        ),
      ),
    );
    final Widget child;
    if (totalResult.valueOrNull?.total == 0) {
      child = SliverEmptyView(
        onTap: _refresh,
        message: 'No referrals yet.',
      );
    } else if (totalResult.hasError && !totalResult.isLoading) {
      final e = totalResult.error;
      child = SliverEmptyView(
        onTap: _refresh,
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      );
    } else {
      child = SliverList.builder(
        itemCount: totalResult.valueOrNull?.total ?? 4,
        itemBuilder: (context, index) {
          final page = index ~/ 20 + 1;
          final indexInPage = index % 20;
          final result = ref.watch(_listProvider(page));
          return result.maybeWhen(
            data: (data) {
              if (indexInPage >= data.list.length) {
                return null;
              }
              final item = data.list[indexInPage];
              return ProviderScope(
                overrides: [_itemProvider.overrideWithValue(item)],
                child: const _ReferralItem(),
              );
            },
            orElse: () => const _ReferralItemShimmer(),
          );
        },
      );
    }
    return AppScaffold(
      body: RefreshIndicator(
        onRefresh: _refresh,
        child: CustomScrollView(
          slivers: [
            const SliverToBoxAdapter(child: _Header()),
            const SliverToBoxAdapter(child: Divider(height: 36.0)),
            MultiSliver(
              pushPinnedChildren: true,
              children: [title, child],
            ),
            const SliverGap.v(50.0),
          ],
        ),
      ),
    );
  }
}

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final link = '$envUrlWebsite?referral=${userInfo?.referralCode ?? ''}';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Gap.v(24.0),
          const Text(
            'My Referral Code',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: AutoSizeText(
                  userInfo?.referralCode ?? '',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.5,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                ),
              ),
              CopyButton(
                onCopy: () => userInfo?.referralCode,
                dimension: 24.0,
              ),
            ],
          ),
          const Gap.v(36.0),
          ThemeTextButton(
            onPressed: () => _showQRCodeDialog(context, link),
            width: 180.0,
            borderRadius: RadiusConstants.max,
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.qr_code_2_rounded),
                Text('QR Code'),
              ],
            ),
          ),
          const Gap.v(16.0),
        ],
      ),
    );
  }

  // 显示二维码弹窗
  void _showQRCodeDialog(BuildContext context, String link) {
    ScrollableBottomSheet.show(
      builder: (context) => _QRCodeDialog(link),
      heightFactor: 0.6,
    );
  }
}

class _QRCodeDialog extends StatelessWidget {
  const _QRCodeDialog(this.link);

  final String link;

  @override
  Widget build(BuildContext context) {
    return ScrollableBottomSheet(
      title: 'My Referral Link',
      sliversBuilder: (context) => [
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 40.0),
            alignment: Alignment.center,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: context.theme.disabledColor),
                borderRadius: BorderRadius.circular(16.0),
                color: Colors.white,
              ),
              child: QrImageView(
                data: link,
                version: QrVersions.auto,
                size: 300.0,
                padding: const EdgeInsets.all(16.0),
              ),
            ),
          ),
        ),
        const SliverGap.v(20.0),
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 40.0),
            alignment: Alignment.center,
            child: Container(
              width: 300.0,
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                borderRadius: 16.0.rCircular,
                color: context.theme.scaffoldBackgroundColor,
              ),
              child: Row(
                spacing: 2.0,
                children: [
                  Expanded(
                    child: Text(
                      link,
                      style: const TextStyle(fontSize: 18.0),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  CopyButton(
                    onCopy: () => link,
                    dimension: 32.0,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

final _itemProvider = Provider.autoDispose<ReferralLog>((ref) => throw UnimplementedError());

class _ReferralItem extends ConsumerWidget {
  const _ReferralItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_itemProvider);
    final textTheme = context.textTheme;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.inviteeEmail.or('Unknown User'),
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  item.createTime.withDateTimeFormat(format: 'yyyy-MM-dd HH:mm'),
                  style: textTheme.bodySmall,
                ),
              ],
            ),
          ),
          if (item.pendingReason.isNotEmpty)
            Tapper(
              onTap: () => TinyDialog.show(text: item.pendingReason),
              child: Text.rich(
                TextSpan(
                  children: [
                    const TextSpan(text: 'Pending '),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: Container(
                        padding: const EdgeInsets.all(2.0),
                        decoration: BoxDecoration(
                          border: Border.all(color: textTheme.bodyMedium!.color!),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.question_mark_rounded,
                          color: textTheme.bodyMedium?.color,
                          size: 12.0,
                        ),
                      ),
                    ),
                  ],
                ),
                style: const TextStyle(
                  fontSize: 20.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          else
            Text(
              '+${item.integral}',
              style: TextStyle(
                color: context.themeColor,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }
}

class _ReferralItemShimmer extends StatelessWidget {
  const _ReferralItemShimmer();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(19),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: MEShimmer(
        child: Row(
          children: [
            Expanded(
              child: Column(
                spacing: 4.0,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 140,
                    height: 16,
                    color: context.theme.cardColor,
                  ),
                  Container(
                    width: 80,
                    height: 14,
                    color: context.theme.cardColor,
                  ),
                ],
              ),
            ),
            Container(
              width: 60,
              height: 24,
              color: context.theme.cardColor,
            ),
          ],
        ),
      ),
    );
  }
}
