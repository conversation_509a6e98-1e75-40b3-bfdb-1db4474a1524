import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/card.dart' show CardInfo, CardType;
import '/provider/business.dart' show fetchDiscoveryEventsProvider, configStateProvider;
import '/provider/card.dart' show fetchMyCardsProvider;

@FFRoute(name: '/card/activate-result')
class CardActivateResultPage extends StatefulWidget {
  const CardActivateResultPage({
    super.key,
    required this.card,
    required this.activated,
  });

  final CardInfo card;
  final bool activated;

  @override
  State<CardActivateResultPage> createState() => _CardActivateResultPageState();
}

class _CardActivateResultPageState extends State<CardActivateResultPage> {
  CardInfo get card => widget.card;

  bool get activated => widget.activated;

  @override
  void initState() {
    super.initState();
    globalContainer.invalidate(fetchMyCardsProvider);
    globalContainer.invalidate(fetchDiscoveryEventsProvider);
    globalContainer.read(configStateProvider.notifier).loadConfig();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      automaticallyImplyLeading: false,
      bodyPadding: EdgeInsets.zero,
      body: Stack(
        clipBehavior: Clip.none,
        children: [
          if (activated) Assets.icons.setting.bgCardActivated.svg(),
          Column(
            spacing: 60.0,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(),
              Container(
                height: 260.0,
                alignment: Alignment.center,
                child: DecoratedBox(
                  decoration: _buildDecoration(
                    context,
                    activated ? context.themeColor : context.theme.dividerColor,
                  ),
                  child: Hero(
                    tag: 'card-activate-cover-${card.cardCode}',
                    child: MEImage(
                      card.backCover,
                      borderRadius: BorderRadius.circular(12.0),
                      fit: BoxFit.fitHeight,
                      emptyBuilder: (context) => switch (card.cardType) {
                        CardType.STICKER => Assets.icons.images.coverSticker.image(fit: BoxFit.cover),
                        CardType.WRISTBAND => Assets.icons.images.coverWristband.image(fit: BoxFit.cover),
                        _ => Assets.icons.images.coverNormalBack.image(fit: BoxFit.cover),
                      },
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  children: [
                    activated
                        ? Assets.icons.check.svg(width: 64.0, height: 64.0)
                        : SizedBox.square(
                            dimension: 64.0,
                            child: CircleAvatar(
                              backgroundColor: context.themeColor,
                              child: const Icon(Icons.link_off_rounded, color: Colors.white, size: 48.0),
                            ),
                          ),
                    const Gap.v(14.0),
                    if (activated) ...[
                      Text('Activated', style: context.textTheme.headlineLarge),
                      Text(
                        'Your new Card3 is online!',
                        style: context.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.normal),
                      ),
                    ] else
                      Text('Unlink Completed', style: context.textTheme.headlineLarge),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        margin: const EdgeInsets.symmetric(horizontal: 24.0),
        onPressed: () {
          Navigator.of(context).maybePop();
        },
        text: context.l10nME.okButton,
      ),
    );
  }

  BoxDecoration _buildDecoration(BuildContext context, Color shadowColor) {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(20.0),
      boxShadow: [
        BoxShadow(
          color: shadowColor,
          spreadRadius: 10.0,
          blurRadius: 50.0,
        ),
      ],
    );
  }
}
