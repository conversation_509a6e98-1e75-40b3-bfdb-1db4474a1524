import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/feat/card/helper.dart' show <PERSON><PERSON>el<PERSON>, CardActivateParams;
import '/feat/nfc/helper.dart' show NfcHelper;
import '/models/card.dart' show CardInfo, NfcType, CardType;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/card.dart' show fetchMyCardsProvider;

@FFRoute(name: '/setting/cards-collection')
class CardsCollectionPage extends ConsumerWidget {
  const CardsCollectionPage({super.key});

  void _onLinkResult(WidgetRef ref, String result) {
    CardHelper.handleCardActivation(
      result,
      onActivated: (card) => Card3ToastUtil.showToast(
        message: ToastMessages.cardAlreadyActivated,
      ),
    );
  }

  Future<void> _onUnlinkResult(WidgetRef ref, String result) async {
    if (!CardHelper.isCard3Format(result)) {
      Card3ToastUtil.showToast(message: ToastMessages.invalidCardFormat);
      return;
    }

    final params = CardHelper.extractActivateParams(result);
    if (params == null) {
      Card3ToastUtil.showToast(message: ToastMessages.invalidCardFormat);
      return;
    }

    final List<CardInfo> cards;
    if (ref.read(fetchMyCardsProvider).valueOrNull case final value?) {
      cards = value;
    } else {
      cards = await AppLoading.run(
        () => ref.read(fetchMyCardsProvider.future),
        until: const Duration(seconds: 1),
      );
    }

    if (cards.firstWhereOrNull((card) => card.cardCode == params.uid) case final card?) {
      ScrollableBottomSheet.show(
        builder: (context) => _UnlinkSheet(card, params),
      );
    } else {
      Card3ToastUtil.showToast(message: 'The card does not belong to you. Try another one.');
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(fetchMyCardsProvider);
    final theme = context.theme;
    return AppScaffold(
      backgroundColor: theme.cardColor,
      body: RefreshIndicator(
        onRefresh: () => ref.refresh(fetchMyCardsProvider.future),
        child: result.when(
          data: (data) {
            return ProviderScope(
              overrides: [_cardsProvider.overrideWithValue(data)],
              child: const _CardCollection(),
            );
          },
          loading: () => const SliverPadding(
            padding: EdgeInsets.only(top: 40.0),
            sliver: AppLoading(),
          ),
          error: (e, s) => SliverPadding(
            padding: const EdgeInsets.only(top: 40.0),
            sliver: RefreshableEmptyView(
              onTap: () => ref.invalidate(fetchMyCardsProvider),
              message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
            ),
          ),
        ),
      ),
      bottomButtonBuilder: result.maybeWhen(
        data: (data) =>
            (context) => Row(
              spacing: 10.0,
              children: [
                if (data.isNotEmpty)
                  Expanded(
                    flex: 2,
                    child: ThemeTextButton.outlined(
                      onPressed: () async {
                        final confirmed = await TinyDialog.show(
                          text:
                              'You are about to unlink a Card3 item, '
                              'it requires you to tapping the exact card with NFC to verify the ownership.',
                          buttonsBuilder: (context) => const TinyDialogButtonGroup(),
                        );
                        if (confirmed != true) {
                          return;
                        }
                        // _onUnlinkResult(ref, 'https://t.card3.co/bIgVPP');
                        // return;
                        NfcHelper.startPollingManually(
                          onResult: (result) {
                            _onUnlinkResult(ref, result);
                            return true;
                          },
                        );
                      },
                      themeColor: context.meTheme.failingColor,
                      text: 'Unlink',
                    ),
                  ),
                Expanded(
                  flex: 3,
                  child: ThemeTextButton(
                    onPressed: () {
                      // _onLinkResult(ref, 'https://t.card3.co/bIgVPP');
                      // return;
                      NfcHelper.startPollingManually(
                        onResult: (result) {
                          _onLinkResult(ref, result);
                          return true;
                        },
                      );
                    },
                    child: Row(
                      spacing: 10.0,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Assets.icons.homeCardNfc.svg(
                          width: 22.0,
                          height: 22.0,
                        ),
                        const Flexible(child: AutoSizeText('Link Item')),
                      ],
                    ),
                  ),
                ),
              ],
            ),
        orElse: () => null,
      ),
    );
  }
}

final _cardsProvider = Provider.autoDispose<List<CardInfo>>(
  (ref) => throw UnimplementedError(),
);

class _CardCollection extends ConsumerWidget {
  const _CardCollection();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cards = ref.watch(_cardsProvider).where((card) => !card.virtualCard).toList();

    final type424 = <CardInfo>[];
    final type215 = <CardInfo>[];
    for (final card in cards) {
      if (card.nfcType == NfcType.NFC424) {
        type424.add(card);
      } else if (card.nfcType == NfcType.NFC215) {
        type215.add(card);
      }
    }

    if (type424.isEmpty && type215.isEmpty) {
      return const RefreshableEmptyView(
        message:
            'Nothing here yet.\n'
            'Your Card3 NFC item will appear once activated.',
      );
    }

    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: AppScaffold.buildTitleWidget(context, 'My Card3 Collections'),
        ),
        if (type424.isNotEmpty) ...[
          _buildTitle(context, 'V2.0'),
          const SliverGap.v(16),
          _buildCardGrid(type424),
          if (type215.isNotEmpty)
            const SliverPadding(
              padding: EdgeInsets.only(top: 20.0),
              sliver: SliverToBoxAdapter(
                child: Divider(height: 20.0, thickness: 1, color: Colors.black),
              ),
            ),
        ],
        if (type215.isNotEmpty) ...[
          _buildTitle(context, 'V1.0'),
          const SliverGap.v(16),
          _buildCardGrid(type215),
        ],
        SliverGap.bottomPadding(context, 50.0),
      ],
    );
  }

  Widget _buildTitle(BuildContext context, String title) {
    return SliverPadding(
      padding: const EdgeInsets.only(top: 24.0),
      sliver: SliverToBoxAdapter(child: Text(title)),
    );
  }

  Widget _buildCardGrid(List<CardInfo> cards) {
    return SliverGrid.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.56,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: cards.length,
      itemBuilder: (context, index) => ProviderScope(
        overrides: [_cardItemProvider.overrideWithValue(cards[index])],
        child: const _CardItem(),
      ),
    );
  }
}

final _cardItemProvider = Provider.autoDispose<CardInfo>(
  (_) => throw UnimplementedError(),
);

class _CardItem extends ConsumerWidget {
  const _CardItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final card = ref.watch(_cardItemProvider);
    final paddedId = card.id.toString().padLeft(8, '0');

    return Column(
      spacing: 8.0,
      children: [
        Expanded(child: card.buildCoverWidget(context)),
        AutoSizeText(
          '${paddedId.substring(0, 4)} ${paddedId.substring(paddedId.length - 4)}',
          style: const TextStyle(fontWeight: FontWeight.w500),
          maxLines: 1,
        ),
      ],
    );
  }
}

class _UnlinkSheet extends ConsumerWidget {
  const _UnlinkSheet(this.card, this.params);

  final CardInfo card;
  final CardActivateParams params;

  Future<void> _unlink(WidgetRef ref) async {
    try {
      await ref
          .read(apiServiceProvider)
          .deactivateCardByParams(
            uid: params.uid,
            ctr: params.ctr,
            cmac: params.cmac,
            activateCode: params.activateCode,
          );
      meNavigator.pushReplacementNamed(
        Routes.cardActivateResult.name,
        arguments: Routes.cardActivateResult.d(card: card, activated: false),
      );
    } catch (e) {
      Card3ToastUtil.showToast(
        message: 'Unable to unlink the item. Please try again later.',
      );
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final textTheme = theme.textTheme;
    final meTheme = theme.extension<METheme>()!;
    return ScrollableBottomSheet(
      title: 'Deleting Card3 item',
      sliversBuilder: (context) => [
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          sliver: SliverToBoxAdapter(
            child: Text.rich(
              TextSpan(
                children: [
                  const TextSpan(text: 'Caution: This NFC item is currently linked to your account'),
                  const TextSpan(text: ' '),
                  TextSpan(text: '(Email: ${ref.watch(userRepoProvider)?.userEmail})'),
                  const TextSpan(text: '.'),
                  const TextSpan(text: '\n\n'),
                  const TextSpan(
                    text:
                        'Unlinking will:\n'
                        '· Remove its connection with your profile.\n'
                        '· Require re-linking before using it again.',
                  ),
                ],
              ),
              style: const TextStyle(fontSize: 18.0, height: 1.5),
            ),
          ),
        ),
        const SliverGap.v(36.0),
        SliverToBoxAdapter(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 24.0),
            padding: const EdgeInsets.all(10.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: meTheme.listColor,
            ),
            child: Row(
              spacing: 20.0,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: 60.0,
                  child: AspectRatio(aspectRatio: 0.65, child: card.buildCoverWidget(context)),
                ),
                Expanded(
                  child: Column(
                    spacing: 4.0,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AutoSizeText(
                        card.eventName,
                        style: textTheme.headlineSmall,
                        maxLines: 1,
                        overflow: TextOverflow.fade,
                        softWrap: false,
                      ),
                      AutoSizeText(
                        card.id.toString().padLeft(8, '0'),
                        style: textTheme.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.fade,
                        softWrap: false,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
      bottomBuilder: (context) => ThemeTextButtonGroup(
        outlinedCancel: false,
        cancelColor: theme.scaffoldBackgroundColor,
        onConfirm: () => AppLoading.run(() => _unlink(ref)),
        confirmThemeColor: context.meTheme.failingColor,
        confirmText: 'Unlink',
      ),
    );
  }
}

extension on CardInfo {
  Widget buildCoverWidget(BuildContext context) => Hero(
    tag: 'card-activate-cover-$cardCode',
    child: MEImage(
      backCover,
      fit: BoxFit.fitWidth,
      borderRadius: const BorderRadius.all(Radius.circular(12.0)),
      emptyBuilder: (context) => switch (cardType) {
        CardType.STICKER => Assets.icons.images.coverSticker.image(
          fit: BoxFit.cover,
        ),
        CardType.WRISTBAND => Assets.icons.images.coverWristband.image(
          fit: BoxFit.cover,
        ),
        _ => Assets.icons.images.coverNormalBack.image(
          fit: BoxFit.cover,
        ),
      },
    ),
  );
}
