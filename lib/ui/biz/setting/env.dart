import 'dart:io' show exit;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFRoute(name: '/settings/env')
class UpdateEnvPage extends ConsumerStatefulWidget {
  const UpdateEnvPage({super.key});

  @override
  ConsumerState createState() => _UpdateEnvPageState();
}

class _UpdateEnvPageState extends ConsumerState<UpdateEnvPage> {
  bool _filter(MapEntry<String, dynamic> entry) {
    return !entry.key.contains('KEY');
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      body: ListView(
        children: [
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text.rich(
              TextSpan(
                children: [
                  const TextSpan(text: 'Current Env'),
                  // if (Env.overrode)
                  //   const TextSpan(
                  //     text: ' (overrode) ',
                  //     style: TextStyle(color: Colors.red),
                  //   ),
                  const TextSpan(text: ': '),
                  TextSpan(
                    text: env.env,
                    style: TextStyle(color: context.themeColor),
                  ),
                ],
              ),
              style: Theme.of(context).textTheme.headlineSmall,
            ),
          ),
          const Gap.v(8.0),
          Table(
            border: TableBorder.all(color: Theme.of(context).dividerColor),
            children: [
              TableRow(
                children: [
                  _buildTableText(
                    'Key',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  _buildTableText(
                    'Value',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              ...env.entries
                  .where(_filter)
                  .map(
                    (e) => TableRow(
                      children: [
                        _buildTableText(e.key),
                        _buildTableText(e.value),
                      ],
                    ),
                  ),
            ],
          ),
          const Gap.v(8.0),
          // ...Env.values.where((e) => e != env).map(
          //       (env) => Container(
          //         alignment: Alignment.center,
          //         margin: const EdgeInsets.only(bottom: 8.0),
          //         child: OutlinedButton(
          //           onPressed: () async {
          //             EnvOverrides.overrideLocal(env);
          //             _exitApp();
          //           },
          //           child: Text('Switch to *${env.env}*'),
          //         ),
          //       ),
          //     ),
          // if (EnvOverrides.localOverrode case final env?)
          //   Container(
          //     alignment: Alignment.center,
          //     margin: const EdgeInsets.only(bottom: 8.0),
          //     child: OutlinedButton(
          //       onPressed: () async {
          //         EnvOverrides.overrideLocal(null);
          //         _exitApp();
          //       },
          //       child: Text('Clear override (${env.env})'),
          //     ),
          //   ),
          // const GradientDivider(height: 1.0),
          // const Gap.v(8.0),
          if (Release.sealed)
            Container(
              alignment: Alignment.center,
              margin: const EdgeInsets.only(bottom: 8.0),
              child: OutlinedButton(
                onPressed: () async {
                  EnvOverrides.overrideSealed(!EnvOverrides.sealedOverrode);
                  _exitApp();
                },
                child: Text(EnvOverrides.sealedOverrode ? 'Sealed' : 'Unseal'),
              ),
            ),
          Container(
            alignment: Alignment.center,
            margin: const EdgeInsets.only(bottom: 8.0),
            child: OutlinedButton(
              onPressed: () async {
                final before = EnvOverrides.jwtOverrode ?? '';
                final tec = TextEditingController(text: before);
                await showDialog(
                  context: context,
                  builder: (context) => AlertDialog.adaptive(
                    content: TextField(controller: tec),
                    actions: [
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).maybePop(),
                        child: Text(
                          context.l10nME.confirmButton,
                        ),
                      ),
                    ],
                  ),
                );
                if (tec.text != before) {
                  EnvOverrides.overrideJWT(tec.text);
                  _exitApp();
                }
              },
              child: const Text('JWT override'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableText(String text, {TextStyle? style}) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Text(text, style: const TextStyle(fontSize: 12.0).merge(style)),
    );
  }

  void _exitApp() {
    showToast('Please reopen the app after the close.');
    2.seconds.delay.whenComplete(() {
      exit(0);
    });
  }
}
