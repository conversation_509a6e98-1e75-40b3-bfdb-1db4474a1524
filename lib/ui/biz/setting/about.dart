import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '/feat/messaging/helper.dart' show MessagingHelper;
import '/provider/settings.dart';
import '/ui/widgets/app_logo.dart';

@FFRoute(name: '/setting/about')
class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  (String id, String message) _buildPingContent() {
    final id = const Uuid().v4();
    final buffer = StringBuffer();
    buffer.writeln('Triggered a ping.');
    buffer.writeln('[I]: $id');
    buffer.writeln(
      '[U]: ${BoxService.getUserInfo()?.toJson()}',
    );
    buffer.writeln(
      '[T]: ${EnvOverrides.jwtOverrode ?? BoxService.getToken()}',
    );
    buffer.writeln(
      '[FCM]: ${MessagingHelper.token}',
    );
    if (DeviceUtil.deviceUuid case final uuid? when uuid.isNotEmpty) {
      buffer.writeln('[UUID]: $uuid');
    }
    return (id, buffer.toString());
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      body: Column(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Consumer(
                  builder: (context, ref, child) {
                    ref.watch(settingsEnvTapCount);
                    return GestureDetector(
                      onTap: () => settingsEnvOnTap(context, ref),
                      onLongPress: () => AppLoading.run(() async {
                        {
                          final (id, message) = _buildPingContent();
                          await BotUtil.reportToBot(
                            message: message,
                            overrideUri: Uri.parse(envBotUtilPingUrl),
                            overrideSigningKey: envBotUtilPingKey,
                          );
                          HapticUtil.notifySuccess(force: true);
                          showToast('We have received your ping!\n$id');
                        }
                      }),
                      child: Assets.lottie.acard.lottie(width: 320.0),
                    );
                  },
                ),
                Center(
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 4.0),
                    padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
                    decoration: BoxDecoration(
                      borderRadius: RadiusConstants.max,
                      color: context.theme.cardColor,
                    ),
                    child: Text(
                      'v${PackageUtil.versionName} (${PackageUtil.versionCode})',
                      style: const TextStyle(
                        fontSize: 18.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                Center(
                  child: Text(
                    '${DateTime.parse(Release.buildTime).format(format: 'yyyy-MM-dd HH:mm:ss')} '
                    '(${Release.commitRef})',
                    style: context.textTheme.bodySmall?.copyWith(fontSize: 10.0),
                  ),
                ),
              ],
            ),
          ),
          const Gap.v(24.0),
          GridView.count(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            crossAxisCount: 2,
            mainAxisSpacing: 16.0,
            crossAxisSpacing: 16.0,
            childAspectRatio: 1.5,
            children:
                [
                      (
                        Assets.icons.social.twitter.svg(width: 40, height: 40),
                        'Follow us on X',
                        () => _launchUrl('https://x.com/card3_ai'),
                      ),
                      (
                        const AppLogo(width: 80.0, fit: BoxFit.contain),
                        'Visit our website',
                        () => _launchUrl(envUrlWebsite),
                      ),
                      (
                        Assets.icons.social.telegram.svg(width: 80, height: 40),
                        'Card3 Telegram',
                        () => _launchUrl('https://t.me/card3official'),
                      ),
                    ]
                    .map(
                      (e) => _buildLinkCard(context, icon: e.$1, title: e.$2, onTap: e.$3),
                    )
                    .toList(),
          ),
          Gap.v(MediaQuery.paddingOf(context).bottom.max(24.0)),
        ],
      ),
    );
  }

  // 创建链接卡片
  Widget _buildLinkCard(
    BuildContext context, {
    required Widget icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return RippleTap(
      onTap: onTap,
      padding: const EdgeInsets.all(20.0),
      borderRadius: BorderRadius.circular(16.0),
      color: context.theme.cardColor,
      child: Column(
        spacing: 12.0,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(child: icon),
          AutoSizeText(
            title,
            style: const TextStyle(
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  // 打开URL
  Future<void> _launchUrl(String url) async {
    if (await canLaunchUrlString(url)) {
      await launchUrlString(url, mode: LaunchMode.externalApplication);
    } else {
      Card3ToastUtil.showToast(message: ToastMessages.couldNotLaunch(url));
    }
  }
}
