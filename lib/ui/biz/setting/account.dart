import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/user.dart' show UserInfo;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/settings.dart' show selectedIndexProvider;
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;

@FFRoute(name: '/setting/account')
class AccountSecurity extends ConsumerStatefulWidget {
  const AccountSecurity({super.key});

  @override
  ConsumerState<AccountSecurity> createState() => _AccountSecurityState();
}

class _AccountSecurityState extends ConsumerState<AccountSecurity> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;

    final theme = context.theme;
    final textTheme = theme.textTheme;

    return AppScaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppScaffold.buildTitleWidget(context, 'Account Security'),
          Container(
            height: 100.0,
            margin: const EdgeInsets.symmetric(vertical: 16.0),
            padding: const EdgeInsets.all(10.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: theme.cardColor,
            ),
            child: Row(
              spacing: 10.0,
              children: [
                UserAvatar(
                  user: userInfo,
                  dimension: 80.0,
                  borderRadius: BorderRadius.circular(20.0),
                ),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AutoSizeText(
                        userInfo?.name.or('Name') ?? 'Name',
                        style: textTheme.headlineSmall,
                        maxLines: 1,
                        overflow: TextOverflow.fade,
                        softWrap: false,
                      ),
                      AutoSizeText(
                        userInfo?.userEmail.or('Email') ?? 'Email',
                        style: textTheme.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.fade,
                        softWrap: false,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          ThemeTextButton.outlined(
            onPressed: () => _showDeleteConfirmation(context, userInfo!),
            borderRadius: BorderRadius.circular(20.0),
            themeColor: Colors.red,
            child: _isLoading ? const AppLoading() : const Text('Delete Account'),
          ),
        ],
      ),
    );
  }

  Future<void> _showDeleteConfirmation(BuildContext context, UserInfo user) async {
    final result = await showModalBottomSheet(
      context: context,
      builder: (context) => _DeleteSheet(user),
    );
    if (result == true) {
      _deleteAccount();
    }
  }

  Future<void> _deleteAccount() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(apiServiceProvider).deleteUser();
      await privyClient.logout();
      ref.read(userRepoProvider.notifier).reset();
      ref.read(selectedIndexProvider.notifier).state = 0;
      meNavigator.removeNamedAndPushAndRemoveUntil(
        Routes.login.name,
        predicate: (_) => false,
      );
      Card3ToastUtil.showToast(message: ToastMessages.deleted);
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.deleteAccountFailed);
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }
}

class _DeleteSheet extends StatelessWidget {
  const _DeleteSheet(this.user);

  final UserInfo user;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final textTheme = theme.textTheme;
    final meTheme = theme.extension<METheme>()!;
    return Padding(
      padding: const EdgeInsets.only(top: 24.0),
      child: Column(
        children: [
          Row(
            spacing: 16.0,
            children: [
              const Gap.h(4.0),
              IconButton(
                onPressed: () => Navigator.pop(context),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                color: meTheme.primaryTextColor,
                icon: const Icon(Icons.arrow_back_ios_new_rounded),
              ),
              Expanded(
                child: Text('Delete Account?', style: textTheme.headlineMedium),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Text.rich(
              TextSpan(
                children: [
                  const TextSpan(text: 'Account: '),
                  TextSpan(
                    text: user.userEmail,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(text: '\n\n'),
                  const TextSpan(text: 'This action is permanent and cannot be undone. Proceed with caution.'),
                ],
              ),
              style: const TextStyle(color: Colors.red, fontSize: 18.0),
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24.0),
            padding: const EdgeInsets.all(10.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: meTheme.listColor,
            ),
            child: Row(
              spacing: 10.0,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                UserAvatar(
                  user: user,
                  dimension: 80.0,
                  borderRadius: BorderRadius.circular(20.0),
                ),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AutoSizeText(
                        user.name,
                        style: textTheme.headlineSmall,
                        maxLines: 1,
                        overflow: TextOverflow.fade,
                        softWrap: false,
                      ),
                      AutoSizeText(
                        user.userEmail,
                        style: textTheme.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.fade,
                        softWrap: false,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          ThemeTextButton(
            margin: const EdgeInsets.all(24.0).copyWith(
              bottom: MediaQuery.paddingOf(context).bottom.max(24.0),
            ),
            onPressed: () => Navigator.pop(context, true),
            themeColor: Colors.red,
            text: context.l10nME.deleteButton,
            textStyle: const TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }
}
