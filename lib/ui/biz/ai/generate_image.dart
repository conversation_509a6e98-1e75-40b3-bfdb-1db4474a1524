import 'dart:io' as io show File;
import 'dart:ui' as ui show ImageFilter;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/feat/notification/handler.dart' show handleNotificationOpenWithPayload;
import '/models/_shared.dart' show jsonTryDecode;
import '/models/business.dart'
    show
        ImageAIStyle,
        ImageAITask,
        ImageAITaskDone,
        ImageAITaskError,
        ImageAITaskGenerating,
        ImageAITaskInQueue,
        ImageAITaskNotFound;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/business.dart' show appConfigsRepoProvider, fetchAIImageQuotaProvider;
import '/ui/widgets/social/profile/avatar_img.dart';

@FFRoute(name: '/ai/generate/image')
class AIGenerateImagePage extends ConsumerStatefulWidget {
  const AIGenerateImagePage({
    super.key,
    this.taskId,
    this.generatedUrl,
    this.filePath,
    this.style,
  });

  final String? taskId;
  final String? generatedUrl;

  final String? filePath;
  final ImageAIStyle? style;

  @override
  ConsumerState<AIGenerateImagePage> createState() => _AIGenerateImagePageState();
}

class _AIGenerateImagePageState extends ConsumerState<AIGenerateImagePage> {
  final _pickerKey = GlobalKey<AvatarImgPickerState>();

  late String? _taskId = widget.taskId;
  late String _generatedUrl = widget.generatedUrl ?? '';
  late String? _filePath = widget.filePath;
  late ImageAIStyle? _style = widget.style;

  final _uploadProgress = ValueNotifier<double>(0.0);
  ImageAITask? _taskResult;
  CancelToken? _taskCancelToken;
  bool _exceptionThrown = false;

  bool get generating => switch (_taskResult) {
    ImageAITaskGenerating() => true,
    ImageAITaskInQueue() => true,
    _ => false,
  };

  @override
  void initState() {
    super.initState();
    if (_taskId == null) {
      generate();
    } else {
      checkResult();
    }
  }

  @override
  void dispose() {
    _uploadProgress.dispose();
    _taskCancelToken?.cancel();
    super.dispose();
  }

  Future<void> generate() async {
    final filePath = _filePath;
    final style = _style;
    if (filePath == null || filePath.isEmpty || style == null) {
      return;
    }

    safeSetState(() {
      _uploadProgress.value = 0.0;
      _exceptionThrown = false;
    });

    try {
      _taskCancelToken = CancelToken();
      final taskInQueue = await ref
          .read(apiServiceProvider)
          .createImageTask(
            imageFilePath: filePath,
            style: style,
            cancelToken: _taskCancelToken,
            onProgress: (count, total) {
              if (!mounted || total == 0) {
                return;
              }
              _uploadProgress.value = count / total;
            },
          );

      // Refreshes the quota after the task has submitted.
      ref.invalidate(fetchAIImageQuotaProvider);

      _taskId = taskInQueue.taskId;
      checkResult();
    } catch (e) {
      _exceptionThrown = true;
      rethrow;
    } finally {
      safeSetState(() {});
    }
  }

  Future<void> checkResult() async {
    final taskId = _taskId;
    if (taskId == null) {
      return;
    }

    safeSetState(() {
      _exceptionThrown = false;
    });

    ImageAITask? taskResult = _taskResult;
    while (mounted && (taskResult == null || taskResult.running)) {
      _taskCancelToken?.cancel();
      _taskCancelToken = CancelToken();

      try {
        taskResult = _taskResult = await ref
            .read(apiServiceProvider)
            .getImageTaskResult(
              taskId: taskId,
              cancelToken: _taskCancelToken,
            );
      } catch (e) {
        _exceptionThrown = true;
        rethrow;
      } finally {
        if (mounted) {
          // Refreshes the quota after fetching the task result.
          ref.invalidate(fetchAIImageQuotaProvider);
        }
        safeSetState(() {});
      }

      if (!taskResult.running) {
        break;
      }

      await Future.delayed(const Duration(seconds: 5));
    }

    if (!mounted) {
      return;
    }

    if (taskResult is ImageAITaskDone) {
      _generatedUrl = taskResult.imageUrl;
      safeSetState(() {});
      return;
    }

    if (taskResult is ImageAITaskNotFound) {
      _exceptionThrown = true;
      throw taskResult;
    }

    if (taskResult is ImageAITaskError) {
      _exceptionThrown = true;
      throw StateError(taskResult.error);
    }

    _exceptionThrown = true;
    throw StateError('Unknown AI generate task result: [$taskId] $taskResult');
  }

  Future<void> _onImageFilePicked(String filePath, ImageAIStyle? aiStyle) async {
    if (!mounted) {
      return;
    }
    setState(() {
      _filePath = filePath;
      _style = aiStyle;
      _taskId = null;
      _generatedUrl = '';
      _taskResult = null;
      _exceptionThrown = false;
    });
    generate();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final textTheme = theme.textTheme;
    final quotaResult = ref.watch(fetchAIImageQuotaProvider);
    return AppScaffold(
      withBlurBackground: true,
      appBarLeading: _taskId == null
          ? const SizedBox.square(dimension: kMinInteractiveDimension)
          : const AppBackButton(),
      body: SizedBox.expand(
        child: Column(
          spacing: 36.0,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_taskId == null && _taskResult == null)
              ValueListenableBuilder(
                valueListenable: _uploadProgress,
                builder: (context, value, _) => Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(text: 'Uploading...'),
                      if (value > 0.0) TextSpan(text: ' ${(value * 100).round()}%'),
                    ],
                  ),
                  style: textTheme.headlineMedium,
                ),
              )
            else if (generating)
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: 'Generating...',
                      style: textTheme.headlineMedium,
                    ),
                    if (_taskResult?.queueEstimateSeconds case final seconds?) ...[
                      const TextSpan(text: ' ('),
                      TextSpan(text: '$seconds'),
                      const TextSpan(text: 's)'),
                    ],
                    if (_taskResult case final ImageAITaskInQueue result when result.queueNumber > 0) ...[
                      TextSpan(text: ' (${result.queueNumber} in queue)'),
                    ],
                  ],
                  style: textTheme.bodySmall?.copyWith(fontSize: 18.0),
                ),
              )
            else if (_taskResult is ImageAITaskDone)
              Text('Your image is ready', style: textTheme.headlineMedium)
            else
              Text('', style: textTheme.headlineMedium),
            ClipRRect(
              borderRadius: _taskResult is ImageAITaskError ? BorderRadius.zero : BorderRadius.circular(30.0),
              child: SizedBox.square(
                dimension: _taskResult is ImageAITaskError ? 100.0 : 300.0,
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    Center(
                      child: AvatarImgPicker(
                        key: _pickerKey,
                        size: 0.0,
                        showEditButton: false,
                        onImageSelected: (result) {},
                      ),
                    ),
                    if (_generatedUrl.isNotEmpty || _taskResult is ImageAITaskDone)
                      MEImage(
                        _generatedUrl,
                        borderRadius: BorderRadius.circular(30.0),
                        backgroundColor: theme.cardColor,
                        fit: BoxFit.cover,
                        emptyBuilder: (context) => const Center(
                          child: Icon(
                            Icons.question_mark_rounded,
                            size: 84.0,
                          ),
                        ),
                      )
                    else if (_taskResult is ImageAITaskError)
                      Assets.icons.placeholderBroken.svg(width: 100.0, fit: BoxFit.contain)
                    else if (_filePath case final filePath?)
                      ClipRRect(
                        borderRadius: BorderRadius.circular(30.0),
                        child: ImageFiltered(
                          imageFilter: ui.ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                          child: Image.file(
                            io.File(filePath),
                            fit: BoxFit.cover,
                          ),
                        ),
                      )
                    else
                      DecoratedBox(decoration: BoxDecoration(color: theme.dividerColor)),
                    if (_taskResult == null || generating) ...[
                      const SizedBox.expand(child: ColoredBox(color: Colors.black26)),
                      if (_exceptionThrown)
                        IconButton(
                          onPressed: () {
                            if (_taskId == null) {
                              generate();
                            } else {
                              checkResult();
                            }
                          },
                          icon: Icon(
                            Icons.refresh_rounded,
                            color: theme.colorScheme.error,
                            size: 84.0,
                          ),
                        )
                      else
                        const AppLoading(),
                    ],
                  ],
                ),
              ),
            ),
            if (_taskResult is ImageAITaskError) _buildTaskError(context, _taskResult as ImageAITaskError),
          ],
        ),
      ),
      bottomButtonBuilder: (context) {
        final rawConfig = ref.watch(appConfigsRepoProvider.notifier).getByKey('aiGenerateImage');
        if (_taskResult == null || generating) {
          final buttonHeight = MEUIConfig.themeTextButtonConfig.height ?? MEUIThemeTextButtonConfig.defaultHeight;
          return Gap.v(buttonHeight * 2 + 10.0);
        }
        return Column(
          spacing: 10.0,
          children: [
            _buildGenerateNewButton(context, quotaResult, rawConfig),
            if (_taskResult is ImageAITaskDone)
              ThemeTextButton(
                onPressed: () => Navigator.of(context).maybePop(_generatedUrl.orNull()),
                text: 'Use this image',
              ),
          ],
        );
      },
    );
  }

  Widget _buildTaskError(BuildContext context, ImageAITaskError error) {
    final String errorText;
    if (jsonTryDecode(error.error) case {'code': final code, 'message': final message}) {
      errorText = '$message - $code';
    } else {
      errorText = error.error;
    }
    final textTheme = context.textTheme;
    return Column(
      spacing: 8.0,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Avatar Generation Failed',
          style: textTheme.headlineSmall,
          textAlign: TextAlign.center,
        ),
        Text(
          'Failed to generate your AI avatar.\nPlease try again later.',
          style: textTheme.bodyMedium,
          textAlign: TextAlign.center,
        ),
        Text(
          '($errorText)',
          style: textTheme.bodySmall?.copyWith(fontSize: 8.0),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildGenerateNewButton(
    BuildContext context,
    AsyncValue<int> quotaResult,
    Map<String, Object?>? rawConfig,
  ) {
    final quota = quotaResult.valueOrNull;

    Color? backgroundColor;
    String? primaryText;
    String? secondaryText;
    if (rawConfig case {'outOfQuota': {'button': final Map<String, Object?> button}} when quota == 0) {
      backgroundColor = button['backgroundColor']?.run((e) {
        if (e is! String || e.length < 6) {
          return null;
        }
        return MEHexColorExtension.fromHex(e);
      });
      primaryText = button['primaryText']?.toString();
      secondaryText = button['secondaryText']?.toString();
    }
    backgroundColor ??= Colors.grey[700];
    primaryText ??= 'Generate New';

    return ThemeTextButton(
      onPressed: () {
        if (quota == null) {
          return;
        }

        if (quota > 0) {
          _pickerKey.currentState?.pickImage(
            allowLocalImage: false,
            onFilePicked: _onImageFilePicked,
          );
          return;
        }

        if (rawConfig case {'outOfQuota': final Map params}) {
          handleNotificationOpenWithPayload(params);
        }
      },
      themeColor: backgroundColor,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(primaryText),
          Flexible(
            child: AutoSizeText.rich(
              TextSpan(
                children: [
                  if (secondaryText case final text?)
                    TextSpan(text: text)
                  else ...[
                    const TextSpan(text: 'Daily limit:'),
                    TextSpan(
                      text: ' ${quota ?? '-'} ',
                      style: TextStyle(
                        color: context.iconTheme.color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const TextSpan(text: 'generations remaining'),
                  ],
                ],
              ),
              style: context.textTheme.bodySmall,
            ),
          ),
          if (quota == null) const AppLoading(size: 16.0, alignment: null),
        ],
      ),
    );
  }
}
