import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/provider/settings.dart' show selectedIndexProvider;
import 'home/card.dart';
import 'home/connection.dart';
import 'home/fun.dart';
import 'home/settings.dart';

final drawerGlobalKeyProvider = Provider<GlobalKey<ScaffoldState>>((ref) {
  return GlobalKey<ScaffoldState>();
});

@FFRoute(name: '/home')
class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      key: ref.watch(drawerGlobalKeyProvider),
      body: const _MainBody(),
      bottomNavigationBar: const _BottomNavBar(),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(selectedIndexProvider);
    final children = [
      const Cards(),
      const Connections(),
      const Fun(),
      const Settings(),
    ];
    return IndexedStack(
      index: index,
      children: children,
    );
  }
}

class _NavItem {
  const _NavItem({
    required this.icon,
    required this.label,
  });

  final SvgGenImage icon;
  final String label;
}

class _BottomNavBar extends ConsumerWidget {
  const _BottomNavBar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selected = ref.watch(selectedIndexProvider);
    final navs = [
      _NavItem(
        icon: Assets.icons.navCards,
        label: context.l10n.labelNavCards,
      ),
      _NavItem(
        icon: Assets.icons.navConnections,
        label: context.l10n.labelNavConnections,
      ),
      _NavItem(
        icon: Assets.icons.navExplore,
        label: context.l10n.labelNavExplore,
      ),
      _NavItem(
        icon: Assets.icons.navSettings,
        label: context.l10n.labelNavSettings,
      ),
    ];

    const borderRadius = BorderRadius.vertical(top: Radius.circular(20.0));
    final bottomPadding = MediaQuery.paddingOf(context).bottom.max(4.0);
    final theme = context.theme;
    final meTheme = context.meTheme;

    return Material(
      color: theme.scaffoldBackgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius,
        side: BorderSide(
          color: theme.dividerColor,
          width: 1.0,
          strokeAlign: BorderSide.strokeAlignOutside,
        ),
      ),
      child: Container(
        height: 64 + bottomPadding + 8,
        padding: EdgeInsets.only(bottom: bottomPadding + 4),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: navs.mapIndexed((index, item) {
            final isSelected = index == selected;
            final color = isSelected ? meTheme.primaryTextColor : theme.dividerColor;
            return Flexible(
              child: SizedBox(
                width: 80.0,
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    AnimatedPositioned(
                      top: 0.0,
                      left: 0.0,
                      right: 0.0,
                      bottom: null,
                      height: isSelected ? 4.0 : 0.0,
                      duration: kThemeAnimationDuration,
                      curve: Curves.easeOutCubic,
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                          borderRadius: RadiusConstants.max,
                          color: meTheme.themeColor,
                        ),
                      ),
                    ),
                    Tapper(
                      onTap: () {
                        ref.read(selectedIndexProvider.notifier).state = index;
                      },
                      behavior: HitTestBehavior.opaque,
                      child: Column(
                        spacing: 3.0,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          item.icon.svg(
                            width: 26.0,
                            height: 26.0,
                            fit: BoxFit.cover,
                            colorFilter: color.filter,
                          ),
                          Flexible(
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: AutoSizeText(
                                item.label,
                                style: context.textTheme.bodySmall?.copyWith(color: color),
                                maxLines: 1,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
