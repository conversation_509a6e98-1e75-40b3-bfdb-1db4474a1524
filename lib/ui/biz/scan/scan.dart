import 'dart:math' as math;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

@FFAutoImport()
import '/feat/scan/uni_qr.dart';

@FFRoute(name: '/scan')
class QRScan extends StatefulWidget {
  const QRScan({
    super.key,
    required this.manager,
  });

  final ScanManager manager;

  @override
  State<QRScan> createState() => QRScanState();
}

class QRScanState extends State<QRScan> {
  final _controller = MobileScannerController(
    detectionSpeed: DetectionSpeed.normal,
    facing: CameraFacing.back,
  );

  List<Widget>? _children;

  @override
  void dispose() {
    _controller.dispose();
    widget.manager.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          MobileScanner(
            controller: _controller,
            onDetect: _handleScanResult,
          ),
          Positioned.fill(
            child: Scanner(
              color: context.themeColor,
              maskColor: context.theme.colorScheme.surface.withValues(
                alpha: 0.58,
              ),
            ),
          ),
          Positioned(
            left: 0.0,
            right: 0.0,
            bottom: 40.0 + context.bottomPadding,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                RippleTap(
                  onTap: _controller.toggleTorch,
                  color: context.meTheme.iconColor,
                  shape: const CircleBorder(),
                  child: SizedBox.square(
                    dimension: 56.0,
                    child: Center(
                      child: ValueListenableBuilder(
                        valueListenable: _controller,
                        builder: (context, state, child) {
                          return Assets.icons.scan.flashlightWhite.svg(
                            width: 20.0,
                            colorFilter: TorchState.on == state.torchState ? context.themeColorFilter : null,
                          );
                        },
                      ),
                    ),
                  ),
                ),
                RippleTap(
                  onTap: () async {
                    final imagePicker = ImagePicker();
                    final xFile = await imagePicker.pickImage(
                      source: ImageSource.gallery,
                      requestFullMetadata: false,
                      imageQuality: 100,
                    );
                    if (xFile == null) {
                      return;
                    }
                    final v = await _controller.analyzeImage(xFile.path);
                    if (v == null) {
                      showToast(context.l10n.textQrCodeNotFound);
                    } else {
                      _handleScanResult(v);
                    }
                  },
                  color: context.meTheme.iconColor,
                  shape: const CircleBorder(),
                  child: SizedBox.square(
                    dimension: 56.0,
                    child: Center(
                      child: Assets.icons.scan.gallery.svg(width: 24.0),
                    ),
                  ),
                ),
              ],
            ),
          ),
          PositionedDirectional(
            start: 24.0,
            top: 10.0 + context.topPadding,
            child: const MEBackButton(
              constraints: BoxConstraints.tightFor(
                width: kMinInteractiveDimension,
                height: kMinInteractiveDimension,
              ),
              padding: EdgeInsets.all(9.0),
            ),
          ),
          ...?_children,
        ],
      ),
    );
  }

  bool _popped = false;

  Future<void> _handleScanResult(BarcodeCapture result) async {
    final ok = await widget.manager.accept(result);
    final state = ok.value.state;
    if (state == ScanState.processing) {
      _children = ok.value.children;
      safeSetState(() {});
    } else if (state == ScanState.processed) {
      if (_popped) {
        return;
      }
      _popped = true;
      if (mounted) {
        Navigator.pop(context, ok);
      }
    }
  }
}

class Scanner extends StatefulWidget {
  const Scanner({
    super.key,
    this.duration = const Duration(milliseconds: 1500),
    required this.color,
    this.rectSize = 240.0,
    this.rectBorderRadius = 24.0,
    this.strokeWidth = 6.0,
    this.scannerHeight = 50.0,
    required this.maskColor,
  });

  final Duration duration;
  final double rectSize;
  final double rectBorderRadius;
  final Color color;
  final double strokeWidth;
  final Color maskColor;
  final double scannerHeight;

  @override
  State<Scanner> createState() => _ScannerState();
}

class _ScannerState extends State<Scanner> with SingleTickerProviderStateMixin {
  late final AnimationController _controller = AnimationController(vsync: this)
    ..duration = widget.duration
    ..addStatusListener((AnimationStatus status) {
      if (status == AnimationStatus.completed) {
        _controller.reset();
      } else if (status == AnimationStatus.dismissed) {
        _controller.forward();
      }
    });

  late final Animation<double> _animation = Tween<double>(
    begin: 0.1,
    end: 0.9,
  ).animate(_controller);

  @override
  void initState() {
    _controller.forward();
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) => CustomPaint(
        painter: ScannerPainter(
          color: widget.color,
          maskColor: widget.maskColor,
          rectSize: widget.rectSize,
          rectBorderRadius: widget.rectBorderRadius,
          strokeWidth: widget.strokeWidth,
          scannerHeight: widget.scannerHeight,
          scannerRatio: _animation.value,
        ),
      ),
    );
  }
}

class ScannerPainter extends CustomPainter {
  ScannerPainter({
    this.rectSize = 240.0,
    this.rectBorderRadius = 24.0,
    this.strokeWidth = 6.0,
    this.scannerHeight = 50.0,
    this.scannerRatio = 50.0,
    required this.color,
    required this.maskColor,
  });

  final double rectSize;
  final double rectBorderRadius;
  final Color color;
  final double strokeWidth;
  final Color maskColor;
  final double scannerHeight;
  final double scannerRatio;

  @override
  void paint(Canvas canvas, Size size) {
    final corner = rectBorderRadius * 2;
    // draw mask
    final maskPaint = Paint()
      ..color = maskColor
      ..style = PaintingStyle.fill;

    final maskPath = Path.combine(
      PathOperation.difference,
      Path()..addRect(Rect.fromLTWH(0.0, 0.0, size.width, size.height)),
      Path()..addRRect(
        RRect.fromRectAndRadius(
          Rect.fromCenter(
            center: Offset(size.width / 2.0, size.height / 2.0),
            width: rectSize,
            height: rectSize,
          ),
          Radius.circular(rectBorderRadius),
        ),
      ),
    );

    canvas.drawPath(maskPath, maskPaint);

    final tl = Offset(
      (size.width - rectSize) / 2.0,
      (size.height - rectSize) / 2.0,
    );
    final tr = Offset(
      (size.width - rectSize) / 2.0 + rectSize - corner,
      (size.height - rectSize) / 2.0,
    );
    final bl = Offset(
      (size.width - rectSize) / 2.0,
      (size.height - rectSize) / 2.0 + rectSize - corner,
    );
    final br = Offset(
      (size.width - rectSize) / 2.0 + rectSize - corner,
      (size.height - rectSize) / 2.0 + rectSize - corner,
    );
    final cornerPaint = Paint()
      ..isAntiAlias = true
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;
    final cornerSize = Size(corner, corner);
    canvas.drawArc(
      tl & cornerSize,
      -math.pi,
      math.pi / 2,
      false,
      cornerPaint,
    );
    canvas.drawArc(
      bl & cornerSize,
      math.pi / 2,
      math.pi / 2,
      false,
      cornerPaint,
    );
    canvas.drawArc(
      tr & cornerSize,
      -math.pi / 2,
      math.pi / 2,
      false,
      cornerPaint,
    );
    canvas.drawArc(
      br & cornerSize,
      math.pi / 2,
      -math.pi / 2,
      false,
      cornerPaint,
    );
    final paint = Paint()..isAntiAlias = true;
    final rect =
        Offset(
          (size.width - rectSize) / 2.0 + 16.0,
          (size.height - rectSize) / 2.0 + rectSize * scannerRatio,
        ) &
        Size(rectSize - 32.0, scannerHeight * (1.0 - scannerRatio));
    paint.shader = LinearGradient(
      colors: <Color>[
        color.withValues(alpha: .05),
        color.withValues(alpha: 1.0 - scannerRatio),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ).createShader(rect);
    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(covariant ScannerPainter oldDelegate) {
    return !(rectSize == oldDelegate.rectSize &&
        rectBorderRadius == oldDelegate.rectBorderRadius &&
        color == oldDelegate.color &&
        strokeWidth == oldDelegate.strokeWidth &&
        maskColor == oldDelegate.maskColor &&
        scannerHeight == oldDelegate.scannerHeight &&
        scannerRatio == oldDelegate.scannerRatio);
  }
}
