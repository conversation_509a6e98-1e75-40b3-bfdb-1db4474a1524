// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'token.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchRawWalletPortfolioOKXHash() =>
    r'6053cfc80eb7fd953fb2643291bd7202882d200c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchRawWalletPortfolioOKX].
@ProviderFor(fetchRawWalletPortfolioOKX)
const fetchRawWalletPortfolioOKXProvider = FetchRawWalletPortfolioOKXFamily();

/// See also [fetchRawWalletPortfolioOKX].
class FetchRawWalletPortfolioOKXFamily
    extends Family<AsyncValue<WalletPortfolioOKX2>> {
  /// See also [fetchRawWalletPortfolioOKX].
  const FetchRawWalletPortfolioOKXFamily();

  /// See also [fetchRawWalletPortfolioOKX].
  FetchRawWalletPortfolioOKXProvider call({
    required Network? network,
    required String? address,
    bool excludeRiskType = true,
    bool writeToCache = true,
  }) {
    return FetchRawWalletPortfolioOKXProvider(
      network: network,
      address: address,
      excludeRiskType: excludeRiskType,
      writeToCache: writeToCache,
    );
  }

  @override
  FetchRawWalletPortfolioOKXProvider getProviderOverride(
    covariant FetchRawWalletPortfolioOKXProvider provider,
  ) {
    return call(
      network: provider.network,
      address: provider.address,
      excludeRiskType: provider.excludeRiskType,
      writeToCache: provider.writeToCache,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchRawWalletPortfolioOKXProvider';
}

/// See also [fetchRawWalletPortfolioOKX].
class FetchRawWalletPortfolioOKXProvider
    extends AutoDisposeFutureProvider<WalletPortfolioOKX2> {
  /// See also [fetchRawWalletPortfolioOKX].
  FetchRawWalletPortfolioOKXProvider({
    required Network? network,
    required String? address,
    bool excludeRiskType = true,
    bool writeToCache = true,
  }) : this._internal(
         (ref) => fetchRawWalletPortfolioOKX(
           ref as FetchRawWalletPortfolioOKXRef,
           network: network,
           address: address,
           excludeRiskType: excludeRiskType,
           writeToCache: writeToCache,
         ),
         from: fetchRawWalletPortfolioOKXProvider,
         name: r'fetchRawWalletPortfolioOKXProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$fetchRawWalletPortfolioOKXHash,
         dependencies: FetchRawWalletPortfolioOKXFamily._dependencies,
         allTransitiveDependencies:
             FetchRawWalletPortfolioOKXFamily._allTransitiveDependencies,
         network: network,
         address: address,
         excludeRiskType: excludeRiskType,
         writeToCache: writeToCache,
       );

  FetchRawWalletPortfolioOKXProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.network,
    required this.address,
    required this.excludeRiskType,
    required this.writeToCache,
  }) : super.internal();

  final Network? network;
  final String? address;
  final bool excludeRiskType;
  final bool writeToCache;

  @override
  Override overrideWith(
    FutureOr<WalletPortfolioOKX2> Function(
      FetchRawWalletPortfolioOKXRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchRawWalletPortfolioOKXProvider._internal(
        (ref) => create(ref as FetchRawWalletPortfolioOKXRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        network: network,
        address: address,
        excludeRiskType: excludeRiskType,
        writeToCache: writeToCache,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<WalletPortfolioOKX2> createElement() {
    return _FetchRawWalletPortfolioOKXProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchRawWalletPortfolioOKXProvider &&
        other.network == network &&
        other.address == address &&
        other.excludeRiskType == excludeRiskType &&
        other.writeToCache == writeToCache;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, network.hashCode);
    hash = _SystemHash.combine(hash, address.hashCode);
    hash = _SystemHash.combine(hash, excludeRiskType.hashCode);
    hash = _SystemHash.combine(hash, writeToCache.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchRawWalletPortfolioOKXRef
    on AutoDisposeFutureProviderRef<WalletPortfolioOKX2> {
  /// The parameter `network` of this provider.
  Network? get network;

  /// The parameter `address` of this provider.
  String? get address;

  /// The parameter `excludeRiskType` of this provider.
  bool get excludeRiskType;

  /// The parameter `writeToCache` of this provider.
  bool get writeToCache;
}

class _FetchRawWalletPortfolioOKXProviderElement
    extends AutoDisposeFutureProviderElement<WalletPortfolioOKX2>
    with FetchRawWalletPortfolioOKXRef {
  _FetchRawWalletPortfolioOKXProviderElement(super.provider);

  @override
  Network? get network =>
      (origin as FetchRawWalletPortfolioOKXProvider).network;
  @override
  String? get address => (origin as FetchRawWalletPortfolioOKXProvider).address;
  @override
  bool get excludeRiskType =>
      (origin as FetchRawWalletPortfolioOKXProvider).excludeRiskType;
  @override
  bool get writeToCache =>
      (origin as FetchRawWalletPortfolioOKXProvider).writeToCache;
}

String _$fetchRawWalletPortfolioAstroxHash() =>
    r'2de9064f125f3e3d7a53f83b80dbef1dd3ab45f5';

/// See also [fetchRawWalletPortfolioAstrox].
@ProviderFor(fetchRawWalletPortfolioAstrox)
const fetchRawWalletPortfolioAstroxProvider =
    FetchRawWalletPortfolioAstroxFamily();

/// See also [fetchRawWalletPortfolioAstrox].
class FetchRawWalletPortfolioAstroxFamily
    extends Family<AsyncValue<WalletPortfolioAstrox>> {
  /// See also [fetchRawWalletPortfolioAstrox].
  const FetchRawWalletPortfolioAstroxFamily();

  /// See also [fetchRawWalletPortfolioAstrox].
  FetchRawWalletPortfolioAstroxProvider call({
    required Network? network,
    required String? address,
    bool writeToCache = true,
  }) {
    return FetchRawWalletPortfolioAstroxProvider(
      network: network,
      address: address,
      writeToCache: writeToCache,
    );
  }

  @override
  FetchRawWalletPortfolioAstroxProvider getProviderOverride(
    covariant FetchRawWalletPortfolioAstroxProvider provider,
  ) {
    return call(
      network: provider.network,
      address: provider.address,
      writeToCache: provider.writeToCache,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchRawWalletPortfolioAstroxProvider';
}

/// See also [fetchRawWalletPortfolioAstrox].
class FetchRawWalletPortfolioAstroxProvider
    extends AutoDisposeFutureProvider<WalletPortfolioAstrox> {
  /// See also [fetchRawWalletPortfolioAstrox].
  FetchRawWalletPortfolioAstroxProvider({
    required Network? network,
    required String? address,
    bool writeToCache = true,
  }) : this._internal(
         (ref) => fetchRawWalletPortfolioAstrox(
           ref as FetchRawWalletPortfolioAstroxRef,
           network: network,
           address: address,
           writeToCache: writeToCache,
         ),
         from: fetchRawWalletPortfolioAstroxProvider,
         name: r'fetchRawWalletPortfolioAstroxProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$fetchRawWalletPortfolioAstroxHash,
         dependencies: FetchRawWalletPortfolioAstroxFamily._dependencies,
         allTransitiveDependencies:
             FetchRawWalletPortfolioAstroxFamily._allTransitiveDependencies,
         network: network,
         address: address,
         writeToCache: writeToCache,
       );

  FetchRawWalletPortfolioAstroxProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.network,
    required this.address,
    required this.writeToCache,
  }) : super.internal();

  final Network? network;
  final String? address;
  final bool writeToCache;

  @override
  Override overrideWith(
    FutureOr<WalletPortfolioAstrox> Function(
      FetchRawWalletPortfolioAstroxRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchRawWalletPortfolioAstroxProvider._internal(
        (ref) => create(ref as FetchRawWalletPortfolioAstroxRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        network: network,
        address: address,
        writeToCache: writeToCache,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<WalletPortfolioAstrox> createElement() {
    return _FetchRawWalletPortfolioAstroxProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchRawWalletPortfolioAstroxProvider &&
        other.network == network &&
        other.address == address &&
        other.writeToCache == writeToCache;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, network.hashCode);
    hash = _SystemHash.combine(hash, address.hashCode);
    hash = _SystemHash.combine(hash, writeToCache.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchRawWalletPortfolioAstroxRef
    on AutoDisposeFutureProviderRef<WalletPortfolioAstrox> {
  /// The parameter `network` of this provider.
  Network? get network;

  /// The parameter `address` of this provider.
  String? get address;

  /// The parameter `writeToCache` of this provider.
  bool get writeToCache;
}

class _FetchRawWalletPortfolioAstroxProviderElement
    extends AutoDisposeFutureProviderElement<WalletPortfolioAstrox>
    with FetchRawWalletPortfolioAstroxRef {
  _FetchRawWalletPortfolioAstroxProviderElement(super.provider);

  @override
  Network? get network =>
      (origin as FetchRawWalletPortfolioAstroxProvider).network;
  @override
  String? get address =>
      (origin as FetchRawWalletPortfolioAstroxProvider).address;
  @override
  bool get writeToCache =>
      (origin as FetchRawWalletPortfolioAstroxProvider).writeToCache;
}

String _$fetchWalletPortfolioHash() =>
    r'21481148bc190455a304c49ff38a18f10f7dee56';

/// See also [fetchWalletPortfolio].
@ProviderFor(fetchWalletPortfolio)
final fetchWalletPortfolioProvider =
    AutoDisposeStreamProvider<WalletPortfolio>.internal(
      fetchWalletPortfolio,
      name: r'fetchWalletPortfolioProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchWalletPortfolioHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchWalletPortfolioRef = AutoDisposeStreamProviderRef<WalletPortfolio>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
