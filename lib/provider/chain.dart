import 'package:flutter_riverpod/flutter_riverpod.dart';

import '/internals/box.dart' show Boxes, BoxKeys, BoxService;
import '/internals/methods.dart' show handleExceptions;
import '/internals/privy.dart' show privyClient, AuthStateExtension;
import '/models/business.dart';
import '/services/chain_manager.dart';
import 'settings.dart' show settingsProvider;
import 'token.dart' show balanceDiffProvider, refreshTokenBalances;

// 当前钱包地址提供者，依赖于当前网络
final walletAddressProvider = Provider.autoDispose<String?>((ref) {
  ref.watch(balanceDiffProvider);
  ref.watch(networkProvider);
  return ChainManager.instance.walletAddress;
});

final networkProvider = Provider<Network>(
  (ref) => ref.watch(settingsProvider).selectedNetwork,
  dependencies: [settingsProvider],
);
final networksProvider = Provider<List<Network>>(
  (ref) {
    final stream = BoxService.watch(box: Boxes.settings, key: BoxKeys.networks);
    final sub = stream.listen((event) {
      ref.invalidateSelf();
    });
    ref.onDispose(() {
      sub.cancel();
    });
    return BoxService.getNetworksFromLocal();
  },
  dependencies: [settingsProvider],
);
final networkByNameProvider = Provider.family<Network, String>(
  (_, name) => BoxService.getNetworkByName(name),
);
final networkByNameOrNullProvider = Provider.family<Network?, String?>(
  (_, name) => BoxService.getNetworkByNameOrNull(name),
);
final networkByIdOKXProvider = Provider.family<Network, String>(
  (_, id) => BoxService.getNetworkByIdOKX(id),
);

final walletCreateProvider = FutureProvider.family<void, String>(
  (ref, privyUserId) async {
    final state = await privyClient.getAuthState();
    final user = state.user;
    if (user == null || user.id != privyUserId) {
      return;
    }

    Future<void> onDone() async {
      await ChainManager.instance.initialize();
      refreshTokenBalances(ref);
    }

    const maxAttempts = 3;
    int attempts = 0;
    while (attempts < maxAttempts) {
      attempts++;

      if (user.embeddedEthereumWallets.isNotEmpty && user.embeddedSolanaWallets.isNotEmpty) {
        await onDone();
        return;
      }

      try {
        if (user.embeddedEthereumWallets.isEmpty) {
          await user.createEthereumWallet();
          await user.refresh();
        }
        if (user.embeddedEthereumWallets.isEmpty) {
          throw Exception('Creating Ethereum wallet failed for $privyUserId ($attempts)');
        }

        if (user.embeddedSolanaWallets.isEmpty) {
          await user.createSolanaWallet();
          await user.refresh();
        }
        if (user.embeddedSolanaWallets.isEmpty) {
          throw Exception('Creating Solana wallet failed for $privyUserId ($attempts)');
        }

        await onDone();
        return;
      } catch (e, s) {
        if (attempts >= maxAttempts) {
          handleExceptions(
            error: 'Creating wallet failed for $privyUserId after $maxAttempts attempts: $e',
            stackTrace: s,
            tag: 'PrivyWalletCreateProvider(attempts: $maxAttempts)',
            tagWithTrace: false,
          );
          return;
        }
        await Future.delayed(const Duration(seconds: 2));
      }
    }
  },
);
