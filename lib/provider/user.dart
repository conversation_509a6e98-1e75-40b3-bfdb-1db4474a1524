import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/constants/envs.dart' as envs show EnvOverrides;
import '/internals/box.dart' show BoxService;
import '/internals/methods.dart';
import '/models/user.dart';
import 'api.dart' show Paged, RefExtension, apiServiceProvider;

part 'user.g.dart';

@riverpod
class PersistentTokenRepo extends _$PersistentTokenRepo {
  /// Overrides runtime token.
  String? get _override => envs.EnvOverrides.jwtOverrode;

  Timer? _refreshTimer;

  @override
  String? build() {
    if (_override != null) {
      return _override;
    }
    if (!_checkRefreshTimer()) {
      return null;
    }
    return _latest;
  }

  String? get _latest => _override ?? BoxService.getToken();

  (bool, Duration) _checkExpired(String token) {
    final decoded = json.decode(
      utf8.decode(
        base64Url.decode(base64Url.normalize(token.split('.')[1])),
      ),
    );

    final expired = DateTime.fromMillisecondsSinceEpoch(
      (decoded['exp'] as int) * 1000,
    );
    final now = DateTime.now();

    return (expired.isBefore(now), expired.difference(now));
  }

  /// Returns whether the token is valid.
  bool _checkRefreshTimer() {
    _refreshTimer?.cancel();

    final token = _latest;
    if (token == null || token.isEmpty) {
      return false;
    }

    try {
      final (hasExpired, difference) = _checkExpired(token);

      if (hasExpired) {
        return false;
      }

      if (difference >= const Duration(minutes: 1)) {
        _refreshTimer = Timer(difference ~/ 2, refresh);
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
    }
    return true;
  }

  Future<bool> refresh() async {
    if (_override != null) {
      final (expired, _) = _checkExpired(_override!);
      return !expired;
    }

    if (!_checkRefreshTimer()) {
      return false;
    }

    final newToken = await ref.read(apiServiceProvider).getRefreshJWT();
    await update(newToken);
    return true;
  }

  Future<void> update(String token) async {
    if (_override != null) {
      return;
    }
    state = await BoxService.updateToken(token);
    _checkRefreshTimer();
  }
}

@Riverpod(keepAlive: true)
final class UserRepo extends _$UserRepo {
  @override
  UserInfo? build() => null;

  void _refresh() {
    state = BoxService.getUserInfo();
  }

  Future<void> update(UserInfo user) async {
    await BoxService.updateUser(user);
    _refresh();
  }

  Future<void> reset() async {
    await BoxService.clearUserBox();
    _refresh();
  }
}

/// Stores the referral code from which the user came from.
final userReferralFromProvider = StateProvider<UserReferralRequest>(
  (ref) => UserReferralRequest.empty,
);

@riverpod
Future<UserInfo> fetchUserInfo(
  Ref ref, {
  String? code,
}) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });

  if (code != null) {
    return ref.read(apiServiceProvider).getPublicProfile(code: code, cancelToken: ct);
  } else {
    final user = await ref.watch(apiServiceProvider).getSelfUserInfo(cancelToken: ct);
    if (ref.read(userRepoProvider) case final local? when local != user) {
      await ref.read(userRepoProvider.notifier).update(user);
    }
    return user;
  }
}

@riverpod
Future<(UserRelation relation, String referralCode)?> fetchUserRelation(
  Ref ref, {
  required String? code,
}) async {
  final userCode = ref.watch(userRepoProvider)?.referralCode;
  code = code?.trim();
  if (code == null || code.isEmpty || userCode == null || userCode.isEmpty || code == userCode) {
    return null;
  }
  final user = await ref.watch(fetchUserInfoProvider(code: code).future);
  final referralCode = user.referralCode;
  final relation = await ref.read(apiServiceProvider).getUserRelation(referralCode: referralCode);
  return (relation, referralCode);
}

@riverpod
Future<Paged<UserFromRelation>> fetchUsersFromRelation(
  Ref ref, {
  required UserFromRelationType type,
  required int page,
}) {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  switch (type) {
    case UserFromRelationType.following:
      return ref.read(apiServiceProvider).getFollowingList(page: page, cancelToken: ct);
    case UserFromRelationType.follower:
      return ref.read(apiServiceProvider).getFollowerList(page: page, cancelToken: ct);
  }
}
