// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'card.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchMyCardsHash() => r'3593954662c42ce5345e873b1a8be857f2f3b645';

/// See also [fetchMyCards].
@ProviderFor(fetchMyCards)
final fetchMyCardsProvider = AutoDisposeFutureProvider<List<CardInfo>>.internal(
  fetchMyCards,
  name: r'fetchMyCardsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMyCardsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchMyCardsRef = AutoDisposeFutureProviderRef<List<CardInfo>>;
String _$fetchPublicCardHash() => r'ccef701e224652f5c0930a1fbda73bf65508de75';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchPublicCard].
@ProviderFor(fetchPublicCard)
const fetchPublicCardProvider = FetchPublicCardFamily();

/// See also [fetchPublicCard].
class FetchPublicCardFamily extends Family<AsyncValue<CardInfo>> {
  /// See also [fetchPublicCard].
  const FetchPublicCardFamily();

  /// See also [fetchPublicCard].
  FetchPublicCardProvider call({required String code}) {
    return FetchPublicCardProvider(code: code);
  }

  @override
  FetchPublicCardProvider getProviderOverride(
    covariant FetchPublicCardProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchPublicCardProvider';
}

/// See also [fetchPublicCard].
class FetchPublicCardProvider extends AutoDisposeFutureProvider<CardInfo> {
  /// See also [fetchPublicCard].
  FetchPublicCardProvider({required String code})
    : this._internal(
        (ref) => fetchPublicCard(ref as FetchPublicCardRef, code: code),
        from: fetchPublicCardProvider,
        name: r'fetchPublicCardProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchPublicCardHash,
        dependencies: FetchPublicCardFamily._dependencies,
        allTransitiveDependencies:
            FetchPublicCardFamily._allTransitiveDependencies,
        code: code,
      );

  FetchPublicCardProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String code;

  @override
  Override overrideWith(
    FutureOr<CardInfo> Function(FetchPublicCardRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchPublicCardProvider._internal(
        (ref) => create(ref as FetchPublicCardRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CardInfo> createElement() {
    return _FetchPublicCardProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchPublicCardProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchPublicCardRef on AutoDisposeFutureProviderRef<CardInfo> {
  /// The parameter `code` of this provider.
  String get code;
}

class _FetchPublicCardProviderElement
    extends AutoDisposeFutureProviderElement<CardInfo>
    with FetchPublicCardRef {
  _FetchPublicCardProviderElement(super.provider);

  @override
  String get code => (origin as FetchPublicCardProvider).code;
}

String _$fetchPublicProfileHash() =>
    r'82d1430b0818ebea615a04adecc5de0cec239c71';

/// See also [fetchPublicProfile].
@ProviderFor(fetchPublicProfile)
const fetchPublicProfileProvider = FetchPublicProfileFamily();

/// See also [fetchPublicProfile].
class FetchPublicProfileFamily extends Family<AsyncValue<UserInfo>> {
  /// See also [fetchPublicProfile].
  const FetchPublicProfileFamily();

  /// See also [fetchPublicProfile].
  FetchPublicProfileProvider call({required String code}) {
    return FetchPublicProfileProvider(code: code);
  }

  @override
  FetchPublicProfileProvider getProviderOverride(
    covariant FetchPublicProfileProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchPublicProfileProvider';
}

/// See also [fetchPublicProfile].
class FetchPublicProfileProvider extends AutoDisposeFutureProvider<UserInfo> {
  /// See also [fetchPublicProfile].
  FetchPublicProfileProvider({required String code})
    : this._internal(
        (ref) => fetchPublicProfile(ref as FetchPublicProfileRef, code: code),
        from: fetchPublicProfileProvider,
        name: r'fetchPublicProfileProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchPublicProfileHash,
        dependencies: FetchPublicProfileFamily._dependencies,
        allTransitiveDependencies:
            FetchPublicProfileFamily._allTransitiveDependencies,
        code: code,
      );

  FetchPublicProfileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String code;

  @override
  Override overrideWith(
    FutureOr<UserInfo> Function(FetchPublicProfileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchPublicProfileProvider._internal(
        (ref) => create(ref as FetchPublicProfileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<UserInfo> createElement() {
    return _FetchPublicProfileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchPublicProfileProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchPublicProfileRef on AutoDisposeFutureProviderRef<UserInfo> {
  /// The parameter `code` of this provider.
  String get code;
}

class _FetchPublicProfileProviderElement
    extends AutoDisposeFutureProviderElement<UserInfo>
    with FetchPublicProfileRef {
  _FetchPublicProfileProviderElement(super.provider);

  @override
  String get code => (origin as FetchPublicProfileProvider).code;
}

String _$fetchSocialsHash() => r'df9c3b5c5b4ab34886b816c99d7ce1e469ce01e0';

/// See also [fetchSocials].
@ProviderFor(fetchSocials)
const fetchSocialsProvider = FetchSocialsFamily();

/// See also [fetchSocials].
class FetchSocialsFamily extends Family<AsyncValue<List<Social>>> {
  /// See also [fetchSocials].
  const FetchSocialsFamily();

  /// See also [fetchSocials].
  FetchSocialsProvider call({String? code}) {
    return FetchSocialsProvider(code: code);
  }

  @override
  FetchSocialsProvider getProviderOverride(
    covariant FetchSocialsProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchSocialsProvider';
}

/// See also [fetchSocials].
class FetchSocialsProvider extends AutoDisposeFutureProvider<List<Social>> {
  /// See also [fetchSocials].
  FetchSocialsProvider({String? code})
    : this._internal(
        (ref) => fetchSocials(ref as FetchSocialsRef, code: code),
        from: fetchSocialsProvider,
        name: r'fetchSocialsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchSocialsHash,
        dependencies: FetchSocialsFamily._dependencies,
        allTransitiveDependencies:
            FetchSocialsFamily._allTransitiveDependencies,
        code: code,
      );

  FetchSocialsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String? code;

  @override
  Override overrideWith(
    FutureOr<List<Social>> Function(FetchSocialsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchSocialsProvider._internal(
        (ref) => create(ref as FetchSocialsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Social>> createElement() {
    return _FetchSocialsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchSocialsProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchSocialsRef on AutoDisposeFutureProviderRef<List<Social>> {
  /// The parameter `code` of this provider.
  String? get code;
}

class _FetchSocialsProviderElement
    extends AutoDisposeFutureProviderElement<List<Social>>
    with FetchSocialsRef {
  _FetchSocialsProviderElement(super.provider);

  @override
  String? get code => (origin as FetchSocialsProvider).code;
}

String _$fetchExtendProfileTopicsHash() =>
    r'f921b3f3ca0c23c1a539bff84a39b45df90b079b';

/// See also [fetchExtendProfileTopics].
@ProviderFor(fetchExtendProfileTopics)
final fetchExtendProfileTopicsProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      fetchExtendProfileTopics,
      name: r'fetchExtendProfileTopicsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchExtendProfileTopicsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchExtendProfileTopicsRef =
    AutoDisposeFutureProviderRef<List<String>>;
String _$fetchExtendProfileRolesHash() =>
    r'f32d571eac880e057d655db0be11bf27baa4b150';

/// See also [fetchExtendProfileRoles].
@ProviderFor(fetchExtendProfileRoles)
final fetchExtendProfileRolesProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      fetchExtendProfileRoles,
      name: r'fetchExtendProfileRolesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchExtendProfileRolesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchExtendProfileRolesRef = AutoDisposeFutureProviderRef<List<String>>;
String _$fetchExtendProfileHash() =>
    r'7b97b6013c797997cfb2492f6c1cb26a9ae1dc29';

/// See also [fetchExtendProfile].
@ProviderFor(fetchExtendProfile)
const fetchExtendProfileProvider = FetchExtendProfileFamily();

/// See also [fetchExtendProfile].
class FetchExtendProfileFamily extends Family<AsyncValue<ExtendProfile>> {
  /// See also [fetchExtendProfile].
  const FetchExtendProfileFamily();

  /// See also [fetchExtendProfile].
  FetchExtendProfileProvider call({String? code}) {
    return FetchExtendProfileProvider(code: code);
  }

  @override
  FetchExtendProfileProvider getProviderOverride(
    covariant FetchExtendProfileProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchExtendProfileProvider';
}

/// See also [fetchExtendProfile].
class FetchExtendProfileProvider
    extends AutoDisposeFutureProvider<ExtendProfile> {
  /// See also [fetchExtendProfile].
  FetchExtendProfileProvider({String? code})
    : this._internal(
        (ref) => fetchExtendProfile(ref as FetchExtendProfileRef, code: code),
        from: fetchExtendProfileProvider,
        name: r'fetchExtendProfileProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchExtendProfileHash,
        dependencies: FetchExtendProfileFamily._dependencies,
        allTransitiveDependencies:
            FetchExtendProfileFamily._allTransitiveDependencies,
        code: code,
      );

  FetchExtendProfileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String? code;

  @override
  Override overrideWith(
    FutureOr<ExtendProfile> Function(FetchExtendProfileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchExtendProfileProvider._internal(
        (ref) => create(ref as FetchExtendProfileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ExtendProfile> createElement() {
    return _FetchExtendProfileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchExtendProfileProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchExtendProfileRef on AutoDisposeFutureProviderRef<ExtendProfile> {
  /// The parameter `code` of this provider.
  String? get code;
}

class _FetchExtendProfileProviderElement
    extends AutoDisposeFutureProviderElement<ExtendProfile>
    with FetchExtendProfileRef {
  _FetchExtendProfileProviderElement(super.provider);

  @override
  String? get code => (origin as FetchExtendProfileProvider).code;
}

String _$fetchGitHubContributionsHash() =>
    r'30d4ff3e8210af34eb904b303e7baf2fd3e425fb';

/// See also [fetchGitHubContributions].
@ProviderFor(fetchGitHubContributions)
const fetchGitHubContributionsProvider = FetchGitHubContributionsFamily();

/// See also [fetchGitHubContributions].
class FetchGitHubContributionsFamily
    extends Family<AsyncValue<FrontEndGitHubContributionCollection?>> {
  /// See also [fetchGitHubContributions].
  const FetchGitHubContributionsFamily();

  /// See also [fetchGitHubContributions].
  FetchGitHubContributionsProvider call({required String handle}) {
    return FetchGitHubContributionsProvider(handle: handle);
  }

  @override
  FetchGitHubContributionsProvider getProviderOverride(
    covariant FetchGitHubContributionsProvider provider,
  ) {
    return call(handle: provider.handle);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchGitHubContributionsProvider';
}

/// See also [fetchGitHubContributions].
class FetchGitHubContributionsProvider
    extends AutoDisposeFutureProvider<FrontEndGitHubContributionCollection?> {
  /// See also [fetchGitHubContributions].
  FetchGitHubContributionsProvider({required String handle})
    : this._internal(
        (ref) => fetchGitHubContributions(
          ref as FetchGitHubContributionsRef,
          handle: handle,
        ),
        from: fetchGitHubContributionsProvider,
        name: r'fetchGitHubContributionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchGitHubContributionsHash,
        dependencies: FetchGitHubContributionsFamily._dependencies,
        allTransitiveDependencies:
            FetchGitHubContributionsFamily._allTransitiveDependencies,
        handle: handle,
      );

  FetchGitHubContributionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.handle,
  }) : super.internal();

  final String handle;

  @override
  Override overrideWith(
    FutureOr<FrontEndGitHubContributionCollection?> Function(
      FetchGitHubContributionsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchGitHubContributionsProvider._internal(
        (ref) => create(ref as FetchGitHubContributionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        handle: handle,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<FrontEndGitHubContributionCollection?>
  createElement() {
    return _FetchGitHubContributionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchGitHubContributionsProvider && other.handle == handle;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, handle.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchGitHubContributionsRef
    on AutoDisposeFutureProviderRef<FrontEndGitHubContributionCollection?> {
  /// The parameter `handle` of this provider.
  String get handle;
}

class _FetchGitHubContributionsProviderElement
    extends
        AutoDisposeFutureProviderElement<FrontEndGitHubContributionCollection?>
    with FetchGitHubContributionsRef {
  _FetchGitHubContributionsProviderElement(super.provider);

  @override
  String get handle => (origin as FetchGitHubContributionsProvider).handle;
}

String _$fetchCustomizeCardOrdersHash() =>
    r'd4682d7b2132f10c48b50ad02f60e5c1a79c445b';

/// See also [fetchCustomizeCardOrders].
@ProviderFor(fetchCustomizeCardOrders)
const fetchCustomizeCardOrdersProvider = FetchCustomizeCardOrdersFamily();

/// See also [fetchCustomizeCardOrders].
class FetchCustomizeCardOrdersFamily
    extends Family<AsyncValue<Paged<CustomizeCardOrder>>> {
  /// See also [fetchCustomizeCardOrders].
  const FetchCustomizeCardOrdersFamily();

  /// See also [fetchCustomizeCardOrders].
  FetchCustomizeCardOrdersProvider call({required int page}) {
    return FetchCustomizeCardOrdersProvider(page: page);
  }

  @override
  FetchCustomizeCardOrdersProvider getProviderOverride(
    covariant FetchCustomizeCardOrdersProvider provider,
  ) {
    return call(page: provider.page);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchCustomizeCardOrdersProvider';
}

/// See also [fetchCustomizeCardOrders].
class FetchCustomizeCardOrdersProvider
    extends AutoDisposeFutureProvider<Paged<CustomizeCardOrder>> {
  /// See also [fetchCustomizeCardOrders].
  FetchCustomizeCardOrdersProvider({required int page})
    : this._internal(
        (ref) => fetchCustomizeCardOrders(
          ref as FetchCustomizeCardOrdersRef,
          page: page,
        ),
        from: fetchCustomizeCardOrdersProvider,
        name: r'fetchCustomizeCardOrdersProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchCustomizeCardOrdersHash,
        dependencies: FetchCustomizeCardOrdersFamily._dependencies,
        allTransitiveDependencies:
            FetchCustomizeCardOrdersFamily._allTransitiveDependencies,
        page: page,
      );

  FetchCustomizeCardOrdersProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.page,
  }) : super.internal();

  final int page;

  @override
  Override overrideWith(
    FutureOr<Paged<CustomizeCardOrder>> Function(
      FetchCustomizeCardOrdersRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchCustomizeCardOrdersProvider._internal(
        (ref) => create(ref as FetchCustomizeCardOrdersRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        page: page,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Paged<CustomizeCardOrder>> createElement() {
    return _FetchCustomizeCardOrdersProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchCustomizeCardOrdersProvider && other.page == page;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchCustomizeCardOrdersRef
    on AutoDisposeFutureProviderRef<Paged<CustomizeCardOrder>> {
  /// The parameter `page` of this provider.
  int get page;
}

class _FetchCustomizeCardOrdersProviderElement
    extends AutoDisposeFutureProviderElement<Paged<CustomizeCardOrder>>
    with FetchCustomizeCardOrdersRef {
  _FetchCustomizeCardOrdersProviderElement(super.provider);

  @override
  int get page => (origin as FetchCustomizeCardOrdersProvider).page;
}

String _$isActiveGuideHash() => r'a4b5688bd7c86613371a632c0598a82772e188e2';

/// See also [isActiveGuide].
@ProviderFor(isActiveGuide)
final isActiveGuideProvider = AutoDisposeProvider<bool?>.internal(
  isActiveGuide,
  name: r'isActiveGuideProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isActiveGuideHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsActiveGuideRef = AutoDisposeProviderRef<bool?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
