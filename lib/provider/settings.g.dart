// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localCacheHash() => r'8dbcefab0985b98f566041ee34fe214e91387118';

/// See also [LocalCache].
@ProviderFor(LocalCache)
final localCacheProvider =
    AutoDisposeAsyncNotifierProvider<LocalCache, String>.internal(
      LocalCache.new,
      name: r'localCacheProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$localCacheHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$LocalCache = AutoDisposeAsyncNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
