import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/api/front_end.dart';
import '/internals/box.dart' show Boxes;
import '/internals/methods.dart' show handleExceptions;
import '/models/card.dart';
import '/models/user.dart' show UserInfo;
import '/provider/api.dart';
import 'business.dart' show configProvider;

part 'card.g.dart';

@riverpod
Future<List<CardInfo>> fetchMyCards(
  Ref ref,
) {
  return ref.read(apiServiceProvider).getMyCards();
}

@riverpod
Future<CardInfo> fetchPublicCard(
  Ref ref, {
  required String code,
}) {
  return ref.read(apiServiceProvider).getCard(cardCode: code);
}

@riverpod
Future<UserInfo> fetchPublicProfile(
  Ref ref, {
  required String code,
}) {
  return ref.read(apiServiceProvider).getPublicProfile(code: code);
}

@riverpod
Future<List<Social>> fetchSocials(
  Ref ref, {
  String? code,
}) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  final List<Social> list;
  if (code == null) {
    list = await ref.read(apiServiceProvider).socialQuery(cancelToken: ct);
  } else {
    list = await ref.read(apiServiceProvider).getPublicSocials(code: code, cancelToken: ct);
  }
  return list.where((e) => SocialPlatform.fromName(e.platformName) != null).toList();
}

@riverpod
Future<List<String>> fetchExtendProfileTopics(
  Ref ref,
) {
  return ref.read(apiServiceProvider).getExtendProfileTopicsAll();
}

@riverpod
Future<List<String>> fetchExtendProfileRoles(
  Ref ref,
) {
  return ref.read(apiServiceProvider).getExtendProfileRolesAll();
}

@riverpod
Future<ExtendProfile> fetchExtendProfile(
  Ref ref, {
  String? code,
}) {
  if (code != null) {
    return ref.read(apiServiceProvider).getExtendProfilePublic(code: code);
  } else {
    return ref.read(apiServiceProvider).getExtendProfile();
  }
}

@riverpod
Future<FrontEndGitHubContributionCollection?> fetchGitHubContributions(
  Ref ref, {
  required String handle,
}) {
  return ref.read(apiFrontEndProvider).getGitHubContributions(handle: handle);
}

@riverpod
Future<Paged<CustomizeCardOrder>> fetchCustomizeCardOrders(Ref ref, {required int page}) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  return ref
      .read(apiServiceProvider)
      .getCustomizeCardOrders(
        page: page,
        size: 20,
        cancelToken: ct,
      );
}

@riverpod
bool? isActiveGuide(Ref ref) {
  // 1. 过滤掉虚拟卡，获取实体卡
  final cards = ref.watch(fetchMyCardsProvider).valueOrNull?.where((card) => !card.virtualCard) ?? [];

  final config = ref.watch(configProvider);

  // 2. 对于为空的（没有实体卡），都返回true
  if (cards.isEmpty) {
    return true;
  }

  // 3. 如果config.activationGuide不为true直接返回false
  if (!config.activationGuide) {
    return false;
  }

  // 4. 对于不为空的（有实体卡），每5分钟内最多显示一次返回true
  const String lastShownKey = 'activation_guide_last_shown';
  const int intervalMinutes = 60 * 24;
  const int intervalMilliseconds = intervalMinutes * 60 * 1000;

  try {
    final now = DateTime.now().millisecondsSinceEpoch;
    final lastShown = Boxes.settings.get(lastShownKey) as int?;

    if (lastShown == null || (now - lastShown) >= intervalMilliseconds) {
      // 记录本次显示时间
      Boxes.settings.put(lastShownKey, now);
      return true;
    }

    // 在5分钟间隔内，不显示
    return false;
  } catch (e, s) {
    handleExceptions(error: e, stackTrace: s);
    return true;
  }
}
