name: card3
description: "card3."
publish_to: 'none'
version: 1.1.0+69

environment:
  sdk: ^3.8.1
  flutter: '>=3.32.6'

me_package:
  url: &me_package_url "https://github.com/AstroxNetwork/me_packages"
  ref: &me_package_ref 'a972e2c9'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Wallets
  privy_flutter: 0.3.0
  web3dart: 3.0.1
  solana:
    git:
      url: https://github.com/AstroxNetwork/espresso-cash-public
      path: 'packages/solana'
      ref: '04fc10d6'

  me_analytics: # overrode
  me_bridge: # overrode
  me_constants: # overrode
  me_extensions: # overrode
  me_fonts: # overrode
  me_l10n: # overrode
  me_misc: # overrode
  me_models: # overrode
  me_ui: # overrode
  me_utils: # overrode

  app_links: 6.4.1
  auto_size_text: # transitive
  cbor: 6.3.7
  collection: 1.19.1
  connectivity_plus: 7.0.0
  decimal: 3.2.4
  dio: 5.9.0
  extended_image: 10.0.1
  ff_annotation_route_library: 3.1.0
  flutter_inappwebview: 6.2.0-beta.2
  flutter_local_notifications: 19.4.1
  flutter_markdown_plus: # transitive
  flutter_secure_storage: 9.2.4
  flutter_smart_dialog: 4.9.8+9
  flutter_svg: 2.2.1
  freezed_annotation: 3.1.0
  hive_ce_flutter: 2.3.2
  http: 1.5.0
  image_editor: 1.6.0
  image_picker: 1.2.0
  image_picker_android: # transitive
  image_picker_platform_interface: # transitive
  image_size_getter: 2.4.1
  in_app_update: 4.2.4
  intl: # transitive
  json_annotation: # transitive, required by code generation
  lottie: 3.3.1
  marqueer: 2.1.0
  mime: 2.0.0
  mobile_scanner: 7.0.1
  native_dio_adapter: 1.5.0
  path: 1.9.1
  path_provider: 2.1.5
  permission_handler: 12.0.1
  pinput: # transitive
  qr_flutter: 4.1.0
  rational: # transitive
  share_plus: 12.0.0
  sliver_tools: 0.2.12
  stack_trace: 1.12.1
  url_launcher: 6.3.2
  uuid: # transitive
  vector_graphics: # transitive
  video_player: 2.10.0
  visibility_detector: 0.4.0+2
  wallet: # transitive
  wolt_modal_sheet: 0.11.0

  # Firebase integrations
  firebase_core: 4.1.0
  firebase_analytics: 12.0.1
  firebase_crashlytics: 5.0.1
  firebase_messaging: 16.0.1

  # Hooks & Riverpod
  flutter_riverpod: 2.6.1
  riverpod_annotation: 2.6.1

  # NFC
  nfc_manager: 4.0.2
  nfc_manager_ndef: 1.0.1
  ndef_record: 1.2.1

  cached_video_player_plus:
    git:
      url: https://github.com/AstroxNetwork/cached_video_player_plus
      ref: '7f9ae19d'

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: any

  build_runner: 2.5.4
#  custom_lint: # transitive
  ff_annotation_route: ^11.1.2
  flutter_gen_runner: 5.11.0
  freezed: 3.1.0
#  hive_ce_generator: ^1.8.2
  json_serializable: # transitive
  riverpod_generator: 2.6.5
#  riverpod_lint: ^2.6.3
  web3dart_builders:
    git:
      url: https://github.com/AstroxNetwork/web3dart_builders
      ref: 'f464a05'
  yaml: # transitive

  astrox_build:
#    path: ../flutter_app_build_tool
    git:
      url: https://github.com/AstroxNetwork/flutter_app_build_tool
      ref: 'c3316129'

dependency_overrides:
  bip39:
    git:
      url: https://github.com/AstroxNetwork/bip39-dart
      ref: '33487ec0'

  cronet_http:
    git:
      url: https://github.com/dart-lang/http
      path: 'pkgs/cronet_http'
      ref: '4a90d16'

  me_analytics:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_analytics
#  me_analytics:
#    path: ../me_packages/packages/me_analytics
  me_bridge:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_bridge
#  me_bridge:
#    path: ../me_packages/packages/me_bridge
  me_constants:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_constants
#  me_constants:
#    path: ../me_packages/packages/me_constants
  me_extensions:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_extensions
#  me_extensions:
#    path: ../me_packages/packages/me_extensions
  me_fonts:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_fonts
#  me_fonts:
#    path: ../me_packages/packages/me_fonts
  me_l10n:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_l10n
#  me_l10n:
#    path: ../me_packages/packages/me_l10n
  me_misc:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_misc
#  me_misc:
#    path: ../me_packages/packages/me_misc
  me_models:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_models
#  me_models:
#    path: ../me_packages/packages/me_models
  me_ui:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_ui
#  me_ui:
#    path: ../me_packages/packages/me_ui
  me_utils:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_utils
#  me_utils:
#    path: ../me_packages/packages/me_utils

flutter:
  uses-material-design: true
  generate: true
  assets:
    - 'assets/'
#    - 'assets/fonts/'
    - 'assets/icons/'
    - 'assets/icons/images/'
    - 'assets/icons/scan/'
    - 'assets/icons/social/'
    - 'assets/icons/social/demo/'
    - 'assets/icons/setting/'
    - 'assets/lottie/'
    - 'assets/media/'
  fonts:
    - family: "MMM Mono"
      fonts:
        - asset: assets/fonts/MMMMono.ttf
    - family: "HarmonyOS Sans"
      fonts:
        - asset: assets/fonts/HarmonyOS_Sans_Thin.ttf
          weight: 100
        - asset: assets/fonts/HarmonyOS_Sans_Light.ttf
          weight: 300
        - asset: assets/fonts/HarmonyOS_Sans_Regular.ttf
          weight: 400
        - asset: assets/fonts/HarmonyOS_Sans_Medium.ttf
          weight: 500
        - asset: assets/fonts/HarmonyOS_Sans_Bold.ttf
          weight: 700
        - asset: assets/fonts/HarmonyOS_Sans_Black.ttf
          weight: 900

flutter_gen:
  output: lib/res/
  line_length: 120
  integrations:
    flutter_svg: true
    lottie: true
  colors:
    inputs:
      - assets/color/theme.xml
