{
  // VSCode Tasks - equivalent to IntelliJ IDEA build configurations
  // Use Ctrl+Shift+P -> "Tasks: Run Task" to execute these
  "version": "2.0.0",
  "tasks": [
    // Configuration Tasks
    {
      "label": "[CONFIG] DEV",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=dev", "--env-encoder=cbor", "--flavor=beta", "--release-config-only"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": [],
      "options": {
        "statusbar": {
          "label": "$(gear) Config DEV"
        }
      }
    },
    {
      "label": "[CONFIG] PROD",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=prod", "--env-encoder=cbor", "--flavor=app", "--release-config-only"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": [],
      "options": {
        "statusbar": {
          "label": "$(gear) Config PROD"
        }
      }
    },
    // APK Build Tasks
    {
      "label": "[BUILD] DEV APK",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=dev", "--env-encoder=cbor", "--dist=apk", "--flavor=beta"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "[BUILD] PROD APK",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=prod", "--env-encoder=cbor", "--dist=apk", "--flavor=app"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    // IPA Build Tasks
    {
      "label": "[BUILD] DEV IPA",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=dev", "--env-encoder=cbor", "--dist=ipa", "--flavor=beta"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "[BUILD] PROD IPA",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=prod", "--env-encoder=cbor", "--dist=ipa", "--flavor=app"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    // Combined Build Tasks
    {
      "label": "[BUILD] DEV IPA+APK",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=dev", "--env-encoder=cbor", "--dist=ipa,apk", "--flavor=beta"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    // Sealed/Production Build Tasks
    {
      "label": "[BUILD] SEALED ALL",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=prod", "--env-encoder=cbor", "--flavor=app", "--sealed"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "[BUILD] SEALED APK",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=prod", "--env-encoder=cbor", "--dist=apk", "--sealed"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "[BUILD] SEALED IPA",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=prod", "--env-encoder=cbor", "--dist=ipa", "--flavor=app", "--sealed"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "[BUILD] SEALED BUNDLE",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=prod", "--env-encoder=cbor", "--dist=bundle", "--flavor=app", "--sealed"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "[BUILD] SEALED Android",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--env=prod", "--env-encoder=cbor", "--platform=android", "--flavor=app", "--sealed"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    // Package Management Tasks
    {
      "label": "[PUB] Upgrade me_packages",
      "type": "shell",
      "command": "dart",
      "args": ["run", "astrox_build", "--upgrade-me-packages"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    }
  ]
}
