group 'io.flutter.plugins.cronet_http'
version '1.0-SNAPSHOT'

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

def dartDefines = [
    cronetHttpNoPlay: 'false'
]
if (project.hasProperty('dart-defines')) {
    def defines = project.property('dart-defines').split(',').collectEntries { entry ->
        def pair = new String(entry.decodeBase64(), 'UTF-8').split('=')
        [(pair.first()): pair.last()]
    }
    dartDefines = dartDefines + defines
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    // Conditional for compatibility with AGP <4.2.
    if (project.android.hasProperty("namespace")) {
        namespace 'io.flutter.plugins.cronet_http'
    }

    compileSdkVersion 31

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // api-level/minSdkVersion should be help in sync in:
        // - .github/workflows/cronet.yml
        // - pkgs/cronet_http/android/build.gradle
        // - pkgs/cronet_http/example/android/app/build.gradle
        minSdkVersion 21
    }

    defaultConfig {
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }
}

dependencies {
    if (dartDefines.cronetHttpNoPlay == 'true') {
        implementation 'org.chromium.net:cronet-embedded:119.6045.31'
    } else {
        implementation "com.google.android.gms:play-services-cronet:18.1.0"
    }
}
