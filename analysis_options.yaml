include: package:flutter_lints/flutter.yaml

formatter:
  page_width: 120
  trailing_commas: preserve

analyzer:
#  plugins:
#    - custom_lint
  errors:
    always_declare_return_types: error
    always_put_control_body_on_new_line: error
    avoid_renaming_method_parameters: error
    avoid_print: error
    avoid_void_async: error
    camel_case_types: error
    depend_on_referenced_packages: error
    directives_ordering: error
    invalid_override_of_non_virtual_member: error
    must_call_super: error
    non_constant_identifier_names: error
    prefer_single_quotes: error
    recursive_getters: error
    require_trailing_commas: error
    unnecessary_brace_in_string_interps: error
    unnecessary_import: error
    unrelated_type_equality_checks: error
    unused_import: error

    constant_identifier_names: ignore
    invalid_annotation_target: ignore
    todo: ignore

custom_lint:
  debug: false
  verbose: false
  rules:
    - avoid_manual_providers_as_generated_provider_dependency: false

linter:
  rules:
    always_declare_return_types: true
    always_put_control_body_on_new_line: true
    avoid_print: true
    avoid_renaming_method_parameters: true
    avoid_unnecessary_containers: true
    avoid_void_async: true
    curly_braces_in_flow_control_structures: true
    directives_ordering: true
    overridden_fields: false
    prefer_const_constructors: true
    prefer_const_constructors_in_immutables: false
    prefer_final_fields: true
    prefer_final_in_for_each: true
    prefer_final_locals: true
    prefer_single_quotes: true
    require_trailing_commas: true
    sort_child_properties_last: true
    sort_constructors_first: true
    sort_unnamed_constructors_first: true
    unnecessary_await_in_return: true
    unnecessary_breaks: true
    unnecessary_late: true
    unnecessary_parenthesis: true
    use_build_context_synchronously: false
    use_super_parameters: true
    void_checks: true
