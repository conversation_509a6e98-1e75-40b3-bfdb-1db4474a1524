// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:convert';

import 'package:package_info_plus/package_info_plus.dart';

import 'device_util.dart';
import 'log_util.dart';

const String _tag = '📦 PackageUtil';

class PackageUtil {
  const PackageUtil._();

  static String get buildTime {
    if (_buildTime?.isNotEmpty == true) {
      return _buildTime!;
    }
    return 'DEBUG_NOT_SPECIFIED';
  }

  static String? _buildTime;

  static late PackageInfo packageInfo;
  static late String packageName;
  static late String versionName;
  static late int versionCode;

  /// x.y.z+a
  static String get versionNameAndCode => '$versionName+$versionCode';

  /// x.y.z+a/202205241234
  static String get buildString => '$versionNameAndCode/$buildTime';

  static String get userAgent => '$packageName/$versionName '
      '(${DeviceUtil.osName}, ${DeviceUtil.osVersion}, ${DeviceUtil.deviceModel})';

  static Future<void> initInfo({
    String? buildTime,
    String? appVersionName,
    int? appVersionCode,
  }) async {
    _buildTime = buildTime;
    final info = await PackageInfo.fromPlatform();
    packageInfo = info;
    packageName = info.packageName;
    versionName = appVersionName ?? info.version;
    versionCode = appVersionCode ?? int.parse(info.buildNumber);
    LogUtil.d(
      'Package info: ${const JsonEncoder.withIndent('  ').convert(
        <String, dynamic>{
          'appName': info.appName,
          'packageName': packageName,
          'version': versionName,
          'buildNumber': versionCode,
        },
      )}',
      tag: _tag,
      tagWithTrace: false,
    );
  }
}
