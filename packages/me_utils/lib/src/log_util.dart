// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.
import 'dart:async';
import 'dart:developer' as dev;

import 'package:flutter/foundation.dart';

import 'bot_util.dart';

typedef LogFunction = void Function(
  Object? message,
  String tag,
  StackTrace stackTrace, {
  bool? isError,
});

/// A runtime store for error log events.
/// The main purpose is to collect errors when use choose to contain logs
/// for the feedback feature.
List<LogEvent> get errorLogEvents => _errorLogEvents.toList(growable: false);
final List<LogEvent> _errorLogEvents = [];

class LogUtil {
  const LogUtil._();

  static const String _tag = 'LOG';
  static StreamController<LogEvent>? _controller;
  static bool enable = false;
  static bool isNativeLogging = false;

  static StreamSubscription<LogEvent> addLogListener(
    void Function(LogEvent event) onData,
  ) {
    _controller ??= StreamController<LogEvent>.broadcast();
    return _controller!.stream.listen(onData);
  }

  static void i(
    Object? message, {
    String? tag,
    StackTrace? stackTrace,
    bool withStackTrace = true,
    int level = 1,
    bool tagWithTrace = kDebugMode,
    bool? report,
    Uri? reportOverrideUri,
    String? reportOverrideSigningKey,
    DateTime? reportOverrideSigningDateTime,
    bool? enabled,
  }) {
    tag = tagWithTrace
        ? buildLogTagWithTrace(StackTrace.current, level, tag)
        : tag ?? _tag;
    _printLog(
      message,
      '$tag ❕',
      stackTrace,
      withStackTrace: withStackTrace,
      report: report,
      reportOverrideUri: reportOverrideUri,
      reportOverrideSigningKey: reportOverrideSigningKey,
      reportOverrideSigningDateTime: reportOverrideSigningDateTime,
      enabled: enabled,
    );
  }

  static void d(
    Object? message, {
    String? tag,
    StackTrace? stackTrace,
    bool withStackTrace = true,
    int level = 1,
    bool tagWithTrace = kDebugMode,
    bool? report,
    Uri? reportOverrideUri,
    String? reportOverrideSigningKey,
    DateTime? reportOverrideSigningDateTime,
    bool? enabled,
  }) {
    tag = tagWithTrace
        ? buildLogTagWithTrace(StackTrace.current, level, tag)
        : tag ?? _tag;
    _printLog(
      message,
      '$tag 📣',
      stackTrace,
      withStackTrace: withStackTrace,
      report: report,
      reportOverrideUri: reportOverrideUri,
      reportOverrideSigningKey: reportOverrideSigningKey,
      reportOverrideSigningDateTime: reportOverrideSigningDateTime,
      enabled: enabled,
    );
  }

  static void dd(
    FutureOr<Object?> Function() call, {
    String? tag,
    StackTrace? stackTrace,
    bool withStackTrace = true,
    int level = 1,
    bool tagWithTrace = kDebugMode,
    bool? report,
    Uri? reportOverrideUri,
    String? reportOverrideSigningKey,
    DateTime? reportOverrideSigningDateTime,
    bool? enabled,
  }) {
    tag = tagWithTrace
        ? buildLogTagWithTrace(StackTrace.current, level, tag)
        : tag ?? _tag;
    _printLog(
      call(),
      '$tag 👀',
      stackTrace,
      withStackTrace: withStackTrace,
      report: report,
      reportOverrideUri: reportOverrideUri,
      reportOverrideSigningKey: reportOverrideSigningKey,
      reportOverrideSigningDateTime: reportOverrideSigningDateTime,
      enabled: enabled,
    );
  }

  static void w(
    Object? message, {
    String? tag,
    StackTrace? stackTrace,
    bool withStackTrace = true,
    int level = 1,
    bool tagWithTrace = kDebugMode,
    bool? report,
    Uri? reportOverrideUri,
    String? reportOverrideSigningKey,
    DateTime? reportOverrideSigningDateTime,
    bool? enabled,
  }) {
    tag = tagWithTrace
        ? buildLogTagWithTrace(StackTrace.current, level, tag)
        : tag ?? _tag;
    _printLog(
      message,
      '$tag ⚠️',
      stackTrace,
      withStackTrace: withStackTrace,
      report: report,
      reportOverrideUri: reportOverrideUri,
      reportOverrideSigningKey: reportOverrideSigningKey,
      reportOverrideSigningDateTime: reportOverrideSigningDateTime,
      enabled: enabled,
    );
  }

  static void e(
    Object? message, {
    String? tag,
    StackTrace? stackTrace,
    bool withStackTrace = true,
    int level = 1,
    bool? report = kReleaseMode,
    Uri? reportOverrideUri,
    String? reportOverrideSigningKey,
    DateTime? reportOverrideSigningDateTime,
    bool tagWithTrace = kDebugMode,
    bool? enabled,
  }) {
    tag = tagWithTrace
        ? buildLogTagWithTrace(StackTrace.current, level, tag)
        : tag ?? _tag;
    _printLog(
      message,
      '$tag ❌',
      stackTrace,
      withStackTrace: withStackTrace,
      isError: true,
      report: report,
      reportOverrideUri: reportOverrideUri,
      reportOverrideSigningKey: reportOverrideSigningKey,
      reportOverrideSigningDateTime: reportOverrideSigningDateTime,
      enabled: enabled,
    );
  }

  static void json(
    Object? message, {
    String? tag,
    StackTrace? stackTrace,
    bool withStackTrace = true,
    int level = 1,
    bool tagWithTrace = kDebugMode,
    bool? report,
    Uri? reportOverrideUri,
    String? reportOverrideSigningKey,
    DateTime? reportOverrideSigningDateTime,
    bool? enabled,
  }) {
    tag = tagWithTrace
        ? buildLogTagWithTrace(StackTrace.current, level, tag)
        : tag ?? _tag;
    _printLog(
      message,
      '$tag 💠',
      stackTrace,
      withStackTrace: withStackTrace,
      report: report,
      reportOverrideUri: reportOverrideUri,
      reportOverrideSigningKey: reportOverrideSigningKey,
      reportOverrideSigningDateTime: reportOverrideSigningDateTime,
      enabled: enabled,
    );
  }

  static String buildLogTagWithTrace(
    StackTrace stackTrace,
    int level,
    String? tag,
  ) {
    if (tag != null) {
      final line = stackTrace.toString().split('\n')[level];
      final trace =
          RegExp(r'\([\w:/.]+:\d+:\d+\)').allMatches(line).lastOrNull?.group(0);
      return '$tag $trace';
    }
    return stackTrace
        .toString()
        .split('\n')[level]
        .replaceAll(RegExp(r'(#\d+\s+)|(<anonymous closure>)'), '')
        .replaceAll('. (', '.() (');
  }

  static void _printLog(
    Object? message,
    String tag,
    StackTrace? stackTrace, {
    required bool? report,
    required bool? enabled,
    required bool withStackTrace,
    bool isError = false,
    Uri? reportOverrideUri,
    String? reportOverrideSigningKey,
    DateTime? reportOverrideSigningDateTime,
  }) {
    if (message is Function()) {
      message = message();
    }
    if (stackTrace == StackTrace.empty) {
      stackTrace = null;
    }
    if (stackTrace != null) {
      stackTrace = FlutterError.demangleStackTrace(stackTrace);
    }
    final DateTime time = DateTime.now();

    // Report to the upstream when allow to report.
    if (report == true) {
      BotUtil.reportToBot(
        message: message,
        tag: tag,
        stackTrace: stackTrace,
        overrideUri: reportOverrideUri,
        overrideSigningKey: reportOverrideSigningKey,
        overrideSigningDateTime: reportOverrideSigningDateTime,
      );
    }

    if (!enable || enabled == false) {
      return;
    }

    final logEvent = LogEvent(message, stackTrace, time, tag, isError);
    _controller?.add(logEvent);

    final String timeString = time.toIso8601String().substring(11);
    // Handle errors.
    if (isError) {
      _errorLogEvents.add(logEvent);
      // Present errors rather than only log them in DEBUG mode.
      if (kDebugMode) {
        FlutterError.presentError(
          FlutterErrorDetails(
            exception: message ?? 'NULL',
            stack: stackTrace,
            library: tag == _tag ? 'Framework' : tag,
          ),
        );
      } else {
        dev.log(
          '$timeString - An error occurred.',
          time: time,
          name: tag,
          error: message,
          stackTrace: stackTrace,
        );
      }
    } else {
      dev.log(
        '$timeString - $message',
        time: time,
        name: tag,
        stackTrace: stackTrace ??
            (isError && withStackTrace ? StackTrace.current : null),
      );
    }
    // Produce native platform logs if applicable.
    if (isNativeLogging) {
      final nativeString = StringBuffer();
      if (tag.isNotEmpty) {
        nativeString.write('[$tag] ');
      }
      nativeString.write(message);
      if (stackTrace != null && stackTrace != StackTrace.empty) {
        nativeString.write('\n$stackTrace');
      }
      debugPrint(nativeString.toString());
    }
  }
}

@immutable
class LogEvent {
  const LogEvent(
    this.message,
    this.stackTrace,
    this.dateTime,
    this.tag,
    this.isError,
  );

  final Object? message;
  final StackTrace? stackTrace;
  final DateTime dateTime;
  final String tag;
  final bool isError;

  @override
  String toString() {
    final sb = StringBuffer('${dateTime.toIso8601String()} $tag ');
    if (isError) {
      sb.write('[E] ');
    }
    sb.write(message);
    if (stackTrace != null && stackTrace != StackTrace.empty) {
      sb.write('\n$stackTrace');
    }
    return sb.toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    if (other is! LogEvent) {
      return false;
    }
    return message == other.message &&
        stackTrace == other.stackTrace &&
        dateTime == other.dateTime &&
        tag == other.tag &&
        isError == other.isError;
  }

  @override
  int get hashCode => Object.hash(message, stackTrace, dateTime, tag, isError);
}

extension MEStopwatchExtension on Stopwatch {
  void logElapsed([String? tag]) {
    LogUtil.d(
      '${tag ?? 'Stopwatch'} elapsed: $elapsed',
      tag: '⌚ Stopwatch',
    );
  }
}
