// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.
import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class HapticUtil {
  const HapticUtil._();

  static bool shouldUseHaptic = defaultTargetPlatform == TargetPlatform.iOS ||
      defaultTargetPlatform == TargetPlatform.macOS;

  static Future<void> notifySuccess({bool force = false}) {
    return _shouldUseHapticWrapper(
      () => _sequenceWithGap(
        haptics: <Future<void> Function()>[
          HapticFeedback.mediumImpact,
          HapticFeedback.heavyImpact,
        ],
        gapInMilliseconds: 150,
      ),
      force: force,
    );
  }

  static Future<void> notifyWarning({bool force = false}) {
    return _shouldUseHapticWrapper(
      () => _sequenceWithGap(
        haptics: <Future<void> Function()>[
          HapticFeedback.heavyImpact,
          HapticFeedback.mediumImpact,
        ],
        gapInMilliseconds: 200,
      ),
      force: force,
    );
  }

  static Future<void> notifyFailure({bool force = false}) {
    return _shouldUseHapticWrapper(
      () => _sequenceWithGap(
        haptics: <Future<void> Function()>[
          HapticFeedback.mediumImpact,
          HapticFeedback.mediumImpact,
          HapticFeedback.heavyImpact,
          HapticFeedback.lightImpact,
        ],
        gapInMilliseconds: 100,
      ),
      force: force,
    );
  }

  static Future<void> _sequenceWithGap({
    required List<Future<void> Function()> haptics,
    required int gapInMilliseconds,
  }) async {
    for (int i = 0; i < haptics.length * 2 - 1; i++) {
      if (i.isOdd) {
        await Future<void>.delayed(Duration(milliseconds: gapInMilliseconds));
        continue;
      }
      await haptics[i ~/ 2]();
    }
  }

  static Future<void> _shouldUseHapticWrapper(
    Future<void> Function() fn, {
    required bool force,
  }) {
    if (force || shouldUseHaptic) {
      return fn();
    }
    return SynchronousFuture<void>(null);
  }
}
