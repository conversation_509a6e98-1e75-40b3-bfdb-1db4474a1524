// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/gestures.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';

import 'log_util.dart';

class DeviceUtil {
  const DeviceUtil._();

  static final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();

  static BaseDeviceInfo? deviceInfo;
  static AndroidDeviceInfo? androidInfo;
  static IosDeviceInfo? iOSInfo;
  static MacOsDeviceInfo? macOSInfo;
  static WindowsDeviceInfo? windowsInfo;
  static LinuxDeviceInfo? linuxInfo;

  static DisplayMode? _highestRefreshRateMode;

  static final String osName = kIsWeb ? 'Web' : Platform.operatingSystem;
  static String osVersion = 'Unknown';
  static String deviceModel = 'ME Device';
  static String? devicePushToken;
  static String? deviceUuid;

  static String get deviceName {
    if (kIsWeb) {
      return 'Browser';
    }
    final String? model;
    if (Platform.isAndroid) {
      model = DeviceUtil.androidInfo?.device;
    } else if (Platform.isIOS) {
      model = DeviceUtil.iOSInfo?.utsname.nodename;
    } else if (Platform.isMacOS) {
      model = DeviceUtil.macOSInfo?.model;
    } else if (Platform.isWindows) {
      model = DeviceUtil.windowsInfo?.productName;
    } else if (Platform.isLinux) {
      model = DeviceUtil.linuxInfo?.machineId;
    } else {
      model = null;
    }
    return (model ?? DeviceUtil.deviceModel)
        .replaceAll('_', ' ')
        .replaceAll('-', ' ')
        .trim();
  }

  static Future<void> initDeviceInfo({bool forceRefresh = false}) async {
    if (deviceInfo != null && !forceRefresh) {
      return;
    }
    await _getModel();
  }

  static Future<void> _getModel() async {
    deviceInfo = await _deviceInfoPlugin.deviceInfo;
    if (Platform.isAndroid) {
      final AndroidDeviceInfo info = deviceInfo as AndroidDeviceInfo;
      androidInfo = info;
      String? device;
      final deviceStringBuffer = StringBuffer()
        ..write('${info.manufacturer} ')
        ..write(info.model);
      device = deviceStringBuffer.toString().trim();
      if (device.isEmpty) {
        device = null;
      }
      deviceModel = device ?? deviceModel;
      osVersion = info.version.sdkInt.toString();
    } else if (Platform.isIOS) {
      final IosDeviceInfo info = deviceInfo as IosDeviceInfo;
      iOSInfo = info;
      deviceModel = info.utsname.machine;
      osVersion = info.systemVersion;
    } else if (Platform.isMacOS) {
      final MacOsDeviceInfo info = deviceInfo as MacOsDeviceInfo;
      macOSInfo = info;
      deviceModel = info.model;
      osVersion = info.osRelease;
    } else if (Platform.isWindows) {
      final WindowsDeviceInfo info = deviceInfo as WindowsDeviceInfo;
      windowsInfo = info;
      deviceModel = info.productName;
      osVersion = info.buildLab;
    } else if (Platform.isLinux) {
      final LinuxDeviceInfo info = deviceInfo as LinuxDeviceInfo;
      linuxInfo = info;
      deviceModel = info.prettyName;
      osVersion = info.version ?? osVersion;
    } else {
      deviceModel = deviceInfo.toString();
      osVersion = 'Unknown';
    }
    LogUtil.d(
      'Device: ${deviceInfo?.forLog}',
      tag: '⚙️ DeviceUtil',
      tagWithTrace: false,
    );
  }

  static Future<void> setHighestRefreshRate() async {
    if (!Platform.isAndroid || androidInfo?.version.sdkInt == null) {
      return;
    }
    // Apply only on Android 23+.
    final int sdkInt = androidInfo!.version.sdkInt;
    if (sdkInt < 23) {
      return;
    }
    // Delay 1 second since bindings will need to reconnect.
    await Future.delayed(const Duration(seconds: 1));
    final DisplayMode current = await FlutterDisplayMode.active;
    // Search for the highest refresh rate with the same screen size and save.
    if (_highestRefreshRateMode == null) {
      final List<DisplayMode> modes = await FlutterDisplayMode.supported;
      final Iterable<DisplayMode> matchedModes = modes.where(
        (DisplayMode mode) =>
            mode.width == current.width && mode.height == current.height,
      );
      if (matchedModes.isNotEmpty) {
        _highestRefreshRateMode = matchedModes.reduce(
          (DisplayMode value, DisplayMode element) =>
              value.refreshRate > element.refreshRate ? value : element,
        );
      }
    }
    final DisplayMode? highest = _highestRefreshRateMode;
    if (highest == null) {
      return;
    }
    // Apply when the current refresh rate is lower than the highest.
    if (current.refreshRate < highest.refreshRate) {
      _logRefreshRateChanges(current, highest);
      await FlutterDisplayMode.setPreferredMode(highest);
      final DisplayMode newMode = await FlutterDisplayMode.active;
      // Only apply resampling when the refresh rate has been updated.
      if (newMode.refreshRate > current.refreshRate) {
        GestureBinding.instance.resamplingEnabled = true;
      }
    }
  }

  static void _logRefreshRateChanges(DisplayMode before, DisplayMode after) {
    final String beforeString = '${before.refreshRate.toInt()}'.padRight(7);
    final String afterString = '${after.refreshRate.toInt()}'.padRight(7);
    final String sizeHeader =
        'Screen: ${after.width}×${after.height}'.padRight(17);
    LogUtil.d(
      'Refresh rate update:\n'
      '${'-' * 21}\n'
      '| $sizeHeader |\n'
      '| Before  | After   |\n'
      '| $beforeString | $afterString |\n'
      '${'-' * 21}',
      tag: '📱 DisplayMode',
      tagWithTrace: false,
    );
  }
}

extension LogDeviceInfoExtension on BaseDeviceInfo {
  String get forLog {
    final info = this;
    final log = switch (info) {
      AndroidDeviceInfo() => '${info.manufacturer} ${info.model} '
          '(Android ${info.version.sdkInt})',
      IosDeviceInfo() => '${info.utsname.machine} '
          '(iOS ${info.systemVersion})',
      MacOsDeviceInfo() => '${info.model} ${info.arch} '
          '(macOS ${info.osRelease})',
      WindowsDeviceInfo() => '${info.productName} '
          '(${info.releaseId} ${info.displayVersion})',
      LinuxDeviceInfo() => info.prettyName,
      _ => info.toString(),
    };
    return log;
  }
}
