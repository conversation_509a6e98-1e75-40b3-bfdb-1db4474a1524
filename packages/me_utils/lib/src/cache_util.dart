// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.
// ignore_for_file: constant_identifier_names
import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:path_provider/path_provider.dart';

class CacheManager {
  const CacheManager._();

  static const int KB = 1024;
  static const int MB = 1024 * KB;
  static const int GB = 1024 * MB;

  static Completer<String>? _cacheDirLock;

  static Future<String> getCacheDir() {
    if (kIsWeb) {
      return Future.value('/cache');
    }
    if (_cacheDirLock case final lock?) {
      return lock.future;
    }
    final lock = Completer<String>();
    _cacheDirLock = lock;
    getTemporaryDirectory().then(
      (e) => lock.complete(e.path),
      onError: lock.completeError,
    );
    return lock.future;
  }

  static Future<int> getCacheSize() async {
    if (kIsWeb) {
      return 0;
    }
    final Directory dir = Directory(await getCacheDir());
    final listStream = dir.list(recursive: true);
    int size = 0;
    await for (final file in listStream) {
      final stat = await file.stat();
      size += stat.size;
    }
    return size;
  }

  static Future<String> getFormatCacheSize() async {
    final int size = await getCacheSize();
    if (size >= GB) {
      return '${(size / GB).toStringAsFixed(2)} GB';
    }
    if (size >= MB) {
      return '${(size / MB).toStringAsFixed(2)} MB';
    }
    if (size >= KB) {
      return '${(size / KB).toStringAsFixed(2)} KB';
    }
    return '$size B';
  }

  static Future<void> clearCache() async {
    if (kIsWeb) {
      return;
    }
    final Directory dir = Directory(await getCacheDir());
    await Future.wait(
      <Future<void>>[
        for (final FileSystemEntity f in dir.listSync())
          f.delete(recursive: true),
      ],
    );
  }
}
