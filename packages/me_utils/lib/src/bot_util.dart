// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:convert' show base64, utf8;

import 'package:crypto/crypto.dart' show Digest, Hmac, sha256;
import 'package:dio/dio.dart' show Dio;
import 'package:me_misc/me_misc.dart' show retryWith;

import 'device_util.dart';
import 'log_util.dart';
import 'package_util.dart';

Uri? _uri;
String? _key;
final Dio _dio = Dio();

class BotUtil {
  const BotUtil._();

  static set url(String url) => _uri = Uri.parse(url);

  static set key(String value) => _key = value;

  static void Function(StringBuffer sb)? buildLogInfo;

  static String? get _deviceInfo => DeviceUtil.deviceInfo?.forLog;

  static Map<String, dynamic> _botBody(
    Object? message, {
    String? tag,
    StackTrace? stackTrace,
  }) {
    final sb = StringBuffer()
      ..writeln('[🕒]: ${DateTime.now()}')
      ..writeln('[🔤]: ${PackageUtil.packageName}');

    sb.write('[🏷️]: ${PackageUtil.buildString}');
    // Explicitly handle package version overrides.
    if (PackageUtil.packageInfo case final info
        when info.buildNumber != PackageUtil.versionCode.toString()) {
      sb.write(' (${info.version}+${info.buildNumber})');
    }
    sb.writeln();

    buildLogInfo?.call(sb);
    if (_deviceInfo != null) {
      sb.writeln('[📱]: $_deviceInfo');
    }
    sb.write('[💥]: ');
    if (tag != null) {
      sb.write('{$tag} ');
    }
    sb.writeln('(${message.runtimeType}) $message');
    if (stackTrace != null && stackTrace != StackTrace.empty) {
      sb.writeln('[🧾]: $stackTrace');
    }
    return <String, dynamic>{
      'msg_type': 'text',
      'content': <String, dynamic>{'text': sb.toString().trim()},
    };
  }

  static Future<void> reportToBot({
    required Object? message,
    String? tag,
    StackTrace? stackTrace,
    Uri? overrideUri,
    String? overrideSigningKey,
    DateTime? overrideSigningDateTime,
  }) async {
    final uri = overrideUri ?? _uri;
    if (uri == null || uri.scheme.isEmpty || uri.host.isEmpty) {
      return;
    }
    final Map<String, dynamic> body = _botBody(
      message,
      tag: tag,
      stackTrace: stackTrace,
    );
    if (_key?.isNotEmpty == true) {
      body.addAll(
        BotUtil.sign(
          key: overrideSigningKey,
          dateTime: overrideSigningDateTime,
        ),
      );
    }
    try {
      await retryWith(
        () => _dio.postUri(uri, data: body),
        retryTimes: 5,
        retryInterval: const Duration(seconds: 1),
      );
    } catch (e, s) {
      LogUtil.w('Report BOT error $e', stackTrace: s);
    }
  }

  static Map<String, String> sign({
    String? key,
    DateTime? dateTime,
  }) {
    key ??= _key;
    dateTime ??= DateTime.now();
    final int timestamp = dateTime.millisecondsSinceEpoch ~/ 1000;
    final Hmac signKey = Hmac(sha256, utf8.encode('$timestamp\n$key'));
    final Digest digest = signKey.convert(<int>[]);
    final String sign = base64.encode(digest.bytes);
    return <String, String>{'timestamp': '$timestamp', 'sign': sign};
  }
}
