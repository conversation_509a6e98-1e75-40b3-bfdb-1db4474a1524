// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.
import 'package:flutter/material.dart';

@optionalTypeArgs
mixin LazyIndexedStackChildMixin<T extends StatefulWidget> on State<T> {
  int? _index;
  int _selectedIndex = 0;

  bool get isPageAppear => _isPageAppear;
  late bool _isPageAppear = _index == _selectedIndex;

  @mustCallSuper
  @override
  @protected
  void initState() {
    super.initState();
    didAppear();
  }

  @mustCallSuper
  @override
  @protected
  void dispose() {
    didDisappear();
    super.dispose();
  }

  @protected
  void onIndexChanged(int index) {}

  @protected
  void didAppear() {}

  @protected
  void didDisappear() {}

  @protected
  void _onIndexChanged(int index) {
    final int lastIndex = _selectedIndex;
    final bool isPageAppear = _isPageAppear;
    _isPageAppear = _index == index;
    if (_index != null && lastIndex != index) {
      if (_index == index && !isPageAppear) {
        didAppear();
      } else if (_index != index && isPageAppear) {
        didDisappear();
      }
    }
    _selectedIndex = index;
    onIndexChanged(index);
  }
}

/// A lazy-loading [IndexedStack] that loads [children] accordingly.
class LazyIndexedStack extends StatefulWidget {
  const LazyIndexedStack({
    super.key,
    required this.children,
    this.index = 0,
    this.alignment = AlignmentDirectional.topStart,
    this.textDirection,
    this.sizing = StackFit.loose,
  }) : assert(children.length > 0, 'Empty children is not allowed.');

  final List<Widget> children;
  final int index;
  final AlignmentGeometry alignment;
  final TextDirection? textDirection;
  final StackFit sizing;

  @override
  State<LazyIndexedStack> createState() => _LazyIndexedStackState();
}

class _LazyIndexedStackState extends State<LazyIndexedStack> {
  late final Map<int, bool> _innerWidgetMap = Map<int, bool>.fromEntries(
    List<MapEntry<int, bool>>.generate(
      widget.children.length,
      (int i) => MapEntry<int, bool>(i, i == index),
    ),
  );

  late int index = widget.index;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _visitStatefulElement((StatefulElement element) {
        final State<StatefulWidget> state = element.state;
        if (state is LazyIndexedStackChildMixin) {
          final int index = widget.children.indexOf(element.widget);
          state._index = index;
        }
      });
    });
  }

  @override
  void didUpdateWidget(LazyIndexedStack oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.index != widget.index) {
      _changeIndex(widget.index);
    }
  }

  void _visitStatefulElement(
    void Function(StatefulElement) elementVisitor,
  ) {
    context.visitChildElements((Element e) {
      if (e.widget is IndexedStack) {
        e.visitChildElements((Element element) {
          if (element is StatefulElement) {
            elementVisitor(element);
          }
        });
      }
    });
  }

  void _activeCurrentIndex(int index) {
    if (_innerWidgetMap[index] != true) {
      _innerWidgetMap[index] = true;
    }
  }

  void _changeIndex(int value) {
    if (!mounted || value == index) {
      return;
    }
    setState(() {
      index = value;
    });
    // Omit index change callback when child widgets are
    // [LazyIndexedStackChildMixin].
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _visitStatefulElement((StatefulElement element) {
        final State<StatefulWidget> state = element.state;
        if (state is LazyIndexedStackChildMixin) {
          if (state._index == null) {
            final int index = widget.children.indexOf(element.widget);
            state._index = index;
          } else {
            state._onIndexChanged(value);
          }
        }
      });
    });
  }

  bool _hasInit(int index) => _innerWidgetMap[index] == true;

  List<Widget> _buildChildren(BuildContext context) {
    return <Widget>[
      for (int i = 0; i < widget.children.length; i++)
        _hasInit(i) ? widget.children[i] : const SizedBox.shrink(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    _activeCurrentIndex(index);
    return IndexedStack(
      index: index,
      alignment: widget.alignment,
      sizing: widget.sizing,
      textDirection: widget.textDirection,
      children: _buildChildren(context),
    );
  }
}
