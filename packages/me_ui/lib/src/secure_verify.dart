import 'dart:async';
import 'dart:io' as io;
import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_darwin/types/auth_messages_ios.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_fonts/me_fonts.dart' show fontGotham;
import 'package:me_l10n/me_l10n.dart';
import 'package:me_misc/me_misc.dart';
import 'package:pinput/pinput.dart';
import 'package:system_clock/system_clock.dart';

import '../gen/assets.gen.dart';
import 'brightness_layer.dart';
import 'button/theme_text_button.dart';
import 'dialog/scrollable_bottom_sheet.dart';
import 'gaps.dart';
import 'text/placeholder_text.dart';
import 'toast.dart';

const int _kPinRetryTimes = 5;
const Duration _kRetryLaterInterval = Duration(minutes: 15);
const String _biometricKey = 'biometric';
const String _pinKey = 'pin';
const String _genAtKey = 'pin:gen-times-at';
const String _timesKey = 'pin:times';
const String _failedAtKey = 'pin:failed-auth-at';
const String _authAtKey = 'pin:auth-at';
const String _noAuthIntervalKey = 'pin:no-auth-interval';
const String _failedRemainderKey = 'pin:failed-remainder';

const Duration _kDuration = Duration(milliseconds: 160);

enum _PinKeyType {
  number,
  bio,
  backspace,
}

class SecureVerify extends StatefulWidget {
  const SecureVerify._({
    required this.theme,
    this.subtitle,
    this.allowClose = false,
    this.withBiometric = true,
  });

  final ThemeData theme;
  final String? subtitle;
  final bool allowClose;
  final bool withBiometric;

  static late Box _box;
  static late void Function(BuildContext) _onForgotPin;
  static late void Function({Object error, StackTrace stackTrace}) _onException;

  static void initialize({
    required Box box,
    required void Function(BuildContext) onForgotPin,
    required void Function({Object error, StackTrace stackTrace}) onException,
  }) {
    _box = box;
    _onForgotPin = onForgotPin;
    _onException = onException;
  }

  static bool get isVerifying => _completer != null && !_completer!.isCompleted;

  static Completer<bool>? _completer;

  static Future<bool> verify(
    BuildContext context, {
    String? subtitle,
    bool closeable = false,
    ThemeData? theme,
  }) {
    if (isVerifying) {
      return _completer!.future;
    }
    if (getPin() == null) {
      return Future.value(true);
    }
    if (!closeable) {
      final authAt = _box.get(_authAtKey) as int?;
      if (authAt != null) {
        final v = SystemClock.elapsedRealtime().inMilliseconds;
        if (v > authAt) {
          final interval = getNoAuthInterval();
          if (v < authAt + interval) {
            return Future.value(true);
          }
        }
      }
    }
    final completer = Completer<bool>();
    _completer = completer;
    HapticFeedback.mediumImpact();
    Navigator.push(
      context,
      METransparentPageRoute<dynamic>(
        settings: RouteSettings(name: 'secure-verify', arguments: closeable),
        pageBuilder: (_, __, ___) => SecureVerify._(
          subtitle: subtitle,
          allowClose: closeable,
          withBiometric: _box.get('biometric', defaultValue: false),
          theme: theme ?? context.theme,
        ),
        transitionsBuilder: (
          BuildContext context,
          Animation<double> animation,
          Animation<double> secondaryAnimation,
          Widget child,
        ) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).animate(
              CurvedAnimation(parent: animation, curve: Curves.ease),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 360),
      ),
    ).then(
      (v) async {
        final verified = v == true;
        if (verified) {
          await setAuthAt().catchError((_) {});
        }
        completer.complete(verified);
      },
      onError: completer.completeError,
    ).whenComplete(() {
      _completer = null;
    });
    return completer.future;
  }

  static bool getBiometric() => _box.get(_biometricKey, defaultValue: false);

  static Future<void> setBiometric(bool value) {
    return _box.put(_biometricKey, value);
  }

  static String? getPin() => _box.get(_pinKey) as String?;

  static Future<void> setPin(String? value) {
    return _box.put(_pinKey, value);
  }

  static Future<void> setAuthAt() {
    final authAt = SystemClock.elapsedRealtime();
    return _box.put(_authAtKey, authAt.inMilliseconds);
  }

  static int getNoAuthInterval() {
    return _box.get(_noAuthIntervalKey) ??
        const Duration(minutes: 1).inMilliseconds;
  }

  static Future<void> setNoAuthInterval(int mills) {
    return _box.put(_noAuthIntervalKey, mills);
  }

  @override
  State<SecureVerify> createState() => _SecureVerifyState();
}

class _SecureVerifyState extends State<SecureVerify>
    with WidgetsBindingObserver {
  final _controller = TextEditingController();
  late final _waitTime = ValueNotifier<String>('00:00');
  late final _subtitle = NewValueNotifier<String>(
    widget.subtitle ?? globalL10nME.pinProceedText,
  );

  Box get _box => SecureVerify._box;

  ThemeData get _theme => widget.theme;

  bool get _withBiometric => _enableBiometric && widget.withBiometric;

  String get _defaultCounter =>
      "${_kRetryLaterInterval.inMinutes.remainder(60).toString().padLeft(2, "0")}"
      ':'
      "${_kRetryLaterInterval.inSeconds.remainder(60).toString().padLeft(2, '0')}";

  int _pinTimes = _kPinRetryTimes;
  bool _isFirst = true;
  bool _checking = false;
  bool _enableBiometric = true;
  bool _waiting = false;
  bool _notMatched = false;
  Timer? _waitingTimer;
  DateTime? _lastOperation;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    Future<void>.microtask(_init);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // Do not manual call [dispose], may have async task.
    // _controller.dispose();
    // _waitTime.dispose();
    _waitingTimer?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _showWaitingView();
    } else if (state == AppLifecycleState.paused) {
      _controller.clear();
    }
  }

  Future<void> _init() async {
    hideKeyboard();
    await _showWaitingView();
    await _bioVerify();
    final genAt = _box.get(_genAtKey) ?? 0;
    final now = SystemClock.elapsedRealtime().inMilliseconds;
    if (_box.get(_timesKey) == null ||
        genAt + _kRetryLaterInterval.inMilliseconds < now) {
      _pinTimes = _kPinRetryTimes;
      await _box.put(_timesKey, _pinTimes);
      await _box.put(_genAtKey, now);
      await _box.delete(_failedAtKey);
      await _box.delete(_failedRemainderKey);
    } else {
      _pinTimes = _box.get(_timesKey)!;
    }
    safeSetState(() {});
  }

  Future<void> _showWaitingView() async {
    _waitingTimer?.cancel();
    final failedAt = _box.get(_failedAtKey) as int? ?? 0;
    final nowMills = SystemClock.elapsedRealtime().inMilliseconds;
    int counter;
    if (nowMills < failedAt) {
      final v = _box.get(_failedRemainderKey, defaultValue: 0) as int;
      await _box.put(_failedAtKey, nowMills - v * 1000);
      counter = v;
    } else {
      if (failedAt == 0) {
        counter = 0;
      } else {
        counter = Duration(
          milliseconds: math.max(
            failedAt + _kRetryLaterInterval.inMilliseconds - nowMills,
            0,
          ),
        ).inSeconds;
      }
    }

    if (counter <= 0) {
      if (_waiting) {
        await _box.delete(_failedAtKey);
        safeSetState(() {
          _waiting = false;
        });
      }
      return;
    }
    _setWaitingTime(counter + 1);
    _subtitle.newValue(globalL10nME.pinUnlockRestrictPrompt);
    // waiting for counter has rendered.
    await Future.delayed(const Duration(milliseconds: 100));
    safeSetState(() {
      _waiting = true;
    });
    final completer = Completer<void>();
    _waitingTimer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) async {
        if (counter < 0) {
          timer.cancel();
          _subtitle.newValue(widget.subtitle ?? globalL10nME.pinProceedText);
          await _box.put(_genAtKey, nowMills);
          safeSetState(() => _waiting = false);
          completer.complete();
        }
        _setWaitingTime(math.max(counter, 0));
        counter--;
        await _box.put(_failedRemainderKey, counter);
      },
    );
    return completer.future;
  }

  void _setWaitingTime(int seconds) {
    final duration = Duration(seconds: seconds);
    final min = duration.inMinutes.remainder(60).toString();
    final sec = duration.inSeconds.remainder(60).toString();
    _waitTime.value = "${min.padLeft(2, '0')}:${sec.padLeft(2, '0')}";
  }

  Future<void> _onKeyPressed(_PinKeyType type, String? text) async {
    if (_checking) {
      return;
    }
    _checking = true;
    try {
      switch (type) {
        case _PinKeyType.number:
          _controller.text = '${_controller.text}$text';
          if (_controller.length == 6) {
            await _onComplete(_controller.text);
          }
        case _PinKeyType.bio:
          await _bioVerify();
        case _PinKeyType.backspace:
          if (_controller.text.isEmpty) {
            return;
          }
          _controller.text = _controller.text.substring(
            0,
            _controller.length - 1,
          );
      }
    } finally {
      _checking = false;
    }
  }

  Future<void> _bioVerify() async {
    if (!_withBiometric) {
      return;
    }
    try {
      if (await verifyBiometric(
        localizedReason: widget.subtitle ?? globalL10nME.pinProceedText,
      )) {
        HapticFeedback.mediumImpact();
        meNavigator.pop(true);
      }
    } catch (e, s) {
      if (e is PlatformException) {
        final v = await LocalAuthentication().canCheckBiometrics;
        if (v) {
          return;
        }
        await stopBiometricVerify();
      }
      SecureVerify._onException(error: e, stackTrace: s);
      _delayEnableBiometric();
    }
  }

  void _delayEnableBiometric() {
    safeSetState(() {
      _enableBiometric = false;
    });
    Future.delayed(const Duration(seconds: 45), () {
      _enableBiometric = true;
      safeSetState(() {});
    });
  }

  Future<void> _onComplete(String code) async {
    await Future.delayed(const Duration(milliseconds: 500));
    _isFirst = false;
    _pinTimes--;
    await _box.put(_timesKey, _pinTimes);
    final pin = SecureVerify.getPin();
    if (code == pin) {
      await _box.delete(_genAtKey);
      await _box.delete(_timesKey);
      await _box.delete(_failedAtKey);
      await _box.delete(_failedRemainderKey);
      HapticFeedback.heavyImpact();
      meNavigator.pop(true);
      return;
    }
    HapticFeedback.heavyImpact();
    _notMatched = true;
    int time = 100;
    while (_controller.length > 0 && mounted) {
      _controller.text = _controller.text.substring(
        0,
        _controller.length - 1,
      );
      await time.milliseconds.delay;
      time -= 10;
    }
    _notMatched = false;
    if (_pinTimes <= 0) {
      await _box.put(
        _failedAtKey,
        SystemClock.elapsedRealtime().inMilliseconds,
      );
      await _showWaitingView();
      _pinTimes = _kPinRetryTimes;
      _isFirst = true;
    }
    _setSubtitle();
  }

  void _setSubtitle() {
    if (!mounted) {
      return;
    }
    final String sub;
    if (_isFirst) {
      sub = widget.subtitle ?? globalL10nME.pinProceedText;
    } else if (_pinTimes == 1) {
      sub = globalL10nME.pinWrongLastTimesLeft(_defaultCounter);
    } else {
      sub = globalL10nME.pinWrongTimesLeft(_pinTimes);
    }
    _subtitle.newValue(sub);
  }

  List<Widget> _buildKeys(BuildContext context) {
    return <Widget>[
      ...List<Widget>.generate(
        9,
        (int index) => _PinKey(
          text: (index + 1).toString(),
          action: _onKeyPressed,
        ),
      ),
      if (_withBiometric)
        _PinKey(
          icon: io.Platform.isIOS
              ? MEUIAssets.icons.setting.faceid.svg(
                  width: 24.0,
                  colorFilter: ColorFilter.mode(
                    context.meTheme.captionTextColor,
                    BlendMode.srcIn,
                  ),
                )
              : MEUIAssets.icons.setting.finger.svg(
                  width: 24.0,
                  colorFilter: ColorFilter.mode(
                    context.meTheme.captionTextColor,
                    BlendMode.srcIn,
                  ),
                ),
          type: _PinKeyType.bio,
          action: _onKeyPressed,
        )
      else
        const SizedBox.shrink(),
      _PinKey(text: '0', action: _onKeyPressed),
      _PinKey(
        icon: MEUIAssets.icons.setting.backspace.svg(
          width: 24.0,
          colorFilter: ColorFilter.mode(
            context.meTheme.captionTextColor,
            BlendMode.srcIn,
          ),
        ),
        type: _PinKeyType.backspace,
        action: _onKeyPressed,
      ),
    ];
  }

  Widget _buildCenter(BuildContext context) {
    return Stack(
      children: <Widget>[
        AbsorbPointer(
          absorbing: _waiting,
          child: AnimatedOpacity(
            curve: Curves.ease,
            duration: const Duration(milliseconds: 300),
            opacity: _waiting ? 0 : 1,
            child: Column(
              children: <Widget>[
                const Gap.v(24),
                _buildPin(context),
                const Gap.v(48),
                _buildKeyboard(context),
                const Gap.v(24),
              ],
            ),
          ),
        ),
        Positioned.fill(child: _buildWaiting(context)),
      ],
    );
  }

  Widget _buildWaiting(BuildContext context) {
    return AnimatedOpacity(
      curve: Curves.ease,
      duration: const Duration(milliseconds: 300),
      opacity: _waiting ? 1 : 0,
      child: AnimatedSlide(
        curve: Curves.ease,
        duration: const Duration(milliseconds: 300),
        offset: _waiting ? Offset.zero : const Offset(0, 0.64),
        child: Center(
          child: ValueListenableBuilder<String>(
            valueListenable: _waitTime,
            builder: (_, String value, __) => IgnorePointer(
              child: _TimeCounter(
                text: value,
                style: TextStyle(
                  fontSize: textSize48,
                  fontWeight: FontWeight.bold,
                  color: _theme.primaryColor,
                  fontFamily: fontGotham,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildControls(BuildContext context) {
    return Row(
      children: <Widget>[
        if (widget.allowClose)
          TextButton(
            onPressed: () {
              Navigator.pop(context, false);
            },
            child: Text(
              globalL10nME.cancelButton,
              style: TextStyle(
                color: _theme.primaryColor,
                fontSize: textSize14,
              ),
            ),
          ),
        const Spacer(),
        TextButton(
          onPressed: () {
            _controller.clear();
            SecureVerify._onForgotPin(context);
          },
          child: Text(
            globalL10nME.pinForgotButton,
            style: context.textTheme.bodySmall?.copyWith(fontSize: textSize14),
          ),
        ),
      ],
    );
  }

  Widget _buildPin(BuildContext context) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _controller,
      builder: (_, TextEditingValue value, __) => Container(
        alignment: Alignment.center,
        width: 236,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: List<Widget>.generate(
            6,
            (int index) => Container(
              width: 36,
              height: 6,
              alignment: AlignmentDirectional.centerStart,
              decoration: BoxDecoration(
                borderRadius: RadiusConstants.r1,
                color: _theme.iconTheme.color,
              ),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 120),
                height: 6,
                width: value.text.length > index ? 36 : 0,
                curve: Curves.linearToEaseOut,
                decoration: BoxDecoration(
                  borderRadius: RadiusConstants.r1,
                  color: _notMatched ? failedColor : _theme.primaryColor,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildKeyboard(BuildContext context) {
    final keys = _buildKeys(context);
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 2,
        mainAxisSpacing: 8,
      ),
      itemBuilder: (_, int index) => keys[index],
      itemCount: keys.length,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
    );
  }

  Widget _buildMain(BuildContext context) {
    return Scaffold(
      backgroundColor: _theme.cardColor.withValues(alpha: 0.8),
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Spacer(),
              Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      globalL10nME.pinTitle,
                      style: context.textTheme.headlineMedium,
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsetsDirectional.only(top: 10),
                child: ValueListenableBuilder<String>(
                  valueListenable: _subtitle,
                  builder: (context, value, _) {
                    return PlaceholderText(
                      value,
                      style: context.textTheme.bodySmall?.copyWith(
                        fontSize: textSize14,
                        height: 1.25,
                      ),
                      onMatched: (_, __) => TextSpan(
                        text: _pinTimes == 1
                            ? _defaultCounter
                            : _pinTimes.toString(),
                        style: const TextStyle(color: failedColor),
                      ),
                    );
                  },
                ),
              ),
              const Spacer(flex: 2),
              _buildCenter(context),
              const Spacer(flex: 2),
              _buildControls(context),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final child = Theme(
      data: widget.theme,
      child: Builder(
        builder: (context) => BrightnessLayer(
          brightness: widget.theme.brightness,
          child: ClipRect(
            child: BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaY: 10, sigmaX: 10),
              child: _buildMain(context),
            ),
          ),
        ),
      ),
    );
    if (widget.allowClose) {
      return child;
    }
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          return;
        }
        final now = DateTime.now();
        if (_lastOperation == null ||
            now.difference(_lastOperation!) > const Duration(seconds: 2)) {
          showToast(globalL10nME.exitDoubleClickToast);
        } else if (now.difference(_lastOperation!) <=
            const Duration(seconds: 2)) {
          exitApp();
        }
        _lastOperation = now;
      },
      child: child,
    );
  }
}

class _PinKey extends StatefulWidget {
  const _PinKey({
    this.text,
    this.icon,
    this.type = _PinKeyType.number,
    required this.action,
  });

  final String? text;
  final Widget? icon;
  final _PinKeyType type;
  final void Function(_PinKeyType, String?) action;

  @override
  State<_PinKey> createState() => _PinKeyState();
}

class _PinKeyState extends State<_PinKey> with SingleTickerProviderStateMixin {
  late DateTime _clickTime;
  Matrix4 _transform = Matrix4.identity();

  void _onPressed() {
    HapticFeedback.lightImpact();
    widget.action(widget.type, widget.text);
  }

  Future<void> _tapEnd() async {
    final diff = DateTime.now().difference(_clickTime);
    if (diff < _kDuration) {
      await Future<void>.delayed(_kDuration - diff);
    }
    safeSetState(() {
      _transform = Matrix4.identity();
    });
  }

  void _tapStart() {
    _clickTime = DateTime.now();
    safeSetState(() {
      _transform = Matrix4.diagonal3Values(1.6, 1.6, 1);
    });
  }

  Widget _buildKeyItem() {
    switch (widget.type) {
      case _PinKeyType.number:
        return Text(
          widget.text!,
          style: context.textTheme.headlineMedium?.copyWith(
            fontFamily: fontGotham,
          ),
        );
      case _PinKeyType.bio:
      case _PinKeyType.backspace:
        return widget.icon!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkResponse(
      onTapDown: (_) => _tapStart(),
      onTapCancel: _tapEnd,
      onTapUp: (_) => _tapEnd(),
      onTap: _onPressed,
      child: AnimatedContainer(
        constraints: const BoxConstraints(minWidth: 80, minHeight: 40),
        duration: _kDuration,
        transform: _transform,
        transformAlignment: Alignment.center,
        child: Center(child: _buildKeyItem()),
      ),
    );
  }
}

class _TimeCounter extends StatefulWidget {
  const _TimeCounter({
    required this.text,
    required this.style,
  });

  final String text;
  final TextStyle style;

  @override
  State<_TimeCounter> createState() => _TimeCounterState();
}

class _TimeCounterState extends State<_TimeCounter> {
  late String _prev = widget.text;
  late double _maxWidth = _maxNumberWidth();

  @override
  void didUpdateWidget(_TimeCounter oldWidget) {
    super.didUpdateWidget(oldWidget);
    final styleChanged = oldWidget.style != widget.style;
    if (styleChanged) {
      _maxWidth = _maxNumberWidth();
    }
    if (oldWidget.text != widget.text || styleChanged) {
      _prev = oldWidget.text;
    }
  }

  double _maxNumberWidth() {
    return <String>['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'].map(
      (String e) {
        final textPainter = TextPainter(
          text: TextSpan(text: e, style: widget.style),
          textDirection: TextDirection.ltr,
          textScaler: TextScaler.linear(
            PlatformDispatcher.instance.textScaleFactor,
          ),
        )..layout();
        return textPainter.size.width;
      },
    ).reduce(math.max);
  }

  Widget _counterTransitionBuilder(
    ValueKey<String> key,
    bool up,
    Widget child,
    Animation<double> animation,
  ) {
    double dy = key == child.key ? -1 : 1;
    if (up) {
      dy = -dy;
    }
    return ClipRect(
      child: SlideTransition(
        position: Tween<Offset>(begin: Offset(0, dy), end: Offset.zero).animate(
          CurvedAnimation(parent: animation, curve: Curves.ease),
        ),
        child: child,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final lastChars = widget.text.split('');
    final prevChars = _prev.split('');
    final children = List<Widget>.generate(
      lastChars.length,
      (int ind) {
        final newChar = lastChars[ind];
        final oldChar = prevChars[ind];
        final key = ValueKey<String>(newChar);
        final up =
            (int.tryParse(newChar) ?? 0) - (int.tryParse(oldChar) ?? 0) >= 0;
        return ConstrainedBox(
          constraints: BoxConstraints(minWidth: _maxWidth),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return _counterTransitionBuilder(key, up, child, animation);
            },
            child: Text(newChar, key: key, style: widget.style),
          ),
        );
      },
    );
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}

Future<bool> trySetupBiometric() async {
  final auth = LocalAuthentication();
  if (!await auth.isDeviceSupported()) {
    return false;
  }
  final v = await ScrollableBottomSheet.show<bool>(
    builder: (context) => ScrollableBottomSheet(
      title: context.l10nME.bioVerifyTitle,
      description: context.l10nME.bioVerifyText,
      bottomBuilder: (context) => ThemeTextButtonGroup(
        onConfirm: () async {
          final result = await auth.authenticate(
            localizedReason: context.l10nME.pinTitle,
          );
          await SecureVerify.setAuthAt();
          if (result) {
            meNavigator.pop(true);
          }
        },
      ),
      sliversBuilder: (context) => [
        SliverFillRemaining(
          child: Center(
            child: io.Platform.isIOS
                ? MEUIAssets.icons.setting.faceid.svg(width: 64.0)
                : MEUIAssets.icons.setting.finger.svg(width: 64.0),
          ),
        ),
      ],
    ),
  );
  return v ?? false;
}

Future<bool> verifyBiometric({
  bool sensitiveTransaction = false,
  String? localizedReason,
}) async {
  assert(localizedReason == null || localizedReason.isNotEmpty);
  try {
    return LocalAuthentication().authenticate(
      localizedReason: localizedReason ?? globalL10nME.pinProceedText,
      authMessages: [
        IOSAuthMessages(cancelButton: globalL10nME.cancelButton),
        AndroidAuthMessages(
          biometricHint: '',
          signInTitle: globalL10nME.bioVerifyTitle,
          cancelButton: globalL10nME.cancelButton,
        ),
      ],
      options: AuthenticationOptions(
        biometricOnly: true,
        sensitiveTransaction: sensitiveTransaction,
        useErrorDialogs: false,
      ),
    );
  } catch (e, s) {
    SecureVerify._onException(error: e, stackTrace: s);
    return false;
  }
}

Future<bool> stopBiometricVerify() {
  return LocalAuthentication().stopAuthentication();
}
