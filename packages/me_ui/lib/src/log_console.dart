// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:async';
import 'dart:math' as math;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_utils/me_utils.dart';

import 'ripple_tap.dart';
import 'toast.dart';

class LogConsole extends StatefulWidget {
  const LogConsole._({
    super.key,
    required this.child,
    this.enable = false,
  });

  final Widget child;
  final bool enable;

  static Widget wrap(Widget child, {bool enable = false}) {
    return LogConsole._(key: _floatLogKey, enable: enable, child: child);
  }

  static void enableLog(bool enable) {
    _floatLogKey.currentState?.enableLogView = enable;
  }

  static bool isEnabled() {
    return _floatLogKey.currentState?._enableLog == true;
  }

  @override
  State<LogConsole> createState() => _LogConsoleState();
}

final GlobalKey<OverlayState> _overlayKey = GlobalKey<OverlayState>();
final GlobalKey<_LogConsoleState> _floatLogKey = GlobalKey<_LogConsoleState>();

const int _kMaxLimitLogs = 500;
const Duration _kDuration = Duration(milliseconds: 120);

class _LogConsoleState extends State<LogConsole> {
  late final _logs = NewValueNotifier<List<LogEvent>>(<LogEvent>[]);
  late final _reversed = ValueNotifier<bool>(true);
  late OverlayEntry _circle;
  late OverlayEntry _terminal;
  late bool _enableLog = widget.enable;

  StreamSubscription<LogEvent>? _subscription;

  set enableLogView(bool value) {
    if (value == _enableLog || !mounted) {
      return;
    }
    _enableLog = value;
    safeSetState(() {});
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      if (!mounted) {
        return;
      }
      _switchLogState(value);
    });
  }

  @override
  void initState() {
    super.initState();
    if (_enableLog) {
      WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
        _injectFloatView();
        _listenLog();
      });
    }
  }

  @override
  void didUpdateWidget(LogConsole oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.enable != widget.enable) {
      _switchLogState(widget.enable);
    }
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  void _switchLogState(bool value) {
    if (value) {
      _injectFloatView();
      _listenLog();
    } else {
      _cancelListen();
      _clearOverlay();
    }
  }

  void _injectFloatView() {
    _circle = _newCircle();
    _terminal = _newTerminal();
    _overlayKey.currentState?.insert(_circle);
    _overlayKey.currentState?.insert(_terminal);
  }

  void _clearOverlay() {
    _circle.remove();
    _terminal.remove();
  }

  void _listenLog() {
    _subscription?.cancel();
    _subscription = LogUtil.addLogListener((LogEvent event) {
      List<LogEvent> value = _logs.value..add(event);
      if (value.length >= _kMaxLimitLogs) {
        value = value.sublist(value.length - _kMaxLimitLogs, value.length);
      }
      _logs.newValue(value);
    });
  }

  void _cancelListen() {
    _subscription?.cancel();
  }

  OverlayEntry _newTerminal() {
    return OverlayEntry(
      builder: (BuildContext context) => _LogTerminal(
        onClose: () => _reversed.value = true,
        logs: _logs,
        hiding: _reversed,
      ),
    );
  }

  OverlayEntry _newCircle() {
    return OverlayEntry(
      builder: (BuildContext context) => _FloatingCircle(
        logs: _logs,
        showing: _reversed,
        onTap: () {
          _reversed.value = false;
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_enableLog) {
      return widget.child;
    }
    return Stack(
      children: <Widget>[
        Positioned.fill(child: widget.child),
        Overlay(key: _overlayKey),
      ],
    );
  }
}

const double _kCircleSize = 48;
const double _kHideSize = _kCircleSize / 3;

class _FloatingCircle extends StatefulWidget {
  const _FloatingCircle({
    required this.logs,
    required this.onTap,
    required this.showing,
  });

  final ValueListenable<List<LogEvent>> logs;
  final ValueListenable<bool> showing;
  final VoidCallback onTap;

  @override
  State<_FloatingCircle> createState() => _FloatingCircleState();
}

class _FloatingCircleState extends State<_FloatingCircle>
    with WidgetsBindingObserver {
  /// Initialize the circle to the near center right.
  late Offset _offset = Offset(
    context.mediaQuery.size.width - (_kCircleSize - _kHideSize),
    context.mediaQuery.size.height / 2,
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    _offset = Offset(
      _offset.dx < 0
          ? _offset.dx
          : context.mediaQuery.size.width - (_kCircleSize - _kHideSize),
      math.min(
        _offset.dy,
        context.mediaQuery.size.height,
      ),
    );
    setState(() {});
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void _onDragUpdate(DragUpdateDetails details) {
    _offset = Offset(
      details.globalPosition.dx - _kCircleSize / 2,
      details.globalPosition.dy - _kCircleSize / 2,
    );
    setState(() {});
  }

  void _onDragEnd(DragEndDetails details) {
    final double width = MediaQuery.of(context).size.width;
    final double half = width / 2;
    if (_offset.dx > half) {
      _offset = Offset(width - (_kCircleSize - _kHideSize), _offset.dy);
    } else {
      _offset = Offset(-_kHideSize, _offset.dy);
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedPositionedDirectional(
      start: _offset.dx,
      top: _offset.dy,
      duration: _kDuration,
      child: ValueListenableBuilder<bool>(
        valueListenable: widget.showing,
        builder: (_, bool value, Widget? child) => AnimatedScale(
          duration: const Duration(milliseconds: 240),
          scale: value ? 1.0 : 0.0,
          alignment: Alignment.bottomCenter,
          child: child,
        ),
        child: Material(
          type: MaterialType.transparency,
          child: GestureDetector(
            onHorizontalDragUpdate: _onDragUpdate,
            onVerticalDragUpdate: _onDragUpdate,
            onHorizontalDragEnd: _onDragEnd,
            onVerticalDragEnd: _onDragEnd,
            child: ValueListenableBuilder<List<LogEvent>>(
              valueListenable: widget.logs,
              builder: (_, List<LogEvent> value, Widget? child) => Stack(
                children: <Widget>[
                  child!,
                  ValueListenableBuilder<List<LogEvent>>(
                    valueListenable: widget.logs,
                    builder: (BuildContext context, List<LogEvent> value, _) {
                      if (value.isEmpty) {
                        return const SizedBox.shrink();
                      }
                      return PositionedDirectional(
                        end: _kHideSize,
                        child: Container(
                          constraints: const BoxConstraints(
                            minWidth: _kCircleSize / 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              value.length.toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10.0,
                                height: 1.25,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
              child: InkResponse(
                onTap: widget.onTap,
                onDoubleTap: () => LogConsole.enableLog(false),
                radius: _kCircleSize / 2,
                child: Container(
                  width: _kCircleSize,
                  height: _kCircleSize,
                  decoration: BoxDecoration(
                    color: context.theme.cardColor,
                    borderRadius: BorderRadius.circular(_kCircleSize / 2),
                    boxShadow: <BoxShadow>[
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.24),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.terminal_rounded,
                    color: context.textTheme.bodyLarge?.color,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

enum _TagStyle { none, short, origin }

class _LogTerminal extends StatefulWidget {
  const _LogTerminal({
    required this.logs,
    required this.onClose,
    required this.hiding,
  });

  final NewValueNotifier<List<LogEvent>> logs;
  final ValueListenable<bool> hiding;
  final VoidCallback onClose;

  @override
  State<_LogTerminal> createState() => _LogTerminalState();
}

class _LogTerminalState extends State<_LogTerminal> {
  final _scrollController = ScrollController();
  final _wrap = ValueNotifier<bool>(true);
  final _time = ValueNotifier<bool>(false);
  final _expanded = ValueNotifier<bool>(false);
  final _tag = ValueNotifier<_TagStyle>(_TagStyle.origin);
  final _filter = ValueNotifier<bool>(false);
  final _isBottom = ValueNotifier<bool>(true);
  final _filterController = TextEditingController();
  Timer? _scrollTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.logs.addListener(_controlScroll);
    });
  }

  @override
  void dispose() {
    widget.logs.removeListener(_controlScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _controlScroll() {
    if (!mounted || !_isBottom.value) {
      _scrollTimer?.cancel();
      return;
    }
    _scrollTimer?.cancel();
    _scrollTimer = Timer(const Duration(milliseconds: 500), () {
      _scrollToBottom();
    });
  }

  void _scrollToBottom() {
    if (mounted) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: _kDuration,
        curve: Curves.easeInOut,
      );
    }
  }

  bool _matchEvent(LogEvent event, String filter) {
    try {
      final RegExp regExp = RegExp(filter, dotAll: true, caseSensitive: false);
      return regExp.hasMatch(event.toString());
    } catch (e) {
      return false;
    }
  }

  Widget _buildFilter(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: _filter,
      builder: (BuildContext context, bool value, Widget? child) {
        if (!value) {
          return const SizedBox.shrink();
        }
        return Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ).add(const EdgeInsetsDirectional.only(top: 16)),
          child: TextField(
            controller: _filterController,
            style: const TextStyle(fontSize: 12, fontFamily: 'Gotham'),
            decoration: InputDecoration(
              constraints: const BoxConstraints(maxHeight: 32),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              hintText: 'Please input keywords or regex.',
              suffixIcon: ValueListenableBuilder<TextEditingValue>(
                valueListenable: _filterController,
                builder: (_, TextEditingValue value, __) {
                  if (value.text.isEmpty) {
                    return const SizedBox.shrink();
                  }
                  return IconButton(
                    onPressed: () {
                      _filterController.clear();
                    },
                    icon: Icon(
                      Icons.clear_rounded,
                      size: 18,
                      color: context.theme.primaryColor,
                    ),
                  );
                },
              ),
              suffixIconConstraints: const BoxConstraints(maxHeight: 32.0),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLogs(BuildContext context) {
    return Expanded(
      child: NotificationListener<ScrollEndNotification>(
        onNotification: (ScrollEndNotification notification) {
          Future.microtask(() {
            _isBottom.value = notification.metrics.pixels >=
                _scrollController.position.maxScrollExtent;
          });
          return true;
        },
        child: Scrollbar(
          child: ValueListenableBuilder<List<LogEvent>>(
            valueListenable: widget.logs,
            builder: (_, List<LogEvent> logs, __) => ValueListenableBuilder(
              valueListenable: _filter,
              builder: (_, bool filter, __) {
                if (filter) {
                  return ValueListenableBuilder<TextEditingValue>(
                    valueListenable: _filterController,
                    builder: (BuildContext context, TextEditingValue text, __) {
                      logs =
                          logs.where((e) => _matchEvent(e, text.text)).toList();
                      if (logs.isEmpty && text.text.isNotEmpty) {
                        return Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Text(
                            'No result.',
                            style: TextStyle(
                              fontSize: 12,
                              color: context.themeColorOrNull ??
                                  context.colorScheme.primary,
                            ),
                          ),
                        );
                      }
                      return _buildLogList(logs);
                    },
                  );
                }
                return _buildLogList(logs);
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogList(Iterable<LogEvent> logs) {
    return ValueListenableBuilder(
      valueListenable: _time,
      builder: (context, time, child) {
        return ListView.separated(
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(vertical: 16),
          itemCount: logs.length,
          itemBuilder: (BuildContext context, int index) {
            final LogEvent log = logs.elementAt(index);
            final logLine = RippleTap(
              onLongPress: () => copyAndToast(log.toString()),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 4.0,
                ),
                child: ValueListenableBuilder<bool>(
                  valueListenable: _wrap,
                  builder: (BuildContext context, bool wrap, _) {
                    return _buildLogLine(context, wrap, log);
                  },
                ),
              ),
            );
            return time
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 16.0,
                          right: 16.0,
                          top: 4.0,
                        ),
                        child: Text(
                          log.dateTime.toIso8601String(),
                          style: const TextStyle(fontSize: 8.0),
                        ),
                      ),
                      logLine,
                    ],
                  )
                : logLine;
          },
          separatorBuilder: (context, index) {
            return const Divider(
              thickness: 1.0,
              height: 1.0,
            );
          },
        );
      },
    );
  }

  Widget _buildControlBar(BuildContext context) {
    return Container(
      height: 40,
      decoration: BoxDecoration(color: context.theme.dividerColor),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  RippleTap(
                    onTap: () {
                      _wrap.value = !_wrap.value;
                    },
                    child: AspectRatio(
                      aspectRatio: 1.0,
                      child: ValueListenableBuilder<bool>(
                        valueListenable: _wrap,
                        builder: (BuildContext context, bool wrap, _) => Icon(
                          Icons.wrap_text_rounded,
                          color: wrap
                              ? context.themeColorOrNull ??
                                  context.colorScheme.primary
                              : context.textTheme.bodyLarge?.color,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                  RippleTap(
                    onTap: () {
                      final _TagStyle value = _tag.value;
                      switch (value) {
                        case _TagStyle.none:
                          _tag.value = _TagStyle.origin;
                        case _TagStyle.short:
                          _tag.value = _TagStyle.none;
                        case _TagStyle.origin:
                          _tag.value = _TagStyle.short;
                      }
                    },
                    child: AspectRatio(
                      aspectRatio: 1.0,
                      child: ValueListenableBuilder<_TagStyle>(
                        valueListenable: _tag,
                        builder: (BuildContext context, _TagStyle style, _) {
                          return Icon(
                            style == _TagStyle.origin
                                ? Icons.tag
                                : style == _TagStyle.short
                                    ? Icons.short_text_rounded
                                    : Icons.not_interested_rounded,
                            color: style == _TagStyle.none
                                ? context.textTheme.bodyLarge?.color
                                : context.themeColorOrNull ??
                                    context.colorScheme.primary,
                            size: 16,
                          );
                        },
                      ),
                    ),
                  ),
                  RippleTap(
                    onTap: () {
                      _time.value = !_time.value;
                    },
                    child: AspectRatio(
                      aspectRatio: 1.0,
                      child: ValueListenableBuilder<bool>(
                        valueListenable: _time,
                        builder: (BuildContext context, bool time, _) => Icon(
                          Icons.access_time_rounded,
                          color: time
                              ? context.themeColorOrNull ??
                                  context.colorScheme.primary
                              : context.textTheme.bodyLarge?.color,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                  RippleTap(
                    onTap: () {
                      _filter.value = !_filter.value;
                    },
                    child: AspectRatio(
                      aspectRatio: 1.0,
                      child: ValueListenableBuilder<bool>(
                        valueListenable: _filter,
                        builder: (BuildContext context, bool filter, _) {
                          return Icon(
                            filter
                                ? Icons.filter_list_off_rounded
                                : Icons.filter_list_rounded,
                            color: context.themeColorOrNull ??
                                context.colorScheme.primary,
                            size: 16,
                          );
                        },
                      ),
                    ),
                  ),
                  RippleTap(
                    onTap: () {
                      copyAndToast(
                        widget.logs.value.map((e) => e.toString()).join('\n'),
                      );
                    },
                    child: AspectRatio(
                      aspectRatio: 1.0,
                      child: Icon(
                        Icons.copy_all_rounded,
                        color: context.themeColorOrNull ??
                            context.colorScheme.primary,
                        size: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          RippleTap(
            onTap: () {
              widget.logs.newValue([]);
            },
            child: AspectRatio(
              aspectRatio: 1.0,
              child: Icon(
                Icons.clear_all_rounded,
                color: Theme.of(context).extension<METheme>()?.failingColor,
                size: 16,
              ),
            ),
          ),
          RippleTap(
            onTap: _scrollToBottom,
            child: AspectRatio(
              aspectRatio: 1.0,
              child: ValueListenableBuilder<bool>(
                valueListenable: _isBottom,
                builder: (BuildContext context, bool bottom, _) => Icon(
                  Icons.keyboard_double_arrow_down_rounded,
                  color: bottom
                      ? context.iconTheme.color
                      : context.themeColorOrNull ?? context.colorScheme.primary,
                  size: 16,
                ),
              ),
            ),
          ),
          RippleTap(
            onTap: () {
              _expanded.value = !_expanded.value;
            },
            child: AspectRatio(
              aspectRatio: 1.0,
              child: ValueListenableBuilder<bool>(
                valueListenable: _expanded,
                builder: (BuildContext context, bool expanded, _) => Icon(
                  expanded
                      ? Icons.fullscreen_exit_rounded
                      : Icons.fullscreen_rounded,
                  color:
                      context.themeColorOrNull ?? context.colorScheme.primary,
                  size: 16,
                ),
              ),
            ),
          ),
          RippleTap(
            onTap: widget.onClose,
            child: AspectRatio(
              aspectRatio: 1.0,
              child: Icon(
                Icons.keyboard_arrow_down_rounded,
                color: context.themeColorOrNull ?? context.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogLine(BuildContext context, bool wrap, LogEvent log) {
    return ValueListenableBuilder<_TagStyle>(
      valueListenable: _tag,
      builder: (BuildContext context, _TagStyle style, _) => Text.rich(
        TextSpan(
          children: <InlineSpan>[
            if (style != _TagStyle.none) ...<InlineSpan>[
              TextSpan(
                text: (style == _TagStyle.short || !wrap) && log.tag.length > 12
                    ? log.tag.replaceRange(6, log.tag.length - 6, '..')
                    : log.tag,
                style: TextStyle(
                  color:
                      context.themeColorOrNull ?? context.colorScheme.primary,
                ),
              ),
              const WidgetSpan(child: SizedBox(width: 8.0)),
            ],
            TextSpan(text: log.message.toString().trim()),
            if (log.stackTrace != null && log.stackTrace != StackTrace.empty)
              TextSpan(text: '\n${log.stackTrace}'),
          ],
        ),
        style: TextStyle(
          color: log.isError
              ? Theme.of(context).extension<METheme>()?.failingColor
              : null,
          height: 1.25,
          fontSize: 8.0,
          fontFamily: 'None',
          fontFamilyFallback: const ['Monospace', 'Courier'],
        ),
        maxLines: wrap ? null : 2,
        overflow: wrap ? null : TextOverflow.ellipsis,
        softWrap: wrap,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: _expanded,
      builder: (BuildContext context, bool expanded, Widget? child) {
        final MediaQueryData query = context.mediaQuery;
        return AnimatedPositionedDirectional(
          duration: _kDuration,
          start: 0,
          end: 0,
          bottom: 0,
          height: expanded
              ? query.size.height
              : query.size.height / 3 + query.viewInsets.bottom,
          child: ValueListenableBuilder<bool>(
            valueListenable: widget.hiding,
            builder: (BuildContext context, bool hiding, _) => AnimatedSlide(
              curve: Curves.easeInOut,
              duration: const Duration(milliseconds: 240),
              offset: Offset(0, hiding ? 1 : 0),
              child: Material(
                type: MaterialType.transparency,
                child: Container(
                  decoration: BoxDecoration(color: context.theme.cardColor),
                  padding: EdgeInsetsDirectional.only(
                    top: expanded ? query.padding.top : 0,
                  ),
                  child: Column(
                    children: <Widget>[
                      _buildControlBar(context),
                      _buildFilter(context),
                      _buildLogs(context),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
