// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_misc/me_misc.dart';

import 'button/me_back_button.dart';
import 'configs/all.dart';
import 'me_image.dart';
import 'sliver/headers.dart';
import 'text/placeholder_text.dart';

class MEScaffold extends StatelessWidget {
  const MEScaffold({
    super.key,
    required this.body,
    this.padding = scaffoldTitleTextPadding,
    this.bodyPadding,
    this.resizeToAvoidBottomInset = false,
    this.automaticallyImplyLeading = true,
    this.onBackButtonPressed,
    this.backButtonPadding = const EdgeInsets.symmetric(horizontal: 24),
    this.backgroundColor,
    this.leading,
    this.alwaysShowBackButton = false,
    this.actions = const <Widget>[],
  });

  final Widget body;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry? bodyPadding;
  final bool? resizeToAvoidBottomInset;
  final bool automaticallyImplyLeading;
  final VoidCallback? onBackButtonPressed;
  final EdgeInsetsGeometry backButtonPadding;
  final Color? backgroundColor;
  final Widget? leading;
  final List<Widget> actions;
  final bool alwaysShowBackButton;

  static Future<void> toPreviousPage(PageController controller) async {
    if (!controller.hasClients) {
      return;
    }
    final int page = (controller.page ?? 0).toInt() - 1;
    if (page < 0) {
      meNavigator.maybePop();
      return;
    }
    return controller.previousPage(
      duration: kTabScrollDuration,
      curve: Curves.fastOutSlowIn,
    );
  }

  static Future<void> toNextPage(PageController controller) async {
    if (!controller.hasClients) {
      return;
    }
    return controller.nextPage(
      duration: kTabScrollDuration,
      curve: Curves.fastOutSlowIn,
    );
  }

  bool _showBackButton(BuildContext context) {
    return leading == null &&
        automaticallyImplyLeading &&
        Navigator.of(context).canPop();
  }

  bool _showHeadingBar(BuildContext context) {
    return leading != null ||
        _showBackButton(context) ||
        actions.isNotEmpty ||
        alwaysShowBackButton;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      body: SafeArea(
        child: Container(
          width: double.maxFinite,
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              if (_showHeadingBar(context))
                Row(
                  children: <Widget>[
                    if (leading != null) leading!,
                    if (_showBackButton(context) || alwaysShowBackButton)
                      Padding(
                        padding: backButtonPadding,
                        child: MEBackButton(onPressed: onBackButtonPressed),
                      ),
                    if (actions.isNotEmpty)
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: actions,
                        ),
                      ),
                  ],
                ),
              Expanded(
                child: Padding(
                  padding: bodyPadding ?? EdgeInsets.zero,
                  child: body,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

const _leadingBoxConstraints = BoxConstraints.tightFor(
  width: kMinInteractiveDimension,
  height: kMinInteractiveDimension,
);

class MEScaffoldWithStackHeader extends StatelessWidget {
  const MEScaffoldWithStackHeader({
    super.key,
    this.sliversBuilder,
    this.icon,
    this.iconBuilder,
    this.iconPackage,
    this.title,
    this.titleStyle,
    this.titleMatchedStyle,
    this.onTitleMatched,
    this.headerPadding,
    this.headerBackgroundColorTween,
    this.subtitle,
    this.subtitleStyle,
    this.subtitleMatchedStyle,
    this.onSubtitleMatched,
    this.actions = const <Widget>[],
    this.bottomButtonBuilder,
    this.onRefresh,
    this.refreshKey,
    this.onBackPressed,
    this.backgroundColor,
    this.bottomButtonBackgroundColor,
    this.horizontalPadding = 24.0,
    this.leading,
    this.showBackButton,
    this.controller,
    this.wrapHorizontalPaddingForSlivers = true,
    this.resizeToAvoidBottomInset = false,
  });

  final Iterable<Widget> Function(BuildContext)? sliversBuilder;
  final String? icon;
  final WidgetBuilder? iconBuilder;
  final String? iconPackage;
  final String? title;
  final TextStyle? titleStyle;
  final TextStyle? titleMatchedStyle;
  final InlineSpan Function(int position, Match matched)? onTitleMatched;
  final EdgeInsetsGeometry? headerPadding;
  final ColorTween? headerBackgroundColorTween;
  final String? subtitle;
  final TextStyle? subtitleStyle;
  final TextStyle? subtitleMatchedStyle;
  final InlineSpan Function(int position, Match matched)? onSubtitleMatched;
  final List<Widget> actions;
  final WidgetBuilder? bottomButtonBuilder;
  final Color? backgroundColor;
  final Color? bottomButtonBackgroundColor;
  final double horizontalPadding;
  final bool wrapHorizontalPaddingForSlivers;
  final Widget? leading;
  final bool? showBackButton;
  final bool resizeToAvoidBottomInset;
  final VoidCallback? onBackPressed;
  final ScrollController? controller;

  /// A function that's called when the user has dragged the refresh indicator
  /// far enough to demonstrate that they want the app to refresh. The returned
  /// [Future] must complete when the refresh operation is finished.
  final RefreshCallback? onRefresh;
  final GlobalKey<RefreshIndicatorState>? refreshKey;

  Widget _buildHeader(BuildContext context) {
    final noIcon = (icon == null || icon!.isEmpty) && iconBuilder == null;
    final showLeading =
        leading != null || (showBackButton ?? Navigator.of(context).canPop());
    return StackSliverPinnedHeader(
      maxExtent: MediaQuery.paddingOf(context).top +
          96.0 +
          (noIcon ? 0 : 50) +
          (title == null ? 0 : 44),
      minExtent: MediaQuery.paddingOf(context).top + 64.0,
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
      ).add(headerPadding ?? EdgeInsets.zero),
      backgroundColorTween: headerBackgroundColorTween ??
          MEUIConfig.scaffoldConfig.headerBackgroundColorTween,
      childrenBuilder: (context, maxOffset, ratio) => <Widget>[
        if (!noIcon)
          PositionedDirectional(
            start: 0.0,
            top: 86.0 - 68.0 * ratio,
            child: Opacity(
              opacity: (1.0 - ratio * 1.5).clamp(0.0, 1.0),
              child: Transform.scale(
                scale: 1.0 - ratio * 0.27,
                child: iconBuilder == null
                    ? MEImage(
                        icon!,
                        height: 54.0,
                        alignment: AlignmentDirectional.topStart,
                        package: iconPackage,
                      )
                    : iconBuilder!(context),
              ),
            ),
          ),
        if (title != null)
          PositionedDirectional(
            start: showLeading ? 44.0 * ratio : 0.0,
            top: noIcon ? 98.0 - 78.0 * ratio : 150.0 - 130.0 * ratio,
            end: 0.0,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              alignment: AlignmentDirectional.centerStart,
              child: PlaceholderText(
                title!,
                style: titleStyle ??
                    TextStyle(
                      fontSize: 28.0 - 6.0 * ratio,
                      fontWeight: FontWeight.w700,
                      height: 1.25,
                    ),
                matchedStyle: titleMatchedStyle,
                onMatched: onTitleMatched,
              ),
            ),
          ),
        if (showLeading)
          PositionedDirectional(
            start: -9.0,
            top: 10.0,
            child: leading != null
                ? ConstrainedBox(
                    constraints: _leadingBoxConstraints,
                    child: leading,
                  )
                : MEBackButton(
                    onPressed: onBackPressed,
                    constraints: _leadingBoxConstraints,
                    padding: const EdgeInsets.all(9.0),
                  ),
          ),
        if (actions.isNotEmpty)
          PositionedDirectional(
            start: 35.0,
            end: 0,
            top: 10.0,
            height: kMinInteractiveDimension,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: actions,
            ),
          ),
      ],
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      sliver: SliverToBoxAdapter(
        child: PlaceholderText(
          subtitle!,
          style: subtitleStyle ??
              Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontSize: 16.0,
                    height: 1.25,
                  ),
          matchedStyle: subtitleMatchedStyle,
          onMatched: onSubtitleMatched,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final slivers = [
      if (subtitle != null) _buildSubtitle(context),
      if (sliversBuilder != null) ...sliversBuilder!(context),
    ];
    Widget body = CustomScrollView(
      controller: controller,
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      slivers: <Widget>[
        _buildHeader(context),
        if (wrapHorizontalPaddingForSlivers)
          ...slivers.map(
            (sliver) => SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
              sliver: sliver,
            ),
          )
        else
          ...slivers,
        if (bottomButtonBuilder != null)
          const SliverToBoxAdapter(child: SizedBox(height: 30.0))
        else
          SliverToBoxAdapter(
            child: SizedBox(
              height: 30.0 + MediaQuery.paddingOf(context).bottom,
            ),
          ),
      ],
    );
    if (onRefresh != null) {
      body = RefreshIndicator(
        key: refreshKey,
        onRefresh: onRefresh!,
        color: Theme.of(context).extension<METheme>()?.themeColor,
        displacement: Screens.topSafeHeight + 40.0,
        child: body,
      );
    }
    if (bottomButtonBuilder != null) {
      body = Column(
        children: <Widget>[
          Expanded(child: body),
          AnimatedContainer(
            padding: EdgeInsets.symmetric(horizontal: horizontalPadding).add(
              EdgeInsets.only(
                top: bottomBarPadding.top,
                bottom: MediaQuery.viewInsetsOf(context).bottom > 0
                    ? 24.0
                    : bottomBarPadding.bottom,
              ),
            ),
            decoration: bottomBarDecoration(
              context,
              backgroundColor: bottomButtonBackgroundColor ??
                  MEUIConfig.scaffoldConfig.bottomButtonBackgroundColor,
            ),
            duration: kThemeChangeDuration,
            child: bottomButtonBuilder!(context),
          ),
        ],
      );
    }
    return Scaffold(
      backgroundColor: backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      body: body,
    );
  }
}
