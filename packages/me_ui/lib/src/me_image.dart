// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:convert';
import 'dart:io' as io show File;
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:jovial_svg/jovial_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:me_misc/me_misc.dart';
import 'package:path/path.dart' as p;

class MEImage extends StatelessWidget {
  MEImage(
    this.image, {
    super.key,
    this.bundle,
    this.package,
    this.width,
    this.height,
    this.color,
    this.backgroundColor,
    this.fit,
    this.alignment = Alignment.center,
    this.cacheWidth,
    this.cacheHeight,
    this.borderRadius = BorderRadius.zero,
    this.clipOval = false,
    this.filterQuality,
    this.errorBuilder,
    this.emptyBuilder,
    this.alternativeSVG = false,
  });

  final String image;
  final AssetBundle? bundle;
  final String? package;
  final double? width;
  final double? height;
  final Color? color;
  final Color? backgroundColor;
  final BoxFit? fit;
  final AlignmentGeometry alignment;
  final int? cacheWidth;
  final int? cacheHeight;
  final BorderRadiusGeometry borderRadius;
  final bool clipOval;
  final FilterQuality? filterQuality;
  final ImageErrorWidgetBuilder? errorBuilder;
  final WidgetBuilder? emptyBuilder;
  final bool alternativeSVG;

  static Future<io.File?> getCachedFileIfExists(String url) =>
      CacheImageProvider.getCachedFileIfExists(url);

  static Future<io.File> getCachedFile(String url) =>
      CacheImageProvider.getCachedFile(url);

  late final _path = image.trim();
  late final _pathBase = p.basename(Uri.parse(_path).path);
  late final isNetwork = _path.startsWith(
    RegExp(r'http(s?)://', caseSensitive: false),
  );
  late final isBase64 = RegExp(
    r'data:.+;base64,',
    caseSensitive: false,
  ).hasMatch(_path);

  late final _extension =
      p.extension(_pathBase).toLowerCase().replaceFirst('.', '');
  late final isSVG = _extension == 'svg';
  late final isLottie = _extension == 'json';

  @override
  Widget build(BuildContext context) {
    Widget child;
    if (_path.isEmpty) {
      child = emptyBuilder?.call(context) ?? const SizedBox.shrink();
    } else if (isNetwork) {
      if (isSVG) {
        if (alternativeSVG) {
          child = SizedBox(
            width: width,
            height: height,
            child: ScalableImageWidget.fromSISource(
              si: ScalableImageCacheHttpSource(Uri.parse(_path)),
              fit: fit ?? BoxFit.contain,
              currentColor: color,
              // alignment: alignment,
            ),
          );
        } else {
          child = SvgPicture(
            CachedSvgNetworkBytesLoader(_path),
            width: width,
            height: height,
            colorFilter: color != null
                ? ColorFilter.mode(color!, BlendMode.srcIn)
                : null,
            // Backward compatibility for SVG.
            fit: fit ?? BoxFit.contain,
            alignment: alignment,
          );
        }
      } else if (isLottie) {
        child = Lottie.network(
          _path,
          width: width,
          height: height,
          fit: fit,
          alignment: alignment,
          errorBuilder: errorBuilder,
          filterQuality: filterQuality,
        );
      } else {
        child = Image(
          image: CacheImageProvider.resizeIfNeeded(
            image,
            cacheWidth,
            cacheHeight,
          ),
          width: width,
          height: height,
          color: color,
          fit: fit,
          alignment: alignment,
          filterQuality: filterQuality ?? FilterQuality.medium,
          errorBuilder: errorBuilder,
        );
      }
    } else if (isSVG) {
      child = SvgPicture.asset(
        image,
        bundle: bundle,
        package: package,
        width: width,
        height: height,
        colorFilter:
            color != null ? ColorFilter.mode(color!, BlendMode.srcIn) : null,
        // Backward compatibility for SVG.
        fit: fit ?? BoxFit.contain,
        alignment: alignment,
      );
    } else if (isLottie) {
      child = Lottie.asset(
        image,
        bundle: bundle,
        package: package,
        width: width,
        height: height,
        fit: fit,
        alignment: alignment,
        filterQuality: filterQuality,
        errorBuilder: errorBuilder,
      );
    } else if (isBase64) {
      final data = image.split('base64,').last;
      final bytes = Uint8List.fromList(base64Decode(data));
      child = Image.memory(
        bytes,
        width: width,
        height: height,
        color: color,
        fit: fit,
        alignment: alignment,
        cacheWidth: cacheWidth,
        filterQuality: filterQuality ?? FilterQuality.medium,
        errorBuilder: errorBuilder,
      );
    } else {
      child = Image.asset(
        image,
        bundle: bundle,
        package: package,
        width: width,
        height: height,
        color: color,
        fit: fit,
        alignment: alignment,
        cacheWidth: cacheWidth,
        filterQuality: filterQuality ?? FilterQuality.medium,
        errorBuilder: errorBuilder,
      );
    }
    if (backgroundColor case final color?) {
      child = ColoredBox(color: color, child: child);
    }
    if (width != null || height != null) {
      child = SizedBox(width: width, height: height, child: child);
    }
    if (borderRadius != BorderRadius.zero) {
      child = ClipRRect(borderRadius: borderRadius, child: child);
    }
    if (clipOval) {
      child = ClipOval(child: child);
    }
    return child;
  }
}
