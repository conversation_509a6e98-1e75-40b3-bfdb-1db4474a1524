// Copyright 2025 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

abstract class IMEAnalytics {
  final Map<String, Object> _defaultParameters = {};

  Map<String, Object>? formalizeParameters(Map<String, Object?>? parameters) {
    if (parameters == null || parameters.isEmpty) {
      return null;
    }

    final map = Map<String, Object>.fromEntries(
      parameters.entries
          .map(
            (e) => switch (e.value) {
              final v? => MapEntry<String, Object>(e.key, v),
              _ => null,
            },
          )
          .nonNulls,
    );

    Object write(Object v) {
      if (v is Function || v is Symbol) {
        throw ArgumentError.value(
          v,
          'value',
          '${v.runtimeType} is not supported.',
        );
      }

      if (v is Map) {
        final map = Map.fromEntries(
          v.entries
              .map(
                (e) => e.key == null || e.value == null ? null : e,
              )
              .nonNulls,
        );
        return map.map((k, v) => MapEntry(write(k), write(v)));
      }

      if (v is Iterable) {
        return v.nonNulls.map(write).toList();
      }

      if (v is Record) {
        return v.toString();
      }

      if (v is DateTime) {
        return v.toIso8601String();
      }

      if (v is Duration) {
        return v.inMilliseconds;
      }

      if (v is Runes) {
        return v.string;
      }

      if (v is Enum) {
        return v.name;
      }

      if (v is bool || v is num || v is String) {
        return v;
      }

      try {
        return write((v as dynamic).toJson());
      } on NoSuchMethodError {
        return v.toString();
      }
    }

    map.updateAll((key, value) {
      return write(value);
    });
    return map;
  }

  Map<String, Object>? buildParameters(
    Map<String, Object?>? parameters, {
    String? type,
  }) {
    final combined = <String, Object?>{
      'me_event_type': type ?? 'default',
      ..._defaultParameters,
      ...?parameters,
    };
    final formalized = formalizeParameters(combined);
    return formalized;
  }

  /// Log a [name] event with [parameters]. Return the event id.
  Future<String> logEvent(
    String name, {
    Map<String, Object?>? parameters,
    String? type,
  });
}
