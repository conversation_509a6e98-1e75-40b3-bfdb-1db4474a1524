// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:io';

import 'package:crypto/crypto.dart' as crypto;

extension MEFileExtension on File {
  Future<String> get sha256 async {
    final Stream<crypto.Digest> stream = crypto.sha256.bind(openRead());
    final crypto.Digest digest = await stream.first;
    final String hash = digest.toString();
    return hash;
  }
}
