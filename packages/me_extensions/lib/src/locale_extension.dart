// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'package:flutter/material.dart';

extension MELocaleExtension on Locale {
  static final Map<String, String> locale2lang = <String, String>{
    'en': 'English',
    'zh': '简体中文',
    'ja': '日本語',
  };

  String get languageName {
    return locale2lang[languageCode]!;
  }
}
