// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:convert' show jsonDecode;

import 'package:flutter/widgets.dart' show Characters;

import 'date_time_extension.dart';

extension MEStringExtension on String {
  String get notBreak => Characters(this).join('\u{200B}');

  /// 3r45..3g1e
  String get asIdentifier =>
      length > 10 ? replaceRange(4, length - 4, '..') : this;

  /// Ellipsis texts itself when using in spans such as [TextSpan].
  String ellipsis(int length, {String ellipsis = '\u2026'}) {
    if (this.length <= length) {
      return this;
    }
    return '${substring(0, length)}$ellipsis';
  }

  String append(String other) => '$this$other';

  String prepend(String other) => '$other$this';

  String removeAll(Pattern pattern) => replaceAll(pattern, '');

  String removeFirst(Pattern pattern, [int startIndex = 0]) =>
      replaceFirst(pattern, '', startIndex);

  dynamic deserialize() => jsonDecode(this);
}

extension MENullableStringExtension on String? {
  String get timeText => withDateTimeFormat();

  String withDateTimeFormat({
    String format = 'yyyy-MM-dd HH:mm:ss',
    bool toLocal = true,
  }) {
    if (this case final v?) {
      return DateTime.parse(v).format(format: format, toLocal: toLocal);
    }
    return '';
  }

  bool get isNullOrEmpty => this == null || this!.isEmpty;

  bool get isNotNullOrEmpty => this != null && this!.isNotEmpty;

  String or(String other) => isNullOrEmpty ? other : this!;

  String? orNull([String? other]) => isNullOrEmpty ? other : this;

  int? toIntOrNull() => this == null ? null : int.tryParse(this!);

  double? toDoubleOrNull() => this == null ? null : double.tryParse(this!);

  Uri? toUriOrNull() => Uri.tryParse(this ?? '::Not valid URI::');
}
