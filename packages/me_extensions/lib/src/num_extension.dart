// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:math' as math;

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:decimal/intl.dart';
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';
import 'package:rational/rational.dart';

export 'package:decimal/decimal.dart' hide BigIntExt, IntExt;
export 'package:rational/rational.dart' hide BigIntExt, IntExt;

final math.Random _random = math.Random();

const _numericalUnits = <(String, double)>[
  ('Qi', 1e18),
  ('Qa', 1e15),
  ('T', 1e12),
  ('B', 1e9),
  ('M', 1e6),
  ('K', 1e3),
];

extension MENumberStringExtension on String {
  int toInt({int? radix}) => int.parse(this, radix: radix);

  double toDouble() => double.parse(this);

  Decimal toDecimal() => Decimal.parse(this);

  BigInt toBigInt({int? radix}) => BigInt.parse(this, radix: radix);
}

extension MENumExtension<T extends num> on T {
  T get _zero => switch (this) { double() => 0.0, int() => 0 } as T;

  T get _one => switch (this) { double() => 1.0, int() => 1 } as T;

  T max(T value) => math.max<T>(this, value);

  T min(T value) => math.min<T>(this, value);

  T get lessThanZero => math.min<T>(_zero, this);

  T get lessThanOne => math.min<T>(_one, this);

  T get moreThanZero => math.max<T>(_zero, this);

  T get moreThanOne => math.max<T>(_one, this);

  T get betweenZeroAndOne => this.moreThanZero.lessThanOne;

  T coerceIn(T minimumValue, T maximumValue) {
    if (minimumValue > maximumValue) {
      throw ArgumentError.value(
        minimumValue,
        'minimumValue',
        'Must less than maximumValue',
      );
    }
    if (maximumValue.isNaN) {
      throw ArgumentError.value(
        maximumValue,
        'maximumValue',
        'Must not be NaN',
      );
    }
    return max(minimumValue).min(maximumValue);
  }

  T coerceAtLeast(T minimumValue) => max(minimumValue);

  T coerceAtMost(T maximumValue) => min(maximumValue);

  num shift(int value) => this * math.pow(10, value);

  /// [multiplier] is to keep the image quality.
  int toCache(
    BuildContext context, {
    double multiplier = 2,
  }) =>
      (this * MediaQuery.devicePixelRatioOf(context) * multiplier).ceil();

  String toNumerical({int fractionDigits = 2}) {
    if (this == 0) {
      return '0';
    }

    for (final (unit, value) in _numericalUnits) {
      if (this >= value) {
        final result = this / value;
        return '${result.roundAsFixed(fractionDigits)}$unit';
      }
    }
    return switch (this) {
      final double v => v.roundAsFixed(fractionDigits),
      final int v => v.toString(),
    };
  }

  BorderRadius get rCircular => BorderRadius.all(
        Radius.circular(this is double ? this as double : toDouble()),
      );

  BorderRadius get rVerticalTop => BorderRadius.vertical(
        top: Radius.circular(this is double ? this as double : toDouble()),
      );

  BorderRadius get rVerticalBottom => BorderRadius.vertical(
        bottom: Radius.circular(this is double ? this as double : toDouble()),
      );
}

extension MENumIterableExtension<T extends num> on Iterable<T> {
  T get max => sorted((a, b) => b.compareTo(a)).first;

  T get min => sorted((a, b) => a.compareTo(b)).first;
}

extension MEIntExtension on int {
  /// This [int] as a [Rational].
  Rational toRational() => Rational.fromInt(this);

  /// This [int] as a [Decimal].
  Decimal toDecimal() => Decimal.fromInt(this);

  List<int> get factors {
    if (this <= 0) {
      return [0];
    }
    return List.generate(this, (i) => i + 1)
        .where((e) => this % e == 0)
        .toList();
  }

  /// 通过时间戳返回 `9小时15分6秒` 格式的时间字符串
  String get durationString {
    final Duration duration = Duration(seconds: this);
    if (this >= 3600) {
      final Duration hour = Duration(hours: duration.inHours);
      final Duration minute = Duration(minutes: duration.inMinutes) - hour;
      final Duration second =
          Duration(seconds: duration.inSeconds) - hour - minute;
      return '${hour.inHours}小时${minute.inMinutes}分${second.inSeconds}秒';
    } else if (this >= 60 && this < 3600) {
      final Duration minute = Duration(minutes: duration.inMinutes);
      final Duration second = Duration(seconds: duration.inSeconds) - minute;
      return '${minute.inMinutes}分${second.inSeconds}秒';
    } else {
      return '$this秒';
    }
  }

  String get fileSizeFromBytes {
    const int kb = 1024;
    const int mb = 1024 * kb;
    const int gb = 1024 * mb;
    if (this >= gb) {
      return '${(this / gb).toStringAsFixed(2)} GB';
    }
    if (this >= mb) {
      return '${(this / mb).toStringAsFixed(2)} MB';
    }
    if (this >= kb) {
      return '${(this / kb).toStringAsFixed(2)} KB';
    }
    return '$this B';
  }

  BigInt decimalsToBigInt() {
    assert(this >= 0);
    return BigInt.parse('1'.padRight(this + 1, '0'));
  }

  Decimal decimalsToDecimal() {
    return Decimal.fromBigInt(decimalsToBigInt());
  }

  Decimal withDecimals(int decimals) {
    return Decimal.fromInt(this).withDecimals(decimals);
  }

  String currencyFormatWithDecimals({
    int decimals = 0,
    int zeroDecimals = 2,
    bool kSeparator = true,
  }) {
    assert(decimals >= 0);
    final decimal = withDecimals(decimals);
    final poundDecimals = decimals - 2;
    return decimal.currencyFormat(
      zeroDecimals: zeroDecimals,
      poundDecimals: poundDecimals > 0 ? poundDecimals : 0,
      kSeparator: kSeparator,
    );
  }

  String currencyFormat({
    String? pattern,
    int zeroDecimals = 0,
    int poundDecimals = 0,
    bool kSeparator = true,
  }) {
    return toDecimal().currencyFormat(
      pattern: pattern,
      zeroDecimals: zeroDecimals,
      poundDecimals: poundDecimals,
      kSeparator: kSeparator,
    );
  }

  int nextRandom([int min = 0]) => min + _random.nextInt(this - min);

  Duration get days => Duration(days: this);

  Duration get hours => Duration(hours: this);

  Duration get minutes => Duration(minutes: this);

  Duration get seconds => Duration(seconds: this);

  Duration get milliseconds => Duration(milliseconds: this);

  Duration get microseconds => Duration(microseconds: this);
}

extension MENullableIntExtension on int? {
  Decimal? toDecimal() => switch (this) {
        final v? => Decimal.fromInt(v),
        _ => null,
      };
}

extension MEDoubleExtension on double {
  double nextRandom([int min = 0]) => min + _random.nextDouble() * (this - min);

  String roundAsFixed(int fractionDigits) {
    final num pow = math.pow(10, fractionDigits);
    return ((this * pow).round() / pow).toString();
  }
}

extension MEDecimalExtension on Decimal {
  Decimal max(Decimal other) {
    return this > other ? this : other;
  }

  Decimal min(Decimal other) {
    return this < other ? this : other;
  }

  Decimal get lessThanZero => min(Decimal.zero);

  Decimal get lessThanOne => min(Decimal.one);

  Decimal get moreThanZero => min(Decimal.zero);

  Decimal get moreThanOne => max(Decimal.one);

  Decimal get betweenZeroAndOne => lessThanOne.moreThanZero;

  /// 根据位数四舍五入
  String roundAsFixed(int size) {
    final Decimal pow = Decimal.fromInt(math.pow(10, size).toInt());
    return ((this * pow).round() / pow).toDecimal().toString();
  }

  String toNumerical({int fractionDigits = 2}) {
    if (this == Decimal.zero) {
      return '0';
    }

    for (final (unit, value) in _numericalUnits) {
      final v = Decimal.parse('$value');
      if (this >= v) {
        final result = (this / v).toDecimal();
        return '${result.roundAsFixed(fractionDigits)}$unit';
      }
    }
    return roundAsFixed(fractionDigits);
  }

  /// Separate numbers with kilo group separator (,)
  /// and fixed to the specific decimals string.
  ///
  /// See `test/decimals_test.dart` for more detailed cases.
  String toKiloSplitString({int decimals = 2}) {
    assert(decimals >= 0);
    final string = toString();
    final split = string.split('.');
    final beforeDecimal = NumberFormat.decimalPattern('en').format(
      int.parse(split.first),
    );
    final String? afterDecimal;
    if (split.length > 1) {
      String last = split.last;
      last = last.substring(0, math.min(last.length, decimals));
      if (last.length < decimals) {
        last = last.padRight(decimals, '0');
      }
      if (last.isEmpty) {
        afterDecimal = null;
      } else {
        afterDecimal = last;
      }
    } else if (decimals > 0) {
      afterDecimal = '0' * decimals;
    } else {
      afterDecimal = null;
    }
    final sb = StringBuffer(beforeDecimal);
    if (afterDecimal != null) {
      sb.write('.$afterDecimal');
    }
    return '$sb';
  }

  Decimal withDecimals(int decimals) {
    return (this / Decimal.fromBigInt(decimals.decimalsToBigInt()))
        .toDecimal(scaleOnInfinitePrecision: decimals);
  }

  String currencyFormatWithDecimals({
    int decimals = 0,
    int zeroDecimals = 2,
    bool kSeparator = true,
  }) {
    assert(decimals >= 0);
    final decimal = withDecimals(decimals);
    final poundDecimals = decimals - 2;
    return decimal.currencyFormat(
      zeroDecimals: zeroDecimals,
      poundDecimals: poundDecimals > 0 ? poundDecimals : 0,
      kSeparator: kSeparator,
    );
  }

  String currencyFormat({
    String? pattern,
    int zeroDecimals = 0,
    int poundDecimals = 0,
    bool kSeparator = true,
  }) {
    if (pattern != null) {
      return DecimalFormatter(NumberFormat(pattern, 'en_US')).format(this);
    }
    assert(zeroDecimals >= 0 && poundDecimals >= 0);
    final String p;
    if (zeroDecimals > 0 || poundDecimals > 0) {
      p = kSeparator
          ? '#,##0.'
              .padRight(6 + zeroDecimals, '0')
              .padRight(6 + zeroDecimals + poundDecimals, '#')
          : '0.'
              .padRight(2 + zeroDecimals, '0')
              .padRight(2 + zeroDecimals + poundDecimals, '#');
    } else {
      p = kSeparator ? '#,##0' : '0';
    }
    return DecimalFormatter(NumberFormat(p, 'en_US')).format(this);
  }
}

extension MEBigIntExtension on BigInt {
  /// This [BigInt] as a [Rational].
  Rational toRational() => Rational(this);

  /// This [BigInt] as a [Decimal].
  Decimal toDecimal() => Decimal.fromBigInt(this);

  BigInt max(BigInt other) {
    return this > other ? this : other;
  }

  BigInt min(BigInt other) {
    return this < other ? this : other;
  }

  BigInt shift(int value) => this * BigInt.from(10).pow(value);

  String roundAsFixed(int fractionDigits) {
    final BigInt pow = BigInt.from(10).pow(fractionDigits);
    return ((this * pow) / pow).toString();
  }

  String toStringAsFixed(BigInt divisor) {
    final BigInt quotient = this ~/ divisor;
    final BigInt remainder = this % divisor;
    if (quotient >= BigInt.from(10)) {
      return quotient.toString();
    }
    final BigInt fraction = (remainder * BigInt.from(100)) ~/ divisor;
    final String fractionStr = fraction.toString().padLeft(2, '0');
    return '$quotient.$fractionStr';
  }

  String toNumerical({int fractionDigits = 2}) {
    if (this == BigInt.zero) {
      return '0';
    }
    for (final (unit, value) in _numericalUnits) {
      final v = BigInt.from(value);
      if (this >= v) {
        final result = this / v;
        return '${result.roundAsFixed(fractionDigits)}$unit';
      }
    }
    return roundAsFixed(fractionDigits);
  }

  String currencyFormatWithDecimals({
    int decimals = 0,
    int zeroDecimals = 2,
    int? poundDecimals,
    bool kSeparator = true,
  }) {
    assert(decimals >= 0);
    final decimal = withDecimals(decimals);
    poundDecimals ??= decimals - 2;
    return decimal.currencyFormat(
      zeroDecimals: zeroDecimals,
      poundDecimals: poundDecimals > 0 ? poundDecimals : 0,
      kSeparator: kSeparator,
    );
  }

  Decimal withDecimals(int decimals) {
    return (Decimal.fromBigInt(this) /
            Decimal.fromBigInt(decimals.decimalsToBigInt()))
        .toDecimal(scaleOnInfinitePrecision: decimals);
  }

  String currencyFormat({
    String? pattern,
    int zeroDecimals = 0,
    int poundDecimals = 0,
    bool kSeparator = true,
  }) {
    return toDecimal().currencyFormat(
      pattern: pattern,
      zeroDecimals: zeroDecimals,
      poundDecimals: poundDecimals,
      kSeparator: kSeparator,
    );
  }
}

extension MENullableBigIntExtension on BigInt? {
  Decimal? toDecimal() => switch (this) {
        final v? => Decimal.fromBigInt(v),
        _ => null,
      };
}
