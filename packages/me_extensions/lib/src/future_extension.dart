// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:async';
import 'dart:collection';

import 'package:flutter/material.dart';

extension MECompleterExtension<T> on Completer<T> {
  void maybeComplete([FutureOr<T>? value]) {
    if (!isCompleted) {
      complete(value);
    }
  }

  void maybeCompleteError(Object error, [StackTrace? stackTrace]) {
    if (!isCompleted) {
      completeError(error, stackTrace);
    }
  }
}

extension MEFutureExtension<T> on Future<T> {
  Future<T> atLeast(Duration duration, {bool eagerError = false}) async {
    final List<dynamic> futures = await Future.wait<dynamic>(
      <Future<dynamic>>[this, Future<void>.delayed(duration)],
      eagerError: eagerError,
    );
    return futures.first as T;
  }

  Future<List<T>> plus(Future<T> b, {bool eagerError = false}) {
    return Future.wait(<Future<T>>[this, b], eagerError: eagerError);
  }

  Future<T> delay(Duration duration) async {
    final result = await this;
    await Future.delayed(duration);
    return result;
  }
}

extension MEFutureOrExtension<T> on Iterable<FutureOr<T>> {
  Future<List<T>> waitOr({
    bool eagerError = false,
    void Function(T successValue)? cleanUp,
  }) {
    return Future.wait(
      map((e) => e is Future<T> ? e : Future.value(e)),
      eagerError: eagerError,
      cleanUp: cleanUp,
    );
  }
}

extension MEIterableFutureExtension<T> on Iterable<Future<T>> {
  Future<List<T>> wait({
    bool eagerError = false,
    void Function(T successValue)? cleanUp,
  }) {
    return Future.wait(this, eagerError: eagerError, cleanUp: cleanUp);
  }

  Future<List<AsyncSnapshot<T>>> allSettled({
    int parallel = 0,
    void Function(int index, T value)? onSuccess,
    void Function(int index, Object error, StackTrace stackTrace)? onError,
    void Function(int index, AsyncSnapshot<T> snapshot)? onComplete,
  }) {
    final len = length;
    if (len == 0) {
      return Future.value(<AsyncSnapshot<T>>[]);
    }
    final completer = Completer<List<AsyncSnapshot<T>>>();
    int remaining = len;
    final list = List<AsyncSnapshot<T>?>.filled(len, null);
    if (parallel <= 0) {
      void handleFuture(int index, Future<T> future) {
        future.then(
          (value) {
            final snapshot =
                AsyncSnapshot.withData(ConnectionState.done, value);
            list[index] = snapshot;
            onSuccess?.call(index, value);
            onComplete?.call(index, snapshot);
          },
          onError: (e, s) {
            final snapshot =
                AsyncSnapshot<T>.withError(ConnectionState.done, e, s);
            list[index] = snapshot;
            onError?.call(index, e, s);
            onComplete?.call(index, snapshot);
          },
        ).whenComplete(() {
          if (--remaining == 0) {
            completer.complete(List.unmodifiable(list.cast()));
          }
        });
      }

      for (int i = 0; i < len; i++) {
        handleFuture(i, elementAt(i));
      }
    } else {
      int running = 0;
      int index = 0;
      final queue = Queue<Future<T>>.from(this);
      void exec() {
        void handleFuture(int index, Future<T> future) {
          future.then(
            (value) {
              final snapshot =
                  AsyncSnapshot.withData(ConnectionState.done, value);
              list[index] = snapshot;
              onSuccess?.call(index, value);
              onComplete?.call(index, snapshot);
            },
            onError: (e, s) {
              final snapshot =
                  AsyncSnapshot<T>.withError(ConnectionState.done, e, s);
              list[index] = snapshot;
              onError?.call(index, e, s);
              onComplete?.call(index, snapshot);
            },
          ).whenComplete(() {
            if (--remaining == 0) {
              completer.complete(List.unmodifiable(list.cast()));
            }
            running--;
            exec();
          });
        }

        while (queue.isNotEmpty && running < parallel) {
          running++;
          final future = queue.removeFirst();
          handleFuture(index++, future);
        }
      }

      exec();
    }
    return completer.future;
  }
}

extension MEIterableAsyncSnapshotExtension<T> on Iterable<AsyncSnapshot<T>> {
  bool get hasError => any((e) => e.hasError);
}
