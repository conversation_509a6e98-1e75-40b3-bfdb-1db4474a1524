// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:convert' show Utf8Codec, jsonEncode;
import 'dart:math' as math;
import 'dart:typed_data';

import 'package:collection/collection.dart';
import 'package:convert/convert.dart';

extension MEIterableExtension<E> on Iterable<E> {
  bool atLeastMatched(int expect, bool Function(E element) test) {
    assert(expect > 0);
    for (final E e in this) {
      if (test(e) && --expect <= 0) {
        return true;
      }
    }
    return false;
  }

  /// Checks whether no element of this iterable satisfies [test].
  ///
  /// Checks every element in iteration order, and returns `true` if
  /// none of them make [test] return `true`, otherwise returns false.
  ///
  /// Example:
  /// ```dart
  /// final numbers = <int>[1, 2, 3, 5, 6, 7];
  /// var result = numbers.no((element) => element >= 5); // false;
  /// result = numbers.no((element) => element >= 10); // true;
  /// ```
  bool no(bool Function(E element) test) {
    for (final E element in this) {
      if (test(element)) {
        return false;
      }
    }
    return true;
  }
}

extension MENullableIterableExtension<T> on Iterable<T>? {
  bool get isNullOrEmpty => this == null || this!.isEmpty;

  bool get isNotNullOrEmpty => this != null && this!.isNotEmpty;
}

extension MEListExtension<T> on List<T> {
  T? getOrNull(int index) {
    if (index >= 0 && length > index) {
      return this[index];
    }
    return null;
  }

  T getOrElse(int index, T value) {
    if (index >= 0 && length > index) {
      return this[index];
    }
    return value;
  }

  T get random => this[math.Random().nextInt(length)];
}

extension MEMapJsonExtension on Map {
  String serialize() => jsonEncode(this);
}

extension MEUint8ListExtension on Uint8List {
  bool equals(Uint8List other) => const ListEquality().equals(this, other);
}

extension MEUtf8CodecExtension on Utf8Codec {
  Uint8List encodeAsU8a(String input) => Uint8List.fromList(encode(input));
}

extension MEHexCodecExtension on HexCodec {
  Uint8List decodeAsU8a(String input) => Uint8List.fromList(decode(input));
}
