// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'package:intl/intl.dart';

extension MEDateTimeExtension on DateTime {
  int get secondsSinceEpoch => millisecondsSinceEpoch ~/ 1000;

  DateTime get startOfTheDay => DateTime(year, month, day);

  DateTime get endOfTheDay => DateTime(year, month, day, 23, 59, 59);

  bool isSameDateAs(DateTime other) {
    return year == other.year && month == other.month && day == other.day;
  }
}

extension MENullableDateTimeExtension on DateTime? {
  String get timeText => format();

  String format({
    String format = 'yyyy-MM-dd HH:mm:ss',
    bool toLocal = true,
  }) {
    if (this case final v?) {
      return DateFormat(format).format(toLocal ? v.toLocal() : v);
    }
    return '';
  }
}
