// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

extension MEBaseMapExtension on Map<dynamic, dynamic> {
  void removeAllEmptyEntry() {
    removeWhere((dynamic k, dynamic v) => k == null || v == null || v == '');
  }

  String toQuery({bool needQuestionMark = false}) {
    if (isEmpty) {
      return '';
    }
    String result = '';
    if (needQuestionMark) {
      result += '?';
    }
    for (final MapEntry<dynamic, dynamic> entry in entries) {
      result += '&${entry.key}=${Uri.encodeFull(entry.value.toString())}';
    }
    return result.replaceFirst('&', '');
  }

  Map<String, String> toQueries() {
    return <String, String>{
      for (final MapEntry<dynamic, dynamic> entry in entries)
        entry.key.toString(): entry.value.toString(),
    };
  }

  Map<String, dynamic> toJsonMap() {
    for (final dynamic k in keys) {
      final dynamic v = this[k];
      if (v is Map) {
        this[k] = v.toJsonMap();
        continue;
      }
      if (v is List) {
        this[k] = v.map<dynamic>((dynamic m) {
          if (m is Map) {
            return m.toJsonMap();
          }
          return m;
        }).toList();
        continue;
      }
    }
    return cast<String, dynamic>();
  }
}
