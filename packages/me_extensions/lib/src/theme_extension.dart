// Copyright 2024 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.
import 'package:flutter/material.dart';

extension METhemeDataExtension on ThemeData {
  bool get platformAndroid => platform == TargetPlatform.android;

  bool get platformIOS => platform == TargetPlatform.iOS;

  bool get platformDarwin => switch (platform) {
        TargetPlatform.iOS || TargetPlatform.macOS => true,
        _ => false,
      };
}
