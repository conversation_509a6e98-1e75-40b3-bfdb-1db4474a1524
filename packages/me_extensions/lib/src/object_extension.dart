// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

extension MEObjectExtension<T extends Object> on T {
  void let(void Function(T) cb) => cb(this);

  T apply(T Function(T) cb) => cb(this);

  R run<R>(R Function(T) cb) => cb(this);
}

extension MENullableObjectExtension on Object? {
  Map<String, dynamic> asJson() => (this as Map).cast<String, dynamic>();
}

extension MEStackTraceExtension on StackTrace {
  StackTrace operator +(StackTrace other) {
    return StackTrace.fromString('$this\n$other');
  }
}

extension MEValueListenableExtension<T> on ValueListenable<T> {
  ValueListenableBuilder<T> vlb(
    ValueWidgetBuilder<T> builder, {
    Widget? child,
  }) {
    return ValueListenableBuilder<T>(
      valueListenable: this,
      builder: builder,
      child: child,
    );
  }
}
