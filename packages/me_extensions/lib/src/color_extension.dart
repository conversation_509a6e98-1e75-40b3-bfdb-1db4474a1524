// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'package:flutter/material.dart';

extension MEColorExtension on Color {
  static int _floatToInt8(double x) {
    return (x * 255.0).round() & 0xff;
  }

  ColorFilter get filter => ColorFilter.mode(this, BlendMode.srcIn);

  bool get isTransparent => a == 0.0;

  MaterialColor get swatch {
    return Colors.primaries.firstWhere(
      (Color c) => c == this,
      orElse: () => _swatch,
    );
  }

  Map<int, Color> get getMaterialColorValues {
    return <int, Color>{
      50: _swatchShade(50),
      100: _swatchShade(100),
      200: _swatchShade(200),
      300: _swatchShade(300),
      400: _swatchShade(400),
      500: _swatchShade(500),
      600: _swatchShade(600),
      700: _swatchShade(700),
      800: _swatchShade(800),
      900: _swatchShade(900),
    };
  }

  MaterialColor get _swatch => MaterialColor(
        _floatToInt8(a) << 24 |
            _floatToInt8(r) << 16 |
            _floatToInt8(g) << 8 |
            _floatToInt8(b) << 0,
        getMaterialColorValues,
      );

  Color _swatchShade(int swatchValue) => HSLColor.fromColor(this)
      .withLightness(1 - (swatchValue / 1000))
      .toColor();

  AlwaysStoppedAnimation<Color> get alwaysStopped =>
      AlwaysStoppedAnimation<Color>(this);
}

/// https://stackoverflow.com/a/50081214/10064463
extension MEHexColorExtension on Color {
  /// String is in the format "aabbcc" or "ffaabbcc" with an optional leading "#".
  static Color fromHex(String hexString) {
    final StringBuffer buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) {
      buffer.write('ff');
    }
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  /// Prefixes a hash sign if [leadingHashSign] is set to `true`.
  String toHex({bool leadingHashSign = true}) {
    return '${leadingHashSign ? '#' : ''}'
        '${(a * 256).round().toRadixString(16).padLeft(2, '0')}'
        '${(r * 256).round().toRadixString(16).padLeft(2, '0')}'
        '${(g * 256).round().toRadixString(16).padLeft(2, '0')}'
        '${(b * 256).round().toRadixString(16).padLeft(2, '0')}';
  }
}
