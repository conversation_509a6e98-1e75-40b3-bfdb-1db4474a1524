// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'dart:async';

import 'package:flutter/widgets.dart';

extension MESafeSetStateExtension on State {
  bool get _mounted =>
      mounted &&
      !context.debugDoingBuild &&
      context.owner?.debugBuilding == false;

  /// [setState] when it's not building, then wait until next frame built.
  FutureOr<void> safeSetState(FutureOr<dynamic> Function() fn) async {
    if (!_mounted) {
      return;
    }
    await fn();
    if (_mounted) {
      // ignore: invalid_use_of_protected_member
      setState(() {});
    }
    final completer = Completer<void>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      completer.complete();
    });
    return completer.future;
  }
}
