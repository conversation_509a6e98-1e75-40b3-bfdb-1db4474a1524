// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

ui.FlutterView get topFlutterView => ui.PlatformDispatcher.instance.views.first;

class Screens {
  const Screens._();

  static MediaQueryData get mediaQuery =>
      MediaQueryData.fromView(topFlutterView);

  static double get dpr => mediaQuery.devicePixelRatio;

  static double get width => mediaQuery.size.width;

  static int get widthPixels => (width * dpr).toInt();

  static double get height => mediaQuery.size.height;

  static int get heightPixels => (height * dpr).toInt();

  static double get aspectRatio => width / height;

  static double get navigationBarHeight =>
      mediaQuery.padding.top + kToolbarHeight;

  static double get topSafeHeight => mediaQuery.padding.top;

  static double get bottomSafeHeight => mediaQuery.padding.bottom;

  static double get safeHeight => height - topSafeHeight - bottomSafeHeight;

  static double get keyboardHeight => mediaQuery.viewInsets.bottom;

  static void updateStatusBarStyle(SystemUiOverlayStyle style) {
    SystemChrome.setSystemUIOverlayStyle(style);
  }
}
