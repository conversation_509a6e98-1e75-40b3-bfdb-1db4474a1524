// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.
import 'dart:convert';

const JsonEncoder globalJsonEncoder = JsonEncoder.withIndent('  ');
const JsonEncoder globalJsonEncoderWithoutIntent = JsonEncoder();

const Duration routeDuration = Duration(milliseconds: 300);
const String hiddenBalance = '***';

/// [RegExp]s.
final RegExp accountIdentifierRegExp = RegExp(r'^[\da-zA-Z]+$');
final RegExp deviceNameRegExp = RegExp(r'[\da-zA-Z. ]');
final RegExp phrasesRegExp = RegExp(r'[\da-zA-Z ]');
final RegExp usernameRegExp = RegExp(r'[a-z0-9_]');
final RegExp walletRegExp = RegExp(r'[\da-zA-Z_ ]');

RegExp tokenAmountRegExp(int decimals) {
  if (decimals <= 0) {
    return RegExp(r'^\d+$');
  }
  return RegExp('^(0|[1-9]\\d*)?(\\.\\d{0,$decimals})?\$');
}
