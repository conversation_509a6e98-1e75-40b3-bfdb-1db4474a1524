// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.
import 'package:flutter/material.dart';

const double kDialogMaxWidth = 320;

const double textSize10 = 10;
const double textSize12 = 12;
const double textSize13 = 13;
const double textSize14 = 14;
const double textSize16 = 16;
const double textSize18 = 18;
const double textSize20 = 20;
const double textSize24 = 24;
const double textSize28 = 28;
const double textSize30 = 30;
const double textSize36 = 36;
const double textSize42 = 42;
const double textSize48 = 48;

class RadiusConstants {
  const RadiusConstants._();

  static const BorderRadius r1 = BorderRadius.all(Radius.circular(1));
  static const BorderRadius r2 = BorderRadius.all(Radius.circular(2));
  static const BorderRadius r3 = BorderRadius.all(Radius.circular(3));
  static const BorderRadius r4 = BorderRadius.all(Radius.circular(4));
  static const BorderRadius r5 = BorderRadius.all(Radius.circular(5));
  static const BorderRadius r6 = BorderRadius.all(Radius.circular(6));
  static const BorderRadius r7 = BorderRadius.all(Radius.circular(7));
  static const BorderRadius r8 = BorderRadius.all(Radius.circular(8));
  static const BorderRadius r9 = BorderRadius.all(Radius.circular(9));
  static const BorderRadius r10 = BorderRadius.all(Radius.circular(10));
  static const BorderRadius r12 = BorderRadius.all(Radius.circular(12));
  static const BorderRadius r15 = BorderRadius.all(Radius.circular(15));
  static const BorderRadius r16 = BorderRadius.all(Radius.circular(16));
  static const BorderRadius r18 = BorderRadius.all(Radius.circular(18));
  static const BorderRadius r20 = BorderRadius.all(Radius.circular(20));
  static const BorderRadius r25 = BorderRadius.all(Radius.circular(25));
  static const BorderRadius max = BorderRadius.all(Radius.circular(999999));
}

const scaffoldTitleTextPadding = EdgeInsets.symmetric(vertical: 24.0);
const scaffoldBodyPadding = EdgeInsetsDirectional.only(
  start: 24.0,
  end: 24.0,
  top: 30.0,
);

List<Widget> paddingWidgets(
  Iterable<Widget> widgets, {
  EdgeInsetsGeometry padding = const EdgeInsets.symmetric(horizontal: 24),
}) {
  return widgets
      .map((Widget w) => Padding(padding: padding, child: w))
      .toList();
}
