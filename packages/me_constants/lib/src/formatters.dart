// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

import 'constants.dart';

/// Empty counter builder for [TextField].
Widget? emptyCounterBuilder(
  BuildContext _, {
  required int currentLength,
  int? maxLength,
  required bool isFocused,
}) =>
    null;

final TextInputFormatter deviceNameFormatter =
    FilteringTextInputFormatter.allow(deviceNameRegExp);

final TextInputFormatter phrasesFormatter =
    FilteringTextInputFormatter.allow(phrasesRegExp);

/// Allow CAPITAL case inputs and convert to lower case eventually.
final TextInputFormatter usernameFormatter =
    LowerCaseFilteringTextInputFormatter.allow(usernameRegExp);

final TextInputFormatter walletFormatter =
    FilteringTextInputFormatter.allow(walletRegExp);

class LowerCaseFilteringTextInputFormatter extends FilteringTextInputFormatter {
  LowerCaseFilteringTextInputFormatter.allow(
    super.filterPattern, {
    super.replacementString,
  }) : super.allow();

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue, // unused
    TextEditingValue newValue,
  ) {
    final TextEditingValue value = super.formatEditUpdate(oldValue, newValue);
    // Force the text to be lower cased, keep other fields.
    return value.copyWith(text: value.text.toLowerCase());
  }
}

/// A formatter that takes a [RegExp] to match the value.
///
/// Currently the formatter only support match the first group.
class RegExpFormatter extends TextInputFormatter {
  RegExpFormatter(this.regExp);

  final RegExp regExp;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty ||
        !newValue.composing.isCollapsed ||
        !newValue.selection.isCollapsed) {
      return newValue;
    }
    final Iterable<RegExpMatch> matches = regExp.allMatches(newValue.text);
    if (matches.length == 1 &&
        matches.first.group(0).toString() == newValue.text) {
      return newValue;
    }
    return oldValue;
  }
}

/// [TextInputFormatter] for monetary amounts with configurable decimal places.
class AmountInputFormatter extends TextInputFormatter {
  /// Creates an [AmountInputFormatter].
  ///
  /// The [decimalDigits] argument specifies the number of digits allowed
  /// after the decimal point. It is required.
  /// If 0, no decimal point is allowed.
  const AmountInputFormatter({
    required this.decimalDigits,
  }) : assert(decimalDigits >= 0); // decimalDigits must be non-negative

  final int decimalDigits;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // 1. Handle character replacement: Replace Chinese period and
    //    interpunct with English period
    String processedText =
        newValue.text.replaceAll('。', '.').replaceAll('·', '.');
    TextSelection newSelection = newValue.selection;

    // If the text hasn't changed visually after replacement, and it was
    // already valid, return newValue. Minor optimization.
    if (processedText == oldValue.text && newValue.text == oldValue.text) {
      if (newValue.text == oldValue.text) {
        return newValue;
      }
    }

    // 2. Handle empty input
    if (processedText.isEmpty) {
      return const TextEditingValue(
        text: '',
        selection: TextSelection.collapsed(offset: 0),
      );
    }

    // 3. Handle the specific case of just typing "."
    if (processedText == '.') {
      if (decimalDigits > 0) {
        // If decimalDigits > 0, allow typing just "."
        return const TextEditingValue(
          text: '.', // Keep as "."
          selection: TextSelection.collapsed(offset: 1), // Cursor after "."
        );
      }
      // If decimalDigits is 0, reject "."
      return oldValue;
    }

    // 4. Basic validation: Check for invalid characters and multiple decimals
    // This regex allows strings consisting of digits and at most one dot.
    // It matches integers, decimals, pure decimals (.xxx), and integers
    // with a trailing dot (xxx.).
    // MODIFIED: Corrected regex pattern for decimalDigits > 0
    final String basicPattern = decimalDigits > 0 ? r'^\d*\.?\d*$' : r'^\d*$';
    final RegExp basicRegex = RegExp(basicPattern);

    if (!basicRegex.hasMatch(processedText)) {
      // If it contains invalid characters or multiple dots
      return oldValue;
    }

    // 5. Handle leading zeros (only check if it doesn't start with a dot)
    if (!processedText.startsWith('.') &&
        processedText.startsWith('0') &&
        processedText.length > 1 &&
        processedText[1] != '.') {
      // Invalid leading zero, e.g., "01", "005"
      return oldValue;
    }

    // 6. Handle decimal places constraint (if decimalDigits > 0)
    if (decimalDigits > 0) {
      final parts = processedText.split('.');
      if (parts.length == 2) {
        // Contains a decimal point
        final decimalPart = parts[1];
        if (decimalPart.length > decimalDigits) {
          // Too many decimal places. Trim extras.
          final limitedRegex = RegExp('^\\d*\\.?\\d{0,$decimalDigits}');
          processedText = limitedRegex.firstMatch(processedText)!.group(0)!;
        }
      }
      // Note: parts.length > 2 is already caught by basicRegex
    }
    // Corrects selections offset if overflowed.
    if (newSelection.baseOffset > processedText.length) {
      newSelection = newSelection.copyWith(baseOffset: processedText.length);
    }
    if (newSelection.extentOffset > processedText.length) {
      newSelection = newSelection.copyWith(extentOffset: processedText.length);
    }

    // If all checks pass, the input is valid.
    // Return the processed text and the original new selection.
    return TextEditingValue(text: processedText, selection: newSelection);
  }
}
