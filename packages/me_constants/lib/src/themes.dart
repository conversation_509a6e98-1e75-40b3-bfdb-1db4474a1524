// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.
import 'dart:ui' as ui show lerpDouble;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'screens.dart';
import 'styles.dart';

const Color transparentBlack = Color(0x00000000);
const Color transparentWhite = Color(0x00ffffff);

const Color failedColor = Color(0xffff6363);
const Color succeedColor = Color(0xFF5dd95d);
const Color dotColor = Color(0xffcaeb35);
const BorderRadius defaultBorderRadius = BorderRadius.all(
  Radius.circular(10.0),
);
const String defaultLatinFonts = 'PT Sans';

class METheme extends ThemeExtension<METheme> {
  const METheme({
    required this.brightness,
    required this.themeColor,
    required this.backgroundColor,
    required this.cardColor,
    required this.dividerColor,
    required this.iconColor,
    required this.listColor,
    required this.primaryTextColor,
    required this.captionTextColor,
    required this.blueGreyIconColor,
    required this.shimmerHighlightColor,
    this.failingColor = failedColor,
    this.successColor = succeedColor,
    this.notificationColor = dotColor,
    this.borderRadius = defaultBorderRadius,
    this.fontFamilyLatin = defaultLatinFonts,
    this.fontFamilyExtraFallbacks = const [],
    this.fontHeightLatin = 1,
    this.fontHeightCJK = 1.24,
  });

  final Brightness brightness;
  final Color themeColor;
  final Color backgroundColor;
  final Color cardColor;
  final Color dividerColor;
  final Color iconColor;
  final Color listColor;
  final Color primaryTextColor;
  final Color captionTextColor;
  final Color blueGreyIconColor;
  final Color shimmerHighlightColor;

  /// Belows are defined without following corresponding brightness.
  final Color failingColor;
  final Color successColor;
  final Color notificationColor;

  final BorderRadius borderRadius;

  final String fontFamilyLatin;
  final List<String> fontFamilyExtraFallbacks;
  final double fontHeightLatin;
  final double fontHeightCJK;

  @override
  ThemeExtension<METheme> copyWith({
    Brightness? brightness,
    Color? backgroundColor,
    Color? themeColor,
    Color? cardColor,
    Color? dividerColor,
    Color? iconColor,
    Color? listColor,
    Color? primaryTextColor,
    Color? captionTextColor,
    Color? blueGreyIconColor,
    Color? shimmerHighlightColor,
    Color? failingColor,
    Color? successColor,
    Color? notificationColor,
    BorderRadius? borderRadius,
    String? fontFamilyLatin,
    List<String>? fontFamilyExtraFallbacks,
    double? fontHeightLatin,
    double? fontHeightCJK,
  }) {
    return METheme(
      brightness: brightness ?? this.brightness,
      themeColor: themeColor ?? this.themeColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      cardColor: cardColor ?? this.cardColor,
      dividerColor: dividerColor ?? this.dividerColor,
      iconColor: iconColor ?? this.iconColor,
      listColor: listColor ?? this.listColor,
      primaryTextColor: primaryTextColor ?? this.primaryTextColor,
      captionTextColor: captionTextColor ?? this.captionTextColor,
      blueGreyIconColor: blueGreyIconColor ?? this.blueGreyIconColor,
      shimmerHighlightColor:
          shimmerHighlightColor ?? this.shimmerHighlightColor,
      failingColor: failingColor ?? this.failingColor,
      successColor: successColor ?? this.successColor,
      notificationColor: notificationColor ?? this.notificationColor,
      borderRadius: borderRadius ?? this.borderRadius,
      fontFamilyLatin: fontFamilyLatin ?? this.fontFamilyLatin,
      fontFamilyExtraFallbacks:
          fontFamilyExtraFallbacks ?? this.fontFamilyExtraFallbacks,
      fontHeightLatin: fontHeightLatin ?? this.fontHeightLatin,
      fontHeightCJK: fontHeightCJK ?? this.fontHeightCJK,
    );
  }

  Color _lerp(Color a, Color b, double t) => Color.lerp(a, b, t)!;

  @override
  ThemeExtension<METheme> lerp(ThemeExtension<METheme>? other, double t) {
    if (other is! METheme) {
      return this;
    }
    return METheme(
      brightness: brightness == other.brightness
          ? brightness
          : t > 0.5
              ? other.brightness
              : brightness,
      themeColor: _lerp(themeColor, other.themeColor, t),
      backgroundColor: _lerp(backgroundColor, other.backgroundColor, t),
      cardColor: _lerp(cardColor, other.cardColor, t),
      dividerColor: _lerp(dividerColor, other.dividerColor, t),
      iconColor: _lerp(iconColor, other.iconColor, t),
      listColor: _lerp(listColor, other.listColor, t),
      primaryTextColor: _lerp(primaryTextColor, other.primaryTextColor, t),
      captionTextColor: _lerp(captionTextColor, other.captionTextColor, t),
      blueGreyIconColor: _lerp(blueGreyIconColor, other.blueGreyIconColor, t),
      shimmerHighlightColor:
          _lerp(shimmerHighlightColor, other.shimmerHighlightColor, t),
      failingColor: _lerp(failingColor, other.failingColor, t),
      successColor: _lerp(successColor, other.successColor, t),
      notificationColor: _lerp(notificationColor, other.notificationColor, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t)!,
      fontFamilyLatin: t > 0.5 ? other.fontFamilyLatin : fontFamilyLatin,
      fontFamilyExtraFallbacks:
          t > 0.5 ? other.fontFamilyExtraFallbacks : fontFamilyExtraFallbacks,
      fontHeightLatin:
          ui.lerpDouble(fontHeightLatin, other.fontHeightLatin, t)!,
      fontHeightCJK: ui.lerpDouble(fontHeightCJK, other.fontHeightCJK, t)!,
    );
  }
}

extension METhemeBuildContextExtension on BuildContext {
  METheme get meTheme => Theme.of(this).extension<METheme>()!;

  Color get themeColor => meTheme.themeColor;

  ColorFilter get themeColorFilter =>
      ColorFilter.mode(themeColor, BlendMode.srcIn);

  METheme? get meThemeOrNull => Theme.of(this).extension<METheme>();

  Color? get themeColorOrNull => meThemeOrNull?.themeColor;
}

ThemeData themeBy({
  required METheme meTheme,
  required Locale locale,
  TextTheme? textTheme,
  InputBorder? inputBorder,
}) {
  final defaultInputBorder =
      inputBorder ?? OutlineInputBorder(borderRadius: meTheme.borderRadius);
  final effectiveTextTheme =
      textTheme ?? textThemeBy(meTheme: meTheme, locale: locale);
  final ThemeData theme = ThemeData.from(
    colorScheme: ColorScheme.fromSeed(
      seedColor: meTheme.themeColor,
      brightness: meTheme.brightness,
      primary: meTheme.themeColor,
      primaryContainer: meTheme.cardColor,
      secondary: meTheme.backgroundColor,
      surface: meTheme.backgroundColor,
    ),
    textTheme: effectiveTextTheme,
    useMaterial3: false,
  ).copyWith(
    extensions: <ThemeExtension<dynamic>>[meTheme],
    primaryColor: meTheme.themeColor,
    cardColor: meTheme.cardColor,
    canvasColor: meTheme.listColor,
    dividerColor: meTheme.dividerColor,
    appBarTheme: AppBarTheme(
      color: meTheme.cardColor,
      elevation: 0.5,
      foregroundColor: meTheme.primaryTextColor,
    ),
    iconTheme: IconThemeData(color: meTheme.iconColor),
    inputDecorationTheme: InputDecorationTheme(
      contentPadding: const EdgeInsets.symmetric(horizontal: 10),
      filled: true,
      fillColor: meTheme.cardColor,
      border: defaultInputBorder,
      disabledBorder: defaultInputBorder,
      enabledBorder: defaultInputBorder.copyWith(
        borderSide: BorderSide(color: meTheme.dividerColor),
      ),
      focusedBorder: defaultInputBorder.copyWith(
        borderSide: BorderSide(color: meTheme.themeColor),
      ),
      focusedErrorBorder: defaultInputBorder.copyWith(
        borderSide: BorderSide(color: meTheme.failingColor),
      ),
      hintStyle: TextStyle(color: meTheme.iconColor),
    ),
    progressIndicatorTheme: ProgressIndicatorThemeData(
      color: meTheme.themeColor,
      refreshBackgroundColor: meTheme.cardColor,
    ),
    scaffoldBackgroundColor: meTheme.backgroundColor,
    sliderTheme: SliderThemeData.fromPrimaryColors(
      primaryColor: meTheme.themeColor,
      primaryColorDark: meTheme.themeColor,
      primaryColorLight: meTheme.themeColor,
      valueIndicatorTextStyle: effectiveTextTheme.bodyLarge!,
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.fromMap({
        WidgetState.selected: meTheme.themeColor,
      }),
      trackColor: WidgetStateProperty.fromMap({
        WidgetState.selected: meTheme.themeColor.withValues(alpha: 0.3),
      }),
    ),
    textSelectionTheme: TextSelectionThemeData(
      cursorColor: meTheme.themeColor,
      selectionColor: meTheme.themeColor.withValues(alpha: .25),
      selectionHandleColor: meTheme.themeColor,
    ),
    tooltipTheme: TooltipThemeData(
      decoration: BoxDecoration(
        borderRadius: meTheme.borderRadius,
        color: meTheme.cardColor,
      ),
      textStyle: effectiveTextTheme.bodyMedium,
    ),
  );
  return theme;
}

TextTheme textThemeBy({
  required METheme meTheme,
  required Locale locale,
}) {
  final bool isCJK = locale.languageCode == 'zh' ||
      locale.languageCode == 'ja' ||
      locale.languageCode == 'kr';
  final TextStyle baseStyle = TextStyle(
    fontFamily: !isCJK ? meTheme.fontFamilyLatin : null,
    fontFamilyFallback: <String>[
      ...meTheme.fontFamilyExtraFallbacks,
      'PingFang SC',
      'Heiti SC',
    ],
    height: isCJK ? meTheme.fontHeightCJK : meTheme.fontHeightLatin,
    leadingDistribution: TextLeadingDistribution.even,
    textBaseline: TextBaseline.ideographic,
  );
  final TextStyle primaryTextStyle = baseStyle.copyWith(
    color: meTheme.primaryTextColor,
  );
  final TextStyle captionTextStyle = baseStyle.copyWith(
    color: meTheme.captionTextColor,
  );
  final Typography typography = Typography.material2014(
    platform: defaultTargetPlatform,
  );
  final TextTheme baseTextTheme = meTheme.brightness == Brightness.dark
      ? typography.white
      : typography.black;
  return baseTextTheme.merge(
    TextTheme(
      displayLarge: captionTextStyle.copyWith(fontWeight: FontWeight.normal),
      displayMedium: captionTextStyle.copyWith(fontWeight: FontWeight.normal),
      displaySmall: captionTextStyle.copyWith(fontWeight: FontWeight.normal),
      headlineLarge: primaryTextStyle.copyWith(
        fontSize: textSize28,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: primaryTextStyle.copyWith(
        fontSize: textSize24,
        fontWeight: FontWeight.bold,
      ),
      headlineSmall: primaryTextStyle.copyWith(
        fontSize: textSize20,
        fontWeight: FontWeight.bold,
      ),
      titleLarge: primaryTextStyle.copyWith(fontWeight: FontWeight.bold),
      titleMedium: primaryTextStyle.copyWith(fontWeight: FontWeight.normal),
      titleSmall: primaryTextStyle.copyWith(fontWeight: FontWeight.bold),
      bodyLarge: primaryTextStyle.copyWith(fontWeight: FontWeight.normal),
      bodyMedium: primaryTextStyle.copyWith(fontWeight: FontWeight.normal),
      bodySmall: captionTextStyle.copyWith(fontWeight: FontWeight.normal),
      labelLarge: primaryTextStyle.copyWith(fontWeight: FontWeight.bold),
      labelMedium: primaryTextStyle.copyWith(fontWeight: FontWeight.normal),
      labelSmall: primaryTextStyle.copyWith(fontWeight: FontWeight.normal),
    ),
  );
}

final bottomBarPadding = EdgeInsetsDirectional.only(
  start: 24.0,
  end: 24.0,
  bottom: 24.0 + Screens.bottomSafeHeight,
  top: 8.0,
);

BoxDecoration bottomBarDecoration(
  BuildContext context, {
  Color? backgroundColor,
}) {
  backgroundColor ??= Theme.of(context).colorScheme.surface;
  return BoxDecoration(
    color: backgroundColor,
    boxShadow: <BoxShadow>[
      if (backgroundColor.a > 0.0)
        BoxShadow(
          color: backgroundColor,
          blurRadius: 10.0,
          spreadRadius: 10.0,
          offset: const Offset(0.0, -10.0),
        ),
    ],
  );
}
