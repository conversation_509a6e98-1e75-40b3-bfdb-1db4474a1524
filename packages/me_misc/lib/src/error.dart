// Copyright 2023 The AstroX Authors. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.
import 'package:me_l10n/me_l10n.dart';

import 'me.dart';

enum MEErrorType { error, failure }

class MEError extends Error {
  MEError(
    this.code, {
    this.type = MEErrorType.error,
    this.action,
    int? extraCode,
    Object? error,
  })  : _extraCode = extraCode ??
            (type == MEErrorType.failure ? MEError._extraFailed : null),
        _error = error;

  final int code;
  final MEErrorType type;
  final String? action;
  final int? _extraCode;
  final Object? _error;

  String toStringShort() {
    final MELocalizations? localizations = MELocalizations.of(meContext);
    assert(
      localizations != null,
      'Cannot throw exceptions when no localizations found.',
    );
    final MELocalizations l = localizations!;
    final StringBuffer sb = StringBuffer();
    if (action != null) {
      sb.write(action);
    }
    if (type == MEErrorType.error) {
      sb.write(l.exceptionError);
    } else {
      sb.write(l.exceptionFailed);
    }
    sb.write(' ');
    sb.write('($code');
    if (_extraCode != null) {
      sb.write(':$_extraCode');
    }
    sb.write(')');
    return sb.toString();
  }

  @override
  String toString() {
    final StringBuffer sb = StringBuffer(toStringShort());
    if (_error != null) {
      sb.write('\n$_error');
    }
    return sb.toString();
  }

  /// Define error codes with specified businesses.
  ///
  /// Category name should follow Candid definitions as much as possible.
  ///
  /// Codes should have a maximum category length and a fixed code length: 4.
  /// A valid pattern should be:
  /// ```dart
  /// static const int walletIIAnchorExist = 4001;
  /// ```
  /// Which indicates:
  /// | Category name | Category code | Business code  |
  /// |---------------|---------------|----------------|
  /// | NNS           | 4             | 0001           |
  static const int _extraFailed = -1;
}
