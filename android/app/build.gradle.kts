import java.util.Base64
import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")

    // NOTE: Required by firebase_analytics
    // START: FlutterFire Configuration
    id("com.google.gms.google-services")
    id("com.google.firebase.crashlytics")
    // END: FlutterFire Configuration

    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

val keystoreProperties = Properties()
val keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(FileInputStream(keystorePropertiesFile))
}

val dartDefines = mutableMapOf(
    "mePlatformArchitecture" to "arm64-v8a"
)
if (project.hasProperty("dart-defines")) {
    val dartDefinesProperty = project.property("dart-defines") as String
    val additionalDefines = dartDefinesProperty
        .split(",")
        .associate { entry ->
            val decoded = String(Base64.getDecoder().decode(entry), Charsets.UTF_8)
            val pair = decoded.split("=")
            pair[0] to pair[1]
        }
    dartDefines.putAll(additionalDefines)
}

android {
    namespace = "fun.card3"
    compileSdk = 36
    ndkVersion = "27.0.12077973"

    defaultConfig {
        applicationId = applicationId
        minSdk = 27
        targetSdk = 36
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    compileOptions {
        // Flag to enable support for the new language APIs
        isCoreLibraryDesugaringEnabled = true
        // Sets Java compatibility to Java 17
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    packaging {
        dex {
            useLegacyPackaging = true
        }
        jniLibs {
            useLegacyPackaging = true
        }
    }

    signingConfigs {
        create("forAll") {
            keyAlias = keystoreProperties["keyAlias"] as String?
            keyPassword = keystoreProperties["keyPassword"] as String?
            storeFile =
                if (keystoreProperties["storeFile"] != null) file(keystoreProperties["storeFile"] as String) else null
            storePassword = keystoreProperties["storePassword"] as String?
            enableV1Signing = true
            enableV2Signing = true
            enableV3Signing = true
            enableV4Signing = true
        }
    }

    flavorDimensions += "default"
    productFlavors {
        create("app") {
            dimension = "default"
            applicationId = "fun.card3"
            manifestPlaceholders += mapOf(
                "applicationLabel" to "Card3",
                "LinkAppHost" to "app.card3.co",
                "LinkMainHost" to "card3.fun",
                "LinkShortHost" to "card3.co",
                "LinkSocialHost" to "social.card3.co",
                "LinkWebsiteHost" to "card3.ai"
            )
        }
        create("beta") {
            dimension = "default"
            applicationId = "fun.card3.beta"
            manifestPlaceholders += mapOf(
                "applicationLabel" to "Card3 (Beta)",
                "LinkAppHost" to "app.card3.co",
                "LinkMainHost" to "test-v.card3.fun",
                "LinkShortHost" to "t.card3.co",
                "LinkSocialHost" to "test-social.card3.co",
                "LinkWebsiteHost" to "test.card3.ai"
            )
        }
    }

    buildTypes {
        getByName("debug") {
            signingConfig = signingConfigs.getByName("forAll")
            ndk {
                abiFilters.clear()
                abiFilters += listOf("arm64-v8a", "x86_64")
            }
            manifestPlaceholders += mapOf(
                "EnableImpeller" to "true",
                "DisableSurfaceControl" to "false"
            )
            isMinifyEnabled = false
            isShrinkResources = false
        }
        getByName("profile") {
            signingConfig = signingConfigs.getByName("forAll")
            ndk {
                abiFilters.clear()
                abiFilters += listOf("arm64-v8a", "x86_64")
            }
            manifestPlaceholders += mapOf(
                "EnableImpeller" to "true",
                "DisableSurfaceControl" to "false"
            )
        }
        getByName("release") {
            signingConfig = signingConfigs.getByName("forAll")
            ndk {
                abiFilters.clear()
                abiFilters += listOf(dartDefines["mePlatformArchitecture"] as String)
            }
            manifestPlaceholders += mapOf(
                "EnableImpeller" to "false",
                "DisableSurfaceControl" to "true"
            )
            isShrinkResources = true
            isMinifyEnabled = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation("androidx.core:core-splashscreen:1.2.0-rc01")
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.1.5")
}
