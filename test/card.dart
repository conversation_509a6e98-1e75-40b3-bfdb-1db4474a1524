import 'package:card3/feat/card/helper.dart' show <PERSON><PERSON><PERSON><PERSON>;
import 'package:flutter_test/flutter_test.dart';

void main() {
  group(
    'CardHelper',
    () {
      test('formalizeUrl', () {
        expect(
          CardHelper.formalizeUrl('card3:///profile?card_code=ABCDEFG'),
          Uri.parse('card3:///profile?card_code=ABCDEFG'),
        );
        expect(
          CardHelper.formalizeUrl('card3.co/?uid=123321'),
          Uri.parse('https://card3.co/?uid=123321'),
        );
        expect(
          CardHelper.formalizeUrl('https%3A%2F%2Fcard3.co%2F%3Fuid%3D123321'),
          Uri.parse('https://card3.co/?uid=123321'),
        );
        expect(
          CardHelper.formalizeUrl('uri:%20card3.co/?uid=041F6EEAD11E90&ctr=000091&cmac=BE4BE7410C48CC32%0A'),
          Uri.parse('https://card3.co/?uid=041F6EEAD11E90&ctr=000091&cmac=BE4BE7410C48CC32'),
        );
      });
    },
  );
}
